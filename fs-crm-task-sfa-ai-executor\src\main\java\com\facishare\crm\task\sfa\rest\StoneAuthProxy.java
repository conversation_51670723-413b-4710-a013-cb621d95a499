package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.task.sfa.rest.dto.StoneAuthModels;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

@RestResource(value = "STONE_AUTH_SERVICE", desc = "Stone Auth API", contentType = "application/json")
public interface StoneAuthProxy {

    @POST(value = "/fs-stone-auth/iam/resource/accredit/file/noSignAcUrl", desc = "获取无签名访问URL")
    StoneAuthModels.NoSignAcUrlResponse getNoSignAcUrl(@Body StoneAuthModels.NoSignAcUrlRequest request);

    @GET(value = "/fs-stone-auth/iam/info/getTingWuCredential?ea={ea}", desc = "获取听悟凭证")
    StoneAuthModels.GetTingWuCredentialResponse getTingWuCredential(@HeaderMap Map<String, String> headers, @PathParams Map<String, String> pathParams);


    @GET(value = "/fs-big-file-manager-biz/api/generateDownloadSignatureUrlByAuth?employeeAccount={employeeAccount}&employeeId={employeeId}&path={path}&expireTime={expireTime}", desc = "获取文件下载签名URL")
    StoneAuthModels.GenerateDownloadUrlResponse generateDownloadUrl(@HeaderMap Map<String, String> headers, @PathParams Map<String, String> pathParams);
} 