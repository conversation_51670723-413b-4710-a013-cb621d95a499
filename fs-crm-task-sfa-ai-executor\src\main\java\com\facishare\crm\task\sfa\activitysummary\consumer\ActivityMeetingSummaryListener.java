package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityMeetingSummaryServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Slf4j
@Component
public class ActivityMeetingSummaryListener extends AbstractActivityCommonListener {
    @Autowired
    private ActivityMeetingSummaryServiceImpl activityMeetingSummaryService;

    @Override
    String getSection() {
        return "sfa-ai-activity-meeting-summary-consumer";
    }

    @Override
    void consume(ActivityMessage activityMessage) {
        String stage = activityMessage.getStage();
        if (ObjectUtils.isEmpty(stage)) {
            return;
        }
        log.info("ActivityMeetingSummaryListener activityMessage:{}", JSONObject.toJSONString(activityMessage));
        switch (stage) {
            case "realtime2text":  // 实时音频转文字
                break;
            case "Add": // 新增
            case "file2text":  // 附件转文本
            case "AddNoAttachment":  // 无附件新增
            case "realtime2textDone":// 实时音频转文字(全部结束时)
                activityMeetingSummaryService.consume(activityMessage);
                break;
            default:
                break;
        }
    }
}

/**
 * 上线需要做的事情：
 * describe-short-name-config
 * "ActivityMeetingSummaryObj": "ams",
 * ================================================================================
 * variables_sfa
 * service_module_apibus_sfa增加activity_meeting_summary
 * service_module_cep_sfa增加activity_meeting_summary
 * ================================================================================
 * topic: sfa-ai-activity
 * 申请一个consumer
 * 消费组：
 * activity-meeting-summary-local（本地）
 * activity-meeting-summary
 * ================================================================================
 * fs-crm-task-sfa-mq.ini
 * 增加配置：sfa-ai-activity-meeting-summary-consumer
 * ================================================================================
 * fs-crm-sales-config
 * 增加配置：qwen_max_text_max_length
 * 增加配置：activity_meeting_summary_llm_model
 * 增加配置：activity_meeting_summary_source_text_field_name
 * ================================================================================
 * fs-webpage-component-name-config
 * fs-webpage-customer-widgets-collection
 * fs-webpage-customer-web-object-detail-component-list
 * fs-webpage-customer-widget
 * <p>
 * 以上配置文件都需要修改，参考线下的配置：activity_meeting_summary
 */
