package com.facishare.crm.task.sfa.rest;


import com.facishare.ai.api.dto.PromptCompletions;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

@RestResource(value = "AI_REST_PROXY_NEW", desc = "Ai内部调用", contentType = "application/json")
public interface AiRestProxy {
    @POST(value = "/v1/prompt/completions", desc = "completions 通过提示词调用大模型")
    AiRestProxyModel.Resposne completions(@Body AiRestProxyModel.Arg arg, @HeaderMap Map<String, String> headerMap);

    @POST(value = "v1/inner/prompt/preView", desc = "查询提示词子模板内容")
    AiRestProxyModel.PromptPreViewResponse promptPreView(@Body AiRestProxyModel.PromptPreViewArg arg, @HeaderMap Map<String, String> headerMap);


    @POST(value = "v1/rag/fastQuery", desc = "completions fastQuery 只向量数据搜索")
    AiRestProxyModel.FastQueryResposne fastQuery(@Body AiRestProxyModel.FastQueryArg arg, @HeaderMap Map<String, String> headerMap);

    @POST(value = "v1/inner/rag/create", desc = "create 只向量数据搜索")
    AiRestProxyModel.RagResposne create(@Body AiRestProxyModel.CreateRagArg arg, @HeaderMap Map<String, String> headerMap);


    /**
     *
     * @param arg 平台提供的参数 model
     * @param headerMap
     * @return
     */
    @POST(value = "/v1/prompt/completions", desc = "completions 通过提示词调用大模型")
    AiRestProxyModel.Resposne completions(@Body PromptCompletions.Arg arg, @HeaderMap Map<String, String> headerMap);


    static Map<String, String> getHeaders(String tenantId,String userId) {
        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", tenantId);
        headers.put("x-fs-userInfo", ObjectUtils.isEmpty(userId) ? User.SUPPER_ADMIN_USER_ID : userId);
        headers.put("Content-Type", "Content-Type");
        return headers;
    }


}
