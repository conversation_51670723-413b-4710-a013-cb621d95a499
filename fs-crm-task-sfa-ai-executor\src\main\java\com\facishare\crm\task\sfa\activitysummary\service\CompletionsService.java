package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.task.sfa.activitysummary.model.CorpusType;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.rest.AiRestProxy;
import com.facishare.paas.appframework.core.model.User;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class CompletionsService {
    @Autowired
    private AiRestProxy aiRestProxy;
    @Autowired
    private FixJSONFormatService fixJSONFormatService;
    @Autowired
    private ActivitySummaryService activitySummaryService;
    @Resource
    private ActivityMongoDao activityMongoDao;

    public static Map<String, Object> CORPUS_TYPE_OF_PROPS= new HashMap<>();

    public static Integer maxNum = 0;
    public static Integer MAX_TOKENS = 0;

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            String str = config.get("corpus_type_of_props", "");
            if(ObjectUtils.isNotEmpty(str)){
                CORPUS_TYPE_OF_PROPS = JSONObject.parseObject(str,Map.class);

            }
            maxNum = config.getInt("max_num", 1000);
            MAX_TOKENS =  config.getInt("ai_return_max_tokens", 4096);
        });
    }

    public String requestCompletion(User user, AiRestProxyModel.Arg arg) {
        arg.setSupportAdvanced(true);
        arg.setMaxTokens(MAX_TOKENS);
        if (ObjectUtils.isEmpty(user.getUserId()) || "-10000".equals(user.getUserId())){
            log.info("requestCompletion userId is system");
        }
        AiRestProxyModel.Resposne completions = new AiRestProxyModel.Resposne();
        try {
            completions = aiRestProxy.completions(arg, AiRestProxy.getHeaders(user.getTenantId(), user.getUserId()));
        }catch (Exception e){
            log.error("requestCompletion exception e: ",e);
        }
        if (completions != null && completions.getErrCode() != 0) {
            log.error("requestCompletion error, code:{}, message:{}", completions.getErrCode(), completions.getErrMessage());
            return "";
        }
        return Optional.ofNullable(completions)
                .map(AiRestProxyModel.Resposne::getResult)
                .map(AiRestProxyModel.Result::getMessage)
                .orElse("");
    }


    public <T> List<T> requestCompletionList(User user, AiRestProxyModel.Arg arg, String jsonFormat, Class<T> tClass) {
        String result = requestCompletion(user, arg);
        if (StringUtils.isBlank(result)) {
            return Lists.newArrayList();
        }
        return fixJSONFormatService.getDataListFixedInvalidJSON(user, jsonFormat, result, tClass, false);
    }

    public <T> T requestCompletionData(User user, AiRestProxyModel.Arg arg, String jsonFormat, Class<T> tClass) {
        String result = requestCompletion(user, arg);
        if (StringUtils.isBlank(result)) {
            return null;
        }
        return fixJSONFormatService.getDataFixedInvalidJSON(user, jsonFormat, result, tClass);
    }

    public <T> List<T> requestCompletionListRetainNewline(User user, AiRestProxyModel.Arg arg, String jsonFormat, Class<T> tClass) {
        String result = requestCompletion(user, arg);
        if (StringUtils.isBlank(result)) {
            return Lists.newArrayList();
        }
        return fixJSONFormatService.getDataListFixedInvalidJSON(user, jsonFormat, result, tClass, true);
    }

    /**
     *  获取语料的类型以及提问词，bindDataId和corpus参数2选一，优先bindDataId
     * @param user ei
     * @param componentApiName 组件的唯一标识
     * @param bindDataId       绑定数据的id
     * @param corpus           手动输入的语料
     * @return
     */
    public CorpusType.Props getCorpusTypeANdProps(User user,String componentApiName,String bindDataId,String corpus){
        if(ObjectUtils.isEmpty(bindDataId) && ObjectUtils.isEmpty(corpus) ){
            log.warn("getCorpusTypeANdProps bindDataId && corpus is null");
            return null;
        }
        if(ObjectUtils.isEmpty(CORPUS_TYPE_OF_PROPS)){
            log.warn("getCorpusTypeANdProps CORPUS_TYPE_OF_PROPS is null");
            return null;
        }
        if(!CORPUS_TYPE_OF_PROPS.containsKey(componentApiName)){
            log.warn("getCorpusTypeANdProps CORPUS_TYPE_OF_PROPS is not containsKey componentApiName:{}",componentApiName);
            return null;
        }
        List<CorpusType.Props> propsList = JSONObject.parseArray(CORPUS_TYPE_OF_PROPS.get(componentApiName).toString(),CorpusType.Props.class);
        if(CollectionUtil.isEmpty(propsList)){
            return null;
        }
        if(propsList.size()==1){
            return propsList.get(0);
        }
        List<Map<String, Object>> list = new ArrayList<>();
        propsList.stream().forEach(x->{
            Map<String, Object> corpusTypeMap = new HashMap<>();
            corpusTypeMap.put("type",x.getCorpusType());
            corpusTypeMap.put("name",x.getCorpusTypeName());
            list.add(corpusTypeMap);
        });
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put("corpusType",JSONObject.toJSONString(list));
        String propsApiName = "handle_type_corpus_find_by_id";
        if(ObjectUtils.isEmpty(bindDataId)){
            sceneParamMap.put("corpus",corpus);
            sceneParamMap.put("corpusType",JSONObject.toJSONString(list));
            propsApiName = "handle_type_corpus_by_param";
        }
        String result =  activitySummaryService.getAiComplete(user,propsApiName,sceneParamMap,bindDataId);
        result = activitySummaryService.captureAiResult(result);
        Map<String, Object>  resultMap = JSONObject.parseObject(result, Map.class);
        CorpusType.Props props = propsList.stream().filter(x->x.getCorpusType().equals(resultMap.get("type").toString())).findFirst().get();
        if(ObjectUtils.isEmpty(props)){
            log.warn("getCorpusTypeANdProps props is not null  resultMap:{}",JSONObject.toJSONString(resultMap));
            return null;
        }
        return props;
    }


    /**
     * 获取mongo所有的录音转换为字符串输出
     * @param tenantId
     * @param activeRecordId
     * @param language
     * @return
     */
    public String getCorpusStrWithSpeakerByMax(String tenantId,String activeRecordId,String language){
        List<InteractiveDocument> interactiveList = getCorpusStrWithSpeakerByMax(tenantId,activeRecordId);
        if(CollectionUtil.isEmpty(interactiveList)){
            return "";
        }
        return activityMongoDao.montageMongoContent(interactiveList,false,language);
    }

    /**
     * 获取所有的mongo的录音信息
     * @param tenantId
     * @param activeRecordId
     * @return
     */
    public List<InteractiveDocument> getCorpusStrWithSpeakerByMax(String tenantId,String activeRecordId){
        List<InteractiveDocument> interactiveList = activityMongoDao.queryListByActiveRecordId(tenantId,activeRecordId,0,maxNum);
        if(CollectionUtil.isEmpty(interactiveList)){
            return null;
        }
        return interactiveList;
    }


}
