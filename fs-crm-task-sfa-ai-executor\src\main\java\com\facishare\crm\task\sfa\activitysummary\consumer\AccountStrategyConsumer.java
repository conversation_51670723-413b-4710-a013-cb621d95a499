package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.AccountStrategyService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.UUID;

@Component
@Slf4j
public class AccountStrategyConsumer implements ApplicationListener<ContextRefreshedEvent> {
    private AutoConfMQPushConsumer consumer;

    @Autowired
    private AccountStrategyService accountStrategyService;
    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "sfa-ai-interaction-strategy-object", (MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                try {
                    consumeResponse(msg);
                } catch (Exception e) {
                    log.error("AccountStrategyConsumer :{}", msg, e);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    private void consumeResponse(MessageExt message) {
        byte[] body = message.getBody();
        ActivityMessage activityMessage = JSON.parseObject(body, ActivityMessage.class);
        TraceContext.get().setTraceId(UUID.randomUUID()+"-"+activityMessage.getObjectId());
        log.info("activityMessage:{}, msgId: {}", activityMessage, message.getMsgId());
        consume(activityMessage);
    }

    private void consume(ActivityMessage activityMessage) {
        accountStrategyService.consume(activityMessage);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    @PreDestroy
    public void close() {
        consumer.close();
    }
}
