package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.sfa.lto.activity.enums.AttendeesInsightType;
import com.facishare.crm.task.sfa.activitysummary.constant.AttendeesInsightConstants;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel.Insight;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MeetingSummaryInsightHandler extends AbstractInsightHandler<AttendeesInsightModel.InsightResult> {

    @Override
    public String getInsightType() {
        return AttendeesInsightType.MEETING_SUMMARY;
    }


    @Override
    public void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {

        String tenantId = attendeesInsightMessage.getTenantId();
        String activeRecordId = attendeesInsightMessage.getActiveRecordId();

        AttendeesInsightModel.AttendeesInsightExtendData extendData = attendeesInsightMessage.getExtendData();


        List<IObjectData> questionList = extendData.getQuestionList();
        if (Safes.isEmpty(questionList)) {
            log.warn("No question list found for activeRecordId: {}", activeRecordId);
            return;
        }
        List<IObjectData> activityUserList = extendData.getActivityUserList();
        String activityUserId = attendeesInsightMessage.getActivityUserId();
        if (Safes.isNotEmpty(activityUserId)) {
            activityUserList = activityUserList.stream().filter(d -> d.getId().equals(activityUserId)).collect(Collectors.toList());
        }

        // 将问题列表按 activity_user_id 分组
        Map<String, List<IObjectData>> questionGroups = activityUserList.stream()
                .filter(u -> "their_side".equals(u.get("participant_types", String.class)))
                .collect(Collectors.toMap(IObjectData::getId, Lists::newArrayList));
        for (IObjectData question : questionList) {
            List<String> questionUser = question.get("question_user", List.class);
            for (String userId : Safes.of(questionUser)) {
                List<IObjectData> questions = questionGroups.get(userId);
                if (questions != null) {
                    questions.add(question);
                }
            }
        }

        List<IObjectData> recordList = new ArrayList<>(questionGroups.size());
        questionGroups.forEach((userId, questions) -> {
            if (questions.isEmpty()) {
                return;
            }
            IObjectData insightRecord = super.buildBaseInsightRecord(tenantId, activeRecordId);
            List<Insight> insightList = Lists.newArrayList();
            questions = questions.stream().limit(20).collect(Collectors.toList());
            for (IObjectData question : questions) {
                String questionContent = question.get("question_content__o", String.class);
                String questionSeq = question.get("question_seq", String.class);
                List<String> seqList = Safes.isEmpty(questionSeq) ? null : Lists.newArrayList(questionSeq);
                if (Safes.isNotEmpty(questionContent)) {
                    insightList.add(new Insight(questionContent, seqList, null, null));
                }
            }
            if (!insightList.isEmpty()) {
                insightRecord.set(AttendeesInsightConstants.ACTIVITY_USER_ID, userId);
                insightRecord.set(AttendeesInsightConstants.INSIGHT_RESULT, JSON.toJSONString(insightList));
                recordList.add(insightRecord);
            }
        });

        if (!recordList.isEmpty()) {
            serviceFacade.bulkSaveObjectData(recordList, User.systemUser(tenantId));
        }
    }

    @Override
    protected String getUserNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        return super.getTheirSideNames(insightMessage);
    }





}
