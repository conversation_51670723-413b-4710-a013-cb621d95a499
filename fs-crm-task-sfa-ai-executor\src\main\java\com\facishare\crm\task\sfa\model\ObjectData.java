package com.facishare.crm.task.sfa.model;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020/5/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ObjectData {
    private String op;
    private String tenantId;
    private String name;
    private List<ObjectChange> body;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ObjectChange {
        public static final String TRIGGER_TYPE_INSERT = "i";
        public static final String TRIGGER_TYPE_UPDATE = "u";
        public static final String TRIGGER_TYPE_INVALID = "invalid";
        public static final String TRIGGER_TYPE_RECOVER = "recover";
        public static final String TRIGGER_TYPE_DELETE = "d";
        public static final String TRIGGER_TYPE_MQ = "mq";
        private String eventId;
        private Context context;
        private String triggerType;
        private String entityId;
        private String objectId;
        private boolean batch;
        private JSONObject beforeTriggerData;
        private JSONObject afterTriggerData;

        public boolean isDeleted() {
            if (null != afterTriggerData && null != afterTriggerData.getInteger("is_deleted")) {
                int isDeleted = afterTriggerData.getInteger("is_deleted");
                if (isDeleted != 0) {
                    return true;
                }
            }
            return false;
        }

        @Data
        public static class Context {
            private String appId;
            private String tenantId;
            private String userId;

            public Context copy() {
                Context context = new Context();
                context.setAppId(appId);
                context.setUserId(userId);
                context.setTenantId(tenantId);
                return context;
            }
        }

        public boolean isChanged(String... fieldNames) {
            if (TRIGGER_TYPE_UPDATE.equals(triggerType)) {
                for (String fieldName : fieldNames) {
                    Object b = null;
                    Object a = null;
                    if (beforeTriggerData != null) {
                        b = beforeTriggerData.get(fieldName);
                    }
                    if (afterTriggerData != null) {
                        a = afterTriggerData.get(fieldName);
                    }
                    if (!Objects.equals(a, b)) {
                        return true;
                    }
                }
            }
            return false;
        }

        public Set<String> changedFields() {
            Set<String> result = Sets.newHashSet();
            if (!TRIGGER_TYPE_UPDATE.equals(triggerType)) {
                return result;
            }
            if (null != beforeTriggerData) {
                result.addAll(beforeTriggerData.keySet());
            }
            if (null != afterTriggerData) {
                result.addAll(afterTriggerData.keySet());
            }
            return result;
        }
    }
}
