package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 对象特征关联常量
 *
 * <AUTHOR>
 */
public interface ObjectFeatureConstants {
	/**
     * 对象
     */
	String OBJECT_API_NAME = "object_api_name";
	/**
     * 特征
     */
	String FEATURE_ID = "feature_id";
	/**
     * 权重
     */
	String WEIGHT = "weight";
	/**
     * 是否必做
     */
	String MUST_DO = "must_do";
	/**
     * 评分规则
     */
	String SCORING_RULE_ID = "scoring_rule_id";
	/**
     * 状态
     */
	String STATUS = "status";
	enum StatusType {
		/**
         * 禁用
         */
		DISABLE("0") ,
		/**
         * 启用
         */
		ENABLE("1") ;
		private final String status;

		public String getStatusType() {
            return status;
        }


		StatusType(String status) {
            this.status = status;
        }
	}
	/**
     * 负责人所在部门
     */
	String OWNER_DEPARTMENT = "owner_department";
	/**
     * 相关团队
     */
	String RELEVANT_TEAM = "relevant_team";

	/**
	 * 系统数据类型
	 */
	String SYSTEM_TYPE = "system_type";

	enum SystemType {
		/**
		 * 系统
		 */
		SYSTEM("system"),
		/**
		 * 自定义
		 */
		UDEF("udef");

		private final String systemType;

		public String getSystemType() {
			return systemType;
		}

		SystemType(String systemType) {
			this.systemType = systemType;
		}
	}
}