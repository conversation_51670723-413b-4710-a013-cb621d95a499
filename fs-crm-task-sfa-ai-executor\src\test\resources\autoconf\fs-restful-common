{"STONE_META_SERVER": {"address": "http://**************:32025/fs-stone-metaserver/api/", "readTimeout": "10", "connectTimeout": "10", "writeTimeout": "10", "maxRequests": "50", "maxRequestsPerHost": "50", "keepAliveDuration": "5", "serializableConfigName": "", "connectionTimeOut": "2000", "adapterFRestServer": "true", "maxActive": "10"}, "STONE_FILETOKEN": {"address": "http://************:8034/fs-stone-filetoken-util/api/", "keepAliveDuration": 5}, "METADATA_OPTION": {"address": "http://oss.firstshare.cn/metadata-option/", "connectionTimeOut": "2000", "keepAliveDuration": "5", "serializableConfigName": "", "adapterFRestServer": "false", "maxActive": "10"}, "STONE_AUDIO_SERVER": {"address": "http://**************:32057/fs-stone-audioserver/api/", "connectionTimeOut": "2000", "keepAliveDuration": "5", "maxActive": "10"}, "STONE_FILE_SERVER": {"address": "http://**************:32019/fs-stone-fileserver/api/", "readTimeout": "10", "connectTimeout": "10", "writeTimeout": "10", "maxRequests": "50", "maxRequestsPerHost": "50", "keepAliveDuration": "6", "serializableConfigName": "", "connectionTimeOut": "2000", "adapterFRestServer": "true", "maxActive": "10"}, "STONE_EX_ACCOUNT_SYNC": {"address": "http://oss.firstshare.cn/fs-stone-exaccountserver/api/", "keepAliveDuration": 5}, "STONE_PROXY_SERVER": {"address": "http://intranet.ceshi113.com/fs-stone-proxy/api/", "readTimeout": "10", "connectTimeout": "10", "writeTimeout": "10", "maxRequests": "50", "maxRequestsPerHost": "50", "keepAliveDuration": "5", "serializableConfigName": "", "connectionTimeOut": "2000", "adapterFRestServer": "true", "maxActive": "10"}, "STONE_DATA_SERVER": {"address": "http://**************:32017/fs-stone-dataserver/api/", "connectionTimeOut": "2000", "keepAliveDuration": "5", "maxActive": "10"}, "JOB_ADDRESS": {"address": "http://localhost:8081/v1/api/job/", "readTimeout": "10", "connectTimeout": "10", "writeTimeout": "10", "maxRequests": "50", "maxRequestsPerHost": "50", "keepAliveDuration": "5", "serializableConfigName": "common-group-service", "adapterFRestServer": "false"}}