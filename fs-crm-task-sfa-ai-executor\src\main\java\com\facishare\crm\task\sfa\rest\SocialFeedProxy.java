package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.task.sfa.model.FeedResult;
import com.facishare.crm.task.sfa.model.SearchFeedModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/11 12:17
 * @description:
 */
@RestResource(value = "FeedServiceProxy", desc = "社交动态", contentType = "application/json")
public interface SocialFeedProxy {

    //@POST(value = "/FeedParticular/searchFeedListParticular", desc = "")
    //ParticularResult searchFeedListParticular(@Body FeedParticularVO feedParticularVO, @HeaderMap Map<String, String> headers);

    @POST(value = "/Feeds/searchFeedResourceList", desc = "")
    FeedResult.Result searchFeedResourceList(@Body SearchFeedModel.Arg arg, @HeaderMap Map<String, String> headers);
}
