package com.facishare.crm.task.sfa.activitysummary.service.tag;

import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphDocument;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphMongoDao;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphContext;
import com.facishare.crm.task.sfa.activitysummary.service.ActiveRecordDataService;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.DocumentProcessService;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 段落内容处理服务
 * 负责段落内容的获取和处理
 */
@Service
@Slf4j
public class ParagraphContentService {

    @Resource
    private ParagraphMongoDao paragraphMongoDao;

    @Resource
    private DocumentProcessService documentProcessService;

    @Resource
    private ActivityMongoDao activityMongoDao;

    @Resource
    private ActiveRecordDataService activeRecordDataService;

    /**
     * 查询段落文档
     *
     * @param tenantId     租户ID
     * @param paragraphIds 段落ID列表
     * @return 段落文档列表
     */
    public List<ParagraphDocument> queryParagraphDocuments(String tenantId, List<String> paragraphIds) {
        try {
            return paragraphMongoDao.queryByIds(tenantId, paragraphIds);
        } catch (Exception e) {
            log.error("Failed to query paragraph documents", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取段落内容
     *
     * @param user               用户信息
     * @param paragraphDocuments 段落文档列表
     * @param type               类型(mongo/text)
     * @return 段落ID到内容的映射
     */
    public Map<String, String> getParagraphContents(User user, List<ParagraphDocument> paragraphDocuments,
            String type) {
        Map<String, String> paragraphContentsMap = new HashMap<>();

        if (CollectionUtils.isEmpty(paragraphDocuments)) {
            return paragraphContentsMap;
        }

        if (ParagraphContext.MONGO_KEY.equals(type)) {
            return getMongoContents(user, paragraphDocuments);
        } else if (ParagraphContext.TEXT_KEY.equals(type)) {
            return getTextContents(user, paragraphDocuments);
        }

        return paragraphContentsMap;
    }

    /**
     * 获取MongoDB中的内容
     */
    private Map<String, String> getMongoContents(User user, List<ParagraphDocument> paragraphDocuments) {
        Map<String, String> paragraphContentsMap = new HashMap<>();

        // 1. 收集文档ID和建立映射关系,有序map
        Map<String, List<String>> documentLinkedMapping = collectDocumentIds(paragraphDocuments);
        List<String> allDocumentIds = documentLinkedMapping.values()
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(allDocumentIds)) {
            return paragraphContentsMap;
        }

        // 2. 批量查询文档
        Map<String, InteractiveDocument> docIdToDocument = queryInteractiveDocuments(user.getTenantId(),
                allDocumentIds);
        if (docIdToDocument.isEmpty()) {
            return paragraphContentsMap;
        }

        // 3. 构建段落内容
        return buildParagraphContents(documentLinkedMapping, docIdToDocument);
    }

    /**
     * 收集文档ID并建立段落ID到文档ID的映射关系
     */
    private Map<String, List<String>> collectDocumentIds(List<ParagraphDocument> paragraphDocuments) {
        Map<String, List<String>> paragraphToDocumentIds = Maps.newLinkedHashMap();

        for (ParagraphDocument paragraphDocument : paragraphDocuments) {
            String paragraphId = paragraphDocument.getId().toString();
            List<String> documentIdList = paragraphDocument.getDocumentIdList();

            if (CollectionUtils.isNotEmpty(documentIdList)) {
                paragraphToDocumentIds.put(paragraphId, documentIdList);
            }
        }

        return paragraphToDocumentIds;
    }

    /**
     * 批量查询互动文档
     */
    private Map<String, InteractiveDocument> queryInteractiveDocuments(String tenantId, List<String> documentIds) {
        List<InteractiveDocument> allInteractiveDocs = activityMongoDao.queryByIds(tenantId, documentIds);
        if (CollectionUtils.isNotEmpty(allInteractiveDocs)) {
            return allInteractiveDocs.stream()
                    .collect(Collectors.toMap(doc -> doc.getId().toString(), doc -> doc, (v1, v2) -> v2));
        }
        return Maps.newHashMap();
    }

    /**
     * 构建段落内容
     */
    private Map<String, String> buildParagraphContents(Map<String, List<String>> documentLinkedMapping,
            Map<String, InteractiveDocument> docIdToDocument) {
        Map<String, String> paragraphContentsLinkedMap = new HashMap<>();

        for (Map.Entry<String, List<String>> entry : documentLinkedMapping.entrySet()) {
            String paragraphId = entry.getKey();
            List<String> documentIds = entry.getValue();

            StringBuilder contentBuilder = new StringBuilder();
            for (String docId : documentIds) {
                InteractiveDocument doc = docIdToDocument.get(docId);
                if (doc != null) {
                    // todo user name需要查询来获取name
                    contentBuilder.append(doc.getUserName())
                            .append(": ")
                            .append(doc.getContent())
                            .append("\n");
                }
            }

            String content = contentBuilder.toString();
            if (StringUtils.isNotBlank(content)) {
                paragraphContentsLinkedMap.put(paragraphId, content);
            }
        }
        return paragraphContentsLinkedMap;
    }

    /**
     * 获取文本类型的内容（待实现）
     */
    private Map<String, String> getTextContents(User user, List<ParagraphDocument> paragraphDocuments) {
        log.warn("Text type content retrieval not implemented yet");
        ParagraphDocument paragraphDocument = paragraphDocuments.get(0);
        String objectId = paragraphDocument.getObjectId();
        IObjectData objectData = activeRecordDataService.queryActiveRecordDataWithFields(user, objectId, Lists.newArrayList(
                DBRecord.ID,
                IObjectData.NAME,
                CommonConstant.INTERACTIVE_CONTENT));
        if (objectData == null) {
            return Maps.newHashMap();
        }
        String interactiveContent = objectData.get(CommonConstant.INTERACTIVE_CONTENT, String.class);
        if (StringUtils.isBlank(interactiveContent)) {
            return Maps.newHashMap();
        }
        Map<String, String> paragraphContentsMap = Maps.newLinkedHashMap();
        // 遍历段落文档
        for (ParagraphDocument paragraphDoc : paragraphDocuments) {
            List<String> documentIdList = paragraphDoc.getDocumentIdList();
            if (CollectionUtils.isEmpty(documentIdList)) {
                continue;
            }

            // 将interactiveContent按行分割
            String[] contentLines = interactiveContent.split("\\r?\\n");
            
            StringBuilder contentBuilder = new StringBuilder();
            // 根据行号获取具体内容
            for (String lineNo : documentIdList) {
                try {
                    int index = Integer.parseInt(lineNo);
                    if (index >= 0 && index < contentLines.length) {
                        contentBuilder.append(contentLines[index]).append("\n");
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid line number: {}", lineNo);
                }
            }

            String content = contentBuilder.toString();
            if (StringUtils.isNotBlank(content) || StringUtils.isNotBlank(content.trim())) {
                paragraphContentsMap.put(paragraphDoc.getId().toString(), content.trim());
            }
        }

        return paragraphContentsMap;
    }

    /**
     * 将段落内容分片，按照每个段落内容长度约为指定长度进行划分
     *
     * @param paragraphContents 原始段落内容映射
     * @param chunkSize         每片内容的大致长度
     * @return 段落标签处理结果
     */
    public List<Map<String, String>> splitParagraphContents(Map<String, String> paragraphContents, int chunkSize) {
        List<Map<String, String>> chunks = new ArrayList<>();
        Map<String, String> currentChunk = new LinkedHashMap<>();
        int currentSize = 0;

        for (Map.Entry<String, String> entry : paragraphContents.entrySet()) {
            String paragraphId = entry.getKey();
            String content = entry.getValue();
            int contentLength = content.length();

            // 添加内容到当前分片
            currentChunk.put(paragraphId, content);
            currentSize += contentLength;

            // 如果当前分片大小超过限制，保存当前分片并创建新的空分片
            if (currentSize > chunkSize) {
                // 保存当前分片（包含刚添加的内容）
                chunks.add(currentChunk);
                
                // 清空重新开始
                currentChunk = new LinkedHashMap<>();
                currentSize = 0;
            }
        }

        // 添加最后一个分片
        if (!currentChunk.isEmpty()) {
            chunks.add(currentChunk);
        }

        // 计算并打印每个分片的长度
        String chunkSizes = chunks.stream()
                .map(chunk -> String.valueOf(
                    chunk.values().stream()
                        .mapToInt(String::length)
                        .sum()))
                .collect(Collectors.joining(","));
        
        log.info("Split paragraph contents into {} chunks, chunk sizes: {}", 
            chunks.size(), chunkSizes);

        return chunks;
    }
}