package com.facishare.crm.task.sfa.activitysummary.model.auditlog;

import com.facishare.crm.sfa.audit.log.EntityConverter;
import com.facishare.crm.sfa.audit.log.model.AuditArg;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;

public class ActivityMessageConverter implements EntityConverter<ActivityMessage> {

    @Override
    public AuditArg convert(ActivityMessage activityMessage) {
        return AuditArg.builder()
                .ei(activityMessage.getTenantId())
                .objectIds(activityMessage.getObjectId())
                .objectApiName(activityMessage.getObjectApiName())
                .extra1(activityMessage.getStage())
                .userId(activityMessage.getOpId())
                .build();
    }
}
