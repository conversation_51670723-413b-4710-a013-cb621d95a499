package com.facishare.crm.task.sfa.activitysummary.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;
import java.util.Stack;

/**
 * JSON 修复工具类
 * 基于 josdejong/jsonrepair 算法的 Java 实现
 * 支持修复各种常见的 JSON 格式问题
 */
@Slf4j
public class JsonRepairUtil {
    
    // 常用正则表达式模式
    private static final Pattern UNQUOTED_KEY_PATTERN = Pattern.compile("([{,]\\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:");
    private static final Pattern TRAILING_COMMA_PATTERN = Pattern.compile(",\\s*([}\\]])");
    private static final Pattern PYTHON_CONSTANTS_PATTERN = Pattern.compile("\\b(True|False|None)\\b");
    private static final Pattern COMMENTS_PATTERN = Pattern.compile("//.*?(?=\\n|$)|/\\*.*?\\*/");
    private static final Pattern MARKDOWN_JSON_PATTERN = Pattern.compile("```json\\s*([\\s\\S]*?)\\s*```");
    
    /**
     * 修复 JSON 字符串
     *
     * @param jsonStr 待修复的 JSON 字符串
     * @return 修复后的 JSON 字符串
     */
    public static String repair(String jsonStr) {
        if (StringUtils.isEmpty(jsonStr)) {
            return "{}";
        }

        try {
            String repaired = jsonStr.trim();

            // 阶段1: 预处理 - 清理格式和标记
            repaired = performPreprocessing(repaired);

            // 阶段2: 语法修复 - 修复基本语法问题
            repaired = performSyntaxRepair(repaired);

            // 阶段3: 结构修复 - 修复结构完整性
            repaired = performStructureRepair(repaired);

            // 阶段4: 后处理 - 清理和优化
            repaired = performPostprocessing(repaired);

            return repaired;

        } catch (Exception e) {
            log.warn("JSON 修复过程中发生错误: {}", e.getMessage());
            return handleFallback(jsonStr);
        }
    }

    /**
     * 预处理阶段 - 清理格式和标记
     */
    private static String performPreprocessing(String json) {
        // 移除 markdown 代码块标记
        json = removeMarkdownCodeBlocks(json);

        // 移除注释
        json = removeComments(json);

        return json;
    }

    /**
     * 语法修复阶段 - 修复基本语法问题
     */
    private static String performSyntaxRepair(String json) {
        // 修复 Python 常量
        json = fixPythonConstants(json);

        // 修复单引号
        json = fixSingleQuotes(json);

        // 修复未引用的键
        json = fixUnquotedKeys(json);

        // 移除尾随逗号
        json = removeTrailingCommas(json);

        // 修复字符串连接
        json = fixStringConcatenation(json);

        return json;
    }

    /**
     * 结构修复阶段 - 修复结构完整性
     */
    private static String performStructureRepair(String json) {
        // 修复转义字符
        json = fixEscapedStrings(json);

        // 修复不完整的结构
        json = fixIncompleteStructures(json);

        return json;
    }

    /**
     * 后处理阶段 - 清理和优化
     */
    private static String performPostprocessing(String json) {
        // 清理多余的空白字符
        json = cleanWhitespace(json);

        return json;
    }
    
    /**
     * 移除 markdown 代码块标记
     */
    private static String removeMarkdownCodeBlocks(String json) {
        java.util.regex.Matcher matcher = MARKDOWN_JSON_PATTERN.matcher(json);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        
        // 简单移除代码块标记
        return json.replaceAll("```json\\s*", "")
                  .replaceAll("```\\s*$", "")
                  .replaceAll("```", "");
    }
    
    /**
     * 移除注释
     */
    private static String removeComments(String json) {
        return COMMENTS_PATTERN.matcher(json).replaceAll("");
    }
    
    /**
     * 修复 Python 常量
     */
    private static String fixPythonConstants(String json) {
        java.util.regex.Matcher matcher = PYTHON_CONSTANTS_PATTERN.matcher(json);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String constant = matcher.group(1);
            String replacement;
            switch (constant) {
                case "True": 
                    replacement = "true";
                    break;
                case "False": 
                    replacement = "false";
                    break;
                case "None": 
                    replacement = "null";
                    break;
                default: 
                    replacement = constant;
                    break;
            }
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 修复单引号为双引号
     */
    private static String fixSingleQuotes(String json) {
        StringBuilder result = new StringBuilder();
        boolean inDoubleQuoteString = false;
        boolean inSingleQuoteString = false;

        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);

            // 检查是否为转义字符
            boolean isEscaped = isEscapedCharacter(json, i);

            if (c == '"' && !isEscaped && !inSingleQuoteString) {
                inDoubleQuoteString = !inDoubleQuoteString;
                result.append(c);
            } else if (c == '\'' && !isEscaped && !inDoubleQuoteString) {
                inSingleQuoteString = !inSingleQuoteString;
                result.append('"'); // 替换为双引号
            } else {
                result.append(c);
            }
        }

        return result.toString();
    }

    /**
     * 检查指定位置的字符是否被转义
     */
    private static boolean isEscapedCharacter(String str, int index) {
        if (index == 0) {
            return false;
        }

        int backslashCount = 0;
        int i = index - 1;

        // 向前计算连续的反斜杠数量
        while (i >= 0 && str.charAt(i) == '\\') {
            backslashCount++;
            i--;
        }

        // 奇数个反斜杠表示当前字符被转义
        return backslashCount % 2 == 1;
    }
    
    /**
     * 修复未引用的键
     */
    private static String fixUnquotedKeys(String json) {
        return UNQUOTED_KEY_PATTERN.matcher(json).replaceAll("$1\"$2\":");
    }
    
    /**
     * 移除尾随逗号和多余的逗号
     */
    private static String removeTrailingCommas(String json) {
        // 移除多个连续的逗号（在字符串外部）
        json = removeExcessiveCommas(json);

        // 移除尾随逗号
        json = json.replaceAll(",+\\s*([}\\]])", "$1");

        // 再移除普通的尾随逗号
        return TRAILING_COMMA_PATTERN.matcher(json).replaceAll("$1");
    }

    /**
     * 移除多余的逗号（保持在字符串外部）
     */
    private static String removeExcessiveCommas(String json) {
        StringBuilder result = new StringBuilder();
        boolean inString = false;
        boolean lastWasComma = false;

        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);
            boolean isEscaped = isEscapedCharacter(json, i);

            if (c == '"' && !isEscaped) {
                inString = !inString;
                result.append(c);
                lastWasComma = false;
            } else if (inString) {
                result.append(c);
                lastWasComma = false;
            } else if (c == ',') {
                if (!lastWasComma) {
                    result.append(c);
                    lastWasComma = true;
                }
                // 跳过多余的逗号
            } else if (!Character.isWhitespace(c)) {
                result.append(c);
                lastWasComma = false;
            } else {
                result.append(c);
                // 空白字符不改变逗号状态
            }
        }

        return result.toString();
    }
    
    /**
     * 修复字符串连接 (如 "hello" + "world")
     */
    private static String fixStringConcatenation(String json) {
        return json.replaceAll("\"\\s*\\+\\s*\"", "");
    }
    
    /**
     * 修复转义字符
     */
    private static String fixEscapedStrings(String json) {
        // 修复双重转义
        json = json.replaceAll("\\\\\\\\\"", "\\\\\"");
        
        // 修复不必要的转义
        json = json.replaceAll("\\\\([^\"\\\\nrtbf/])", "$1");
        
        return json;
    }
    
    /**
     * 修复不完整的结构
     */
    private static String fixIncompleteStructures(String json) {
        json = json.trim();

        StructureBalance balance = calculateStructureBalance(json);

        // 如果存在负向不平衡，先移除多余的结束符
        json = removeExcessiveClosingBrackets(json, balance);

        // 使用栈来正确补全结束符
        return completeStructureWithStack(json);
    }

    /**
     * 使用栈正确补全结构
     */
    private static String completeStructureWithStack(String json) {
        StringBuilder result = new StringBuilder();
        Stack<Character> openBrackets = new Stack<>();
        boolean inString = false;

        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);
            boolean isEscaped = isEscapedCharacter(json, i);

            result.append(c);

            if (c == '"' && !isEscaped) {
                inString = !inString;
            } else if (!inString) {
                switch (c) {
                    case '{':
                    case '[':
                        openBrackets.push(c);
                        break;
                    case '}':
                        if (!openBrackets.isEmpty() && openBrackets.peek() == '{') {
                            openBrackets.pop();
                        }
                        break;
                    case ']':
                        if (!openBrackets.isEmpty() && openBrackets.peek() == '[') {
                            openBrackets.pop();
                        }
                        break;
                }
            }
        }

        // 补全缺失的引号
        if (inString) {
            result.append('"');
        }

        // 按栈的顺序补全缺失的结束符
        while (!openBrackets.isEmpty()) {
            char openChar = openBrackets.pop();
            if (openChar == '{') {
                result.append('}');
            } else if (openChar == '[') {
                result.append(']');
            }
        }

        return result.toString();
    }

    /**
     * 计算结构平衡状态 - 使用栈来准确检测括号匹配
     */
    private static StructureBalance calculateStructureBalance(String json) {
        StructureBalance balance = new StructureBalance();
        Stack<Character> bracketStack = new Stack<>();

        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);
            boolean isEscaped = isEscapedCharacter(json, i);

            if (c == '"' && !isEscaped) {
                balance.inString = !balance.inString;
            } else if (!balance.inString) {
                switch (c) {
                    case '{':
                    case '[':
                        bracketStack.push(c);
                        break;
                    case '}':
                        if (bracketStack.isEmpty() || bracketStack.peek() != '{') {
                            balance.braceBalance--; // 记录多余的结束符
                        } else {
                            bracketStack.pop();
                        }
                        break;
                    case ']':
                        if (bracketStack.isEmpty() || bracketStack.peek() != '[') {
                            balance.bracketBalance--; // 记录多余的结束符
                        } else {
                            bracketStack.pop();
                        }
                        break;
                }
            }
        }

        // 计算剩余未匹配的开始符
        while (!bracketStack.isEmpty()) {
            char remaining = bracketStack.pop();
            if (remaining == '{') {
                balance.braceBalance++;
            } else if (remaining == '[') {
                balance.bracketBalance++;
            }
        }

        return balance;
    }

    /**
     * 移除多余的结束符
     */
    private static String removeExcessiveClosingBrackets(String json, StructureBalance balance) {
        if (balance.braceBalance >= 0 && balance.bracketBalance >= 0) {
            return json; // 没有多余的结束符
        }

        StringBuilder result = new StringBuilder();
        boolean inString = false;
        int braceCount = 0;
        int bracketCount = 0;

        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);
            boolean isEscaped = isEscapedCharacter(json, i);
            boolean shouldAppend = true;

            if (c == '"' && !isEscaped) {
                inString = !inString;
            } else if (!inString) {
                switch (c) {
                    case '{':
                        braceCount++;
                        break;
                    case '}':
                        if (braceCount > 0) {
                            braceCount--;
                        } else {
                            shouldAppend = false; // 跳过多余的 }
                        }
                        break;
                    case '[':
                        bracketCount++;
                        break;
                    case ']':
                        if (bracketCount > 0) {
                            bracketCount--;
                        } else {
                            shouldAppend = false; // 跳过多余的 ]
                        }
                        break;
                }
            }

            if (shouldAppend) {
                result.append(c);
            }
        }

        return result.toString();
    }

    /**
     * 结构平衡状态类
     */
    private static class StructureBalance {
        int braceBalance = 0;           // 花括号平衡
        int bracketBalance = 0;         // 方括号平衡
        boolean inString = false;       // 是否在字符串内
    }
    
    /**
     * 清理多余的空白字符
     */
    private static String cleanWhitespace(String json) {
        // 移除多余的空白字符，但保留字符串内的空白
        StringBuilder result = new StringBuilder();
        boolean inString = false;
        char prevChar = '\0';
        
        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);
            
            if (c == '"' && prevChar != '\\') {
                inString = !inString;
                result.append(c);
            } else if (inString) {
                result.append(c);
            } else if (!Character.isWhitespace(c)) {
                result.append(c);
            } else if (Character.isWhitespace(c) && !Character.isWhitespace(prevChar)) {
                // 只保留一个空格
                result.append(' ');
            }
            
            prevChar = c;
        }
        
        return result.toString().trim();
    }
    
    /**
     * 降级处理方案
     */
    private static String handleFallback(String originalJson) {
        String cleaned = originalJson.trim();
        
        // 基本清理
        cleaned = cleaned.replaceAll("```json", "")
                        .replaceAll("```", "")
                        .replaceAll("//.*", "")
                        .trim();
        
        // 如果包含大括号，尝试提取对象
        if (cleaned.contains("{") && cleaned.contains("}")) {
            int start = cleaned.indexOf("{");
            int end = cleaned.lastIndexOf("}");
            if (start < end) {
                return cleaned.substring(start, end + 1);
            }
        }
        
        // 如果包含方括号，尝试提取数组
        if (cleaned.contains("[") && cleaned.contains("]")) {
            int start = cleaned.indexOf("[");
            int end = cleaned.lastIndexOf("]");
            if (start < end) {
                return cleaned.substring(start, end + 1);
            }
        }
        
        // 最后的降级方案
        return "{}";
    }
    
    /**
     * 验证修复后的 JSON 是否有效
     */
    public static boolean isValidJson(String json) {
        try {
            com.alibaba.fastjson.JSON.parse(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证JSON结构是否有效（不依赖外部库）
     */
    public static boolean isValidJsonStructure(String json) {
        if (StringUtils.isEmpty(json)) {
            return false;
        }

        json = json.trim();

        // 检查是否是基本类型
        if (json.equals("true") || json.equals("false") || json.equals("null")) {
            return true;
        }

        // 检查是否是数字
        try {
            Double.parseDouble(json);
            return true;
        } catch (NumberFormatException e) {
            // 不是数字，继续检查
        }

        // 检查是否是字符串
        if (json.startsWith("\"") && json.endsWith("\"") && json.length() >= 2) {
            return isValidJsonString(json);
        }

        // 检查是否是对象或数组
        if ((json.startsWith("{") && json.endsWith("}")) ||
            (json.startsWith("[") && json.endsWith("]"))) {
            return isValidJsonStructureInternal(json);
        }

        return false;
    }

    /**
     * 验证JSON字符串是否有效
     */
    private static boolean isValidJsonString(String json) {
        if (json.length() < 2 || !json.startsWith("\"") || !json.endsWith("\"")) {
            return false;
        }

        // 检查字符串内部的转义字符是否正确
        for (int i = 1; i < json.length() - 1; i++) {
            char c = json.charAt(i);
            if (c == '"' && !isEscapedCharacter(json, i)) {
                return false; // 未转义的内部双引号
            }
        }

        return true;
    }

    /**
     * 验证JSON结构内部是否有效 - 使用栈来准确检测括号匹配
     */
    private static boolean isValidJsonStructureInternal(String json) {
        Stack<Character> bracketStack = new Stack<>();
        boolean inString = false;

        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);
            boolean isEscaped = isEscapedCharacter(json, i);

            if (c == '"' && !isEscaped) {
                inString = !inString;
            } else if (!inString) {
                switch (c) {
                    case '{':
                    case '[':
                        bracketStack.push(c);
                        break;
                    case '}':
                        if (bracketStack.isEmpty() || bracketStack.peek() != '{') {
                            return false; // 无效的括号匹配，如 }{
                        }
                        bracketStack.pop();
                        break;
                    case ']':
                        if (bracketStack.isEmpty() || bracketStack.peek() != '[') {
                            return false; // 无效的括号匹配，如 ][
                        }
                        bracketStack.pop();
                        break;
                }
            }
        }

        // 检查最终状态：栈应该为空且不在字符串内
        return bracketStack.isEmpty() && !inString;
    }
    
    /**
     * 带验证的修复方法
     */
    public static String repairAndValidate(String jsonStr) {
        String repaired = repair(jsonStr);
        
        if (isValidJson(repaired)) {
            return repaired;
        }
        
        // 如果修复后仍然无效，尝试降级方案
        log.warn("修复后的 JSON 仍然无效，使用降级方案: {}", repaired);
        return handleFallback(jsonStr);
    }
}