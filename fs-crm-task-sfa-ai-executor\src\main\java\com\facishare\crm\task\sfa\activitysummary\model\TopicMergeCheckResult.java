package com.facishare.crm.task.sfa.activitysummary.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 话题合并检查结果模型
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TopicMergeCheckResult {
    
    /**
     * 是否应该合并
     */
    @JSONField(name = "shouldMerge")
    private Boolean shouldMerge;
    
    /**
     * 合并原因或不合并原因
     */
    private String reason;
}
