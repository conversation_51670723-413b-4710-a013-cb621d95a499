package com.facishare.crm.task.sfa.activitysummary.service.tag;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphDocument;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphMongoDao;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphTagResult;
import com.facishare.crm.task.sfa.activitysummary.model.TagProcessingResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 段落标签数据服务
 * 负责标签数据的存储和处理
 */
@Service
@Slf4j
public class ParagraphTagDataService {

    @Resource
    private ParagraphMongoDao paragraphMongoDao;

    /**
     * 更新段落文档的标签
     *
     * @param tenantId           租户ID
     * @param paragraphDocuments 段落文档列表
     * @param processingResult   处理结果
     */
    public void updateParagraphTags(String tenantId, List<ParagraphDocument> paragraphDocuments,
                                    TagProcessingResult processingResult) {
        Map<String, List<ParagraphTagResult.TagResult>> mergedResults = processingResult.getTagResults();

        if (CollectionUtils.isEmpty(paragraphDocuments) || mergedResults.isEmpty()) {
            return;
        }

        try {
            // 收集需要更新的文档
            List<ParagraphDocument> documentsToUpdate = new ArrayList<>();

            // 遍历每个段落文档
            for (ParagraphDocument document : paragraphDocuments) {
                String paragraphId = document.getId().toString();
                List<ParagraphTagResult.TagResult> tags = mergedResults.get(paragraphId);
                List<String> allTags = Lists.newArrayList();
                List<String> tagReason = Lists.newArrayList();
                List<String> tagNoReason = Lists.newArrayList();
                if(CollectionUtils.isEmpty(tags)){
                    continue;
                }
                for (ParagraphTagResult.TagResult tag : tags) {
                    allTags.addAll(tag.getTagId());
                    if (StringUtils.isNotBlank(tag.getReason())) {
                        tagReason.add(tag.getReason());
                    }
                    if (StringUtils.isNotBlank(tag.getNoReason())) {
                        tagNoReason.add(tag.getNoReason());
                    }
                }
                if (CollectionUtils.isNotEmpty(tags)) {
                    // 更新标签
                    document.setTags(allTags);
                    document.setLastUpdateTime(System.currentTimeMillis());
                    if(CollectionUtils.isNotEmpty(tagReason)){
                        document.setReason(JSON.toJSONString(tagReason));
                    }
                    if(CollectionUtils.isNotEmpty(tagNoReason)){
                        document.setNoReason(JSON.toJSONString(tagNoReason));
                    }
                    documentsToUpdate.add(document);
                }
            }
            // 批量更新文档
            if (CollectionUtils.isNotEmpty(documentsToUpdate)) {
                long updateTagsCount = paragraphMongoDao.batchUpdateTags(tenantId, documentsToUpdate);
                log.info("Batch updated tags for {} paragraphs, update count {}", documentsToUpdate.size(), updateTagsCount);
            }
        } catch (Exception e) {
            log.error("Error updating paragraph tags", e);
        }
    }
} 