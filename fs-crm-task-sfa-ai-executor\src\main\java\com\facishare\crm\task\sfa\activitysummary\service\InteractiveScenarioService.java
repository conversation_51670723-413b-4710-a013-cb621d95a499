package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson2.JSONObject;
import com.facishare.crm.sfa.expression.SFAExpressionService;
import com.facishare.crm.sfa.expression.util.ConvertUtils;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.task.sfa.activitysummary.model.CorpusType;
import com.facishare.crm.task.sfa.activitysummary.model.InteractiveScenarioModel;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.rest.AiRestProxy;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.ObjectFunctionService;
import com.facishare.paas.appframework.core.predef.service.dto.function.FunctionInfo;
import com.facishare.paas.appframework.core.predef.service.dto.function.RunFunction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.functions.utils.Maps;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.task.sfa.common.constants.CommonConstant.ACTIVE_RECORD_API_NAME;

/**
 * 互动场景 自定义提示词
 */
@Component
@Slf4j
public class InteractiveScenarioService {

    public static String AGENT_PROMPT_VERSION_API_NAME = "AgentPromptVersionObj";
    public static final Map<String, InteractiveScenarioModel.ScenarioContext> SCENARIO_CONTEXT_MAP = new HashMap<>();
    private static boolean ENABLE = false;

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private AiRestProxy aiRestProxy;
    @Resource
    private CompletionsService completions;
    @Resource
    private SFAExpressionService sfaExpressionService;
    @Resource
    private ObjectFunctionService objectFunctionService;

    static {
        ConfigFactory.getInstance().getConfig("fs-sfa-ai", (config) -> {
            String jsonText = config.get("customPromptWordsConfig", "{}");
            try {
                initConfig(jsonText);
            } catch (Exception e) {
                log.error("interactiveScenarioConfig config error!jsonText={}", jsonText, e);
            }
            if ("true".equals(config.get("enableAutoInteractiveScenario", "false"))) {
                ENABLE = true;
            }
        });
    }

    /**
     * 根据销售记录的互动场景字段获取模板
     *
     * @param model          模块ApiName枚举类
     * @param user           user
     * @param activeRecordId 销售记录Id
     */
    public InteractiveScenarioModel.TemplateConfig getTemplateConfig(InteractiveScenarioModel.ModelApiName model, User user, String activeRecordId) {
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(user.getTenantId()), activeRecordId, ACTIVE_RECORD_API_NAME);
        return getTemplateConfig(model, user, objectData);
    }

    /**
     * 根据互动场景获取模板
     *
     * @param model            模块ApiName枚举类
     * @param user             user
     * @param activeRecordData 销售记录对象数据，当传null的时候会抛出ValidateException异常
     */
    public InteractiveScenarioModel.TemplateConfig getTemplateConfig(InteractiveScenarioModel.ModelApiName model, User user, IObjectData activeRecordData) {
        if (activeRecordData == null) {
            log.error("activeRecordData data is null!");
            throw new ValidateException("activeRecordData is null!");
        }
        log.info("getTemplateConfig model:{},id:{}", model.getApiName(), activeRecordData.getId());
        InteractiveScenarioModel.ScenarioContext scenarioContext = SCENARIO_CONTEXT_MAP.get(model.getApiName());
        if (scenarioContext == null || scenarioContext.getTemplate() == null) {
            log.error("'fs-sfa-ai.customPromptWordsConfig' config file error!");
            throw new ValidateException("config error!");
        }
        String userAgentPrompt = getUserAgentPromptVersionData(model, user, activeRecordData);
        if (!ObjectUtils.isEmpty(userAgentPrompt)) {
            InteractiveScenarioModel.TemplateConfig templateConfig = InteractiveScenarioModel.TemplateConfig.builder()
                    .content(userAgentPrompt)
                    .templateApiName(scenarioContext.getTemplateApiName())
                    .sourceConfig(Maps.of("content", userAgentPrompt))
                    .build();
            log.info("model:{},id:{},user templateConfig:{}", model.getApiName(), activeRecordData.getId(), templateConfig);
            return templateConfig;
        }
        String interactiveScenario = activeRecordData.get("interactive_scenario", String.class);
        if (ObjectUtils.isEmpty(interactiveScenario) || !scenarioContext.getTemplate().containsKey(interactiveScenario)) {
            interactiveScenario = "default";
        }
        InteractiveScenarioModel.TemplateConfig templateConfig = scenarioContext.getTemplate().get(interactiveScenario);
        if (templateConfig == null) {
            log.error("'fs-sfa-ai.customPromptWordsConfig' config file error!");
            throw new ValidateException("config error!");
        }
        log.info("model:{},id:{},system templateConfig:{}", model.getApiName(), activeRecordData.getId(), templateConfig);
        return templateConfig;
    }

    /**
     * 根据互动场景获取ai结果
     *
     * @param model            模块ApiName枚举类
     * @param user             用户
     * @param activeRecordData 销售记录对象数据，当传null的时候会抛出ValidateException异常
     * @param aiParameter      调用提示词模板需要传的其他参数，如生成语言等
     */
    public String getAiResult(InteractiveScenarioModel.ModelApiName model, User user, IObjectData activeRecordData, Map<String, String> aiParameter) {
        InteractiveScenarioModel.TemplateConfig templateConfig = getTemplateConfig(model, user, activeRecordData);
        return getAiResultByTemplateConfig(templateConfig, user, activeRecordData.getId(), aiParameter);
    }

    /**
     * 根据互动场景获取ai结果
     *
     * @param model          模块ApiName枚举类
     * @param user           用户
     * @param activeRecordId 销售记录Id
     * @param aiParameter    调用提示词模板需要传的其他参数，如生成语言等
     */
    public String getAiResult(InteractiveScenarioModel.ModelApiName model, User user, String activeRecordId, Map<String, String> aiParameter) {
        InteractiveScenarioModel.TemplateConfig templateConfig = getTemplateConfig(model, user, activeRecordId);
        return getAiResultByTemplateConfig(templateConfig, user, activeRecordId, aiParameter);
    }

    private String getAiResultByTemplateConfig(InteractiveScenarioModel.TemplateConfig templateConfig, User user,
                                               String activeRecordId, Map<String, String> aiParameter) {
        Map<String, Object> argMap = new HashMap<>();
        argMap.putAll(templateConfig.getSourceConfig());
        argMap.putAll(aiParameter);
        AiRestProxyModel.Arg aiArg = AiRestProxyModel.Arg.builder()
                .apiName(templateConfig.getTemplateApiName())
                .bingObjectDataId(activeRecordId)
                .sceneVariables(argMap)
                .build();
        return completions.requestCompletion(user, aiArg);
    }

    /**
     * 校验用户自定义的规则，并获取提示词子模板的内容
     *
     * @param model      模块枚举
     * @param user       用户
     * @param objectData 被校验的数据
     * @return 有自定义提示词子模板，返回子模板内容，如果没有就返回null
     */
    private String getUserAgentPromptVersionData(InteractiveScenarioModel.ModelApiName model, User user, IObjectData objectData) {
        if (ObjectUtils.isEmpty(objectData)) {
            return null;
        }
        String tenantId = user.getTenantId();
        // 查询所有符合规则的用户配置
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "model_api_name", model.getApiName());
        SearchUtil.fillFilterEq(filters, "prompt_status", "enable");
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "0");
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy(IObjectData.LAST_MODIFIED_TIME, false)));
        QueryResult<IObjectData> agentPromptDataList = serviceFacade.findBySearchQuery(User.systemUser(tenantId), AGENT_PROMPT_VERSION_API_NAME, searchTemplateQuery);
        if (ObjectUtils.isEmpty(agentPromptDataList) || ObjectUtils.isEmpty(agentPromptDataList.getData())) {
            return null;
        }
        // 循环校验规则
        for (IObjectData o : agentPromptDataList.getData()) {
            String filter = "";
            String aplApiName = "";
            if (!ObjectUtils.isEmpty(o.get("rule"))) {
                JSONObject ruleObject = JSONObject.parseObject(o.get("rule",String.class));
                filter = String.format("{filters:%s}", ruleObject.getString("filter"));
                if (!ObjectUtils.isEmpty(ruleObject.get("APL"))) {
                    aplApiName = ruleObject.getJSONObject("APL").getString("function_api_name");
                }
            }
            // 规则
            if (!checkRuleByFilter(tenantId, filter, objectData)) {
                continue;
            }
            // APL
            if (!checkRuleByAPL(aplApiName, tenantId, objectData.getId(), objectData.getDescribeApiName())) {
                continue;
            }
            // 查询子提示词的内容
            JSONObject templateObject = JSONObject.parseObject(o.get("subtemplate_api_name", String.class));
            AiRestProxyModel.PromptPreViewArg promptPreViewArg = AiRestProxyModel.PromptPreViewArg.builder()
                    .dataId(objectData.getId())
                    .promptApiName(templateObject.getString("promptApiName"))
                    .lang(I18N.ZH_CN)
                    .build();
            Map<String, String> headers = AiRestProxy.getHeaders(tenantId, user.getUserId());
            AiRestProxyModel.PromptPreViewResponse promptPreViewResponse = aiRestProxy.promptPreView(promptPreViewArg, headers);
            if (ObjectUtils.isEmpty(promptPreViewResponse.getResult())){
                return null;
            }
            return promptPreViewResponse.getResult().getContent();
        }
        // 不匹配，返回null
        return null;
    }

    private boolean checkRuleByFilter(String tenantId, String filter, IObjectData objectData) {
        if (ObjectUtils.isEmpty(filter)) {
            return true;
        }
        // 如果不选，默认符合所有数据，这里前端不传filter应该更合适
        for (Wheres wheres : ConvertUtils.convert2Wheres(filter)) {
            for (IFilter wheresFilter : wheres.getFilters()) {
                if (ObjectUtils.isEmpty(wheresFilter.getFieldValues())){
                    return true;
                }
            }
        }
        IObjectDescribe describe = serviceFacade.findObject(tenantId, objectData.getDescribeApiName());
        try {
            Boolean evaluate = sfaExpressionService.evaluate(filter, objectData, describe);
            return Boolean.TRUE.equals(evaluate);
        } catch (Exception e) {
            log.error("checkRuleByFilter error.", e);
        }
        return false;
    }

    private boolean checkRuleByAPL(String aplApiName, String tenantId, String objectDataId, String objectApiName) {
        if (ObjectUtils.isEmpty(aplApiName)) {
            return true;
        }
        RunFunction.Arg arg = new RunFunction.Arg();
        arg.setParameters(Lists.newArrayList(
                FunctionInfo.Parameter.builder().type("String").name("objectDataId").value(objectDataId).build(),
                FunctionInfo.Parameter.builder().type("String").name("objectApiName").value(objectApiName).build()
        ));
        arg.setApiName(aplApiName);
        RequestContext requestContext = RequestContext.builder()
                .user(User.systemUser(tenantId))
                .tenantId(tenantId)
                .build();
        ServiceContext serviceContext = new ServiceContext(requestContext, "interactiveScenario", "getUserAgentPromptVersionData");
        try {
            RunFunction.Result result = objectFunctionService.runFunction(arg, serviceContext);
            return Boolean.TRUE.equals(result.getFunctionResult());
        } catch (Exception e) {
            log.error("checkRuleByAPL error!", e);
        }
        return false;
    }

    private static void initConfig(String configJsonText) {
        SCENARIO_CONTEXT_MAP.clear();
        JSONObject baseObject = JSONObject.parseObject(configJsonText);
        for (String modelKey : baseObject.keySet()) {
            JSONObject modelObject = baseObject.getJSONObject(modelKey);
            String templateApiName = modelObject.getString("template_api_name");
            String useVariableName = modelObject.getString("use_variable_name");
            if (ObjectUtils.isEmpty(useVariableName)) {
                useVariableName = "content";
            }
            Map<String, InteractiveScenarioModel.TemplateConfig> template = new HashMap<>();
            JSONObject templateObject = modelObject.getJSONObject("template");
            for (String scenarioKey : templateObject.keySet()) {
                JSONObject jsonObject = templateObject.getJSONObject(scenarioKey);
                Map<String, String> sourceConfig = new HashMap<>();
                jsonObject.keySet().forEach(key -> sourceConfig.put(key, jsonObject.getString(key)));
                InteractiveScenarioModel.TemplateConfig templateConfig = InteractiveScenarioModel.TemplateConfig.builder()
                        .templateApiName(templateApiName)
                        .content(jsonObject.getString(useVariableName))
                        .sourceConfig(sourceConfig)
                        .build();
                template.put(scenarioKey, templateConfig);
            }
            InteractiveScenarioModel.ScenarioContext scenarioContext = InteractiveScenarioModel.ScenarioContext.builder()
                    .moduleApiName(modelKey)
                    .templateApiName(templateApiName)
                    .useVariableName(useVariableName)
                    .template(template)
                    .build();
            SCENARIO_CONTEXT_MAP.put(modelKey, scenarioContext);
        }
    }

    /**
     * 校验销售记录的互动场景字段，如果为空，会使用ai判断一次销售记录的互动场景
     *
     * @param tenantId 租户Id
     * @param objectId 销售记录Id
     */
    public void checkInteractiveScenario(String tenantId, String objectId) {
        if (!ENABLE) {
            log.info("skip checkInteractiveScenario");
            return;
        }
        try {
            IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), objectId, ACTIVE_RECORD_API_NAME);
            if (objectData == null) {
                log.warn("ActiveRecordObj data is null!");
                return;
            }
            String interactiveScenario = objectData.get("interactive_scenario", String.class);
            if (!ObjectUtils.isEmpty(interactiveScenario)) {
                return;
            }
            // 调用ai获取洞察场景
            CorpusType.Props corpusTypeANdProps = completions.getCorpusTypeANdProps(User.systemUser(tenantId), "check_interactive_scenario", objectId, null);
            if (corpusTypeANdProps == null) {
                log.error("corpusTypeANdProps is null!");
                return;
            }
            log.info("objectId:{}, corpusType:{}", objectId, corpusTypeANdProps.getCorpusType());
            objectData.set("interactive_scenario", corpusTypeANdProps.getCorpusType());
            serviceFacade.updateObjectData(User.systemUser(tenantId), objectData);
        } catch (Exception e) {
            log.error("check interactive_scenario error.", e);
        }
    }

    @Deprecated
    public InteractiveScenarioModel.TemplateConfig test(InteractiveScenarioModel.ModelApiName model, User user, IObjectData objectData) {
        return getTemplateConfig(model,user,objectData);
    }

}
