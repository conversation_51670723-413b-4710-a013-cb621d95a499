package com.facishare.crm.task.sfa.activitysummary.service.paragraph;

import com.facishare.crm.task.sfa.activitysummary.model.ParagraphContext;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphResultModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 文本分片处理服务
 */
@Slf4j
@Service
public class TextSegmentationService {
    @Autowired
    private ContentBatchProcessService contentBatchProcessService;

    /**
     * 处理长文本分片
     *
     * @param user     用户
     * @param content  原始文本内容
     * @param activeRecordData 销售记录
     */
    public void processLongText(User user, String content, IObjectData activeRecordData) {
        if (StringUtils.isBlank(content)) {
            log.warn("文本内容为空，跳过处理");
            return;
        }

        // 检查文本是否缺少足够的换行符
        if (!hasEnoughNewlines(content)) {
            log.info("文本缺少足够的换行符，使用基于字符的分段处理");
            return;
        }

        log.info("文本包含足够的换行符，使用基于行的分段处理");
        processLongTextByLines(user, content, activeRecordData);
    }


    public boolean hasEnoughNewlines(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }
        int expectedNewlines = content.length() / ParagraphContext.MIN_CONTENT_LENGTH;
        long actualNewlines = content.chars().filter(ch -> ch == '\n').count();
        log.info("预期换行符数量: {},实际换行符数量: {},是否足够: {}", expectedNewlines, actualNewlines, actualNewlines >= expectedNewlines);
        return actualNewlines >= expectedNewlines;
    }

    /**
     * 基于行分割处理长文本
     *
     * @param user     用户
     * @param content  原始文本内容
     * @param activeRecordData 销售记录
     */
    private void processLongTextByLines(User user, String content, IObjectData activeRecordData) {
        // 按行分割并添加行号
        String[] lines = content.split("\\r?\\n");

        // 添加数组非空检查
        if (lines.length == 0) {
            log.warn("分割后的文本行为空，跳过处理");
            return;
        }

        ParagraphContext paragraphContext = new ParagraphContext();
        String previousSummary = null;
        paragraphContext.setActiveRecordData(activeRecordData);
        paragraphContext.setType(ParagraphContext.TEXT_KEY);
        for (int lineNumber = 0; lineNumber < lines.length; lineNumber++) {
            String line = lines[lineNumber];
            //空行也记录,但是不处理
            if (StringUtils.isBlank(line) || StringUtils.isBlank(line.trim())) {
                continue;
            }
            // 处理单行文本
            paragraphContext.addContent(line.trim(), String.valueOf(lineNumber));
            // 检查是否需要处理当前批次
            if (paragraphContext.isContentLengthSufficient()) {
                if(lineNumber == (lines.length - 1)){
                    paragraphContext.setEnd(true);
                }
                previousSummary = processCurrentBatch(user, paragraphContext, previousSummary);
            }
        }

        // 处理最后一个批次
        if (!paragraphContext.isContentEmpty()) {
            // 在循环外标记最后一批
            paragraphContext.setEnd(true);
            processCurrentBatch(user, paragraphContext, previousSummary);
        }
    }

    /**
     * 处理当前批次
     *
     * @param user            用户
     * @param previousSummary 前一个摘要
     * @return 处理后的摘要
     */
    private String processCurrentBatch(User user, ParagraphContext paragraphContext,
                                       String previousSummary) {
        try {
            Optional<ParagraphResultModel> paragraphModelOpt = contentBatchProcessService.processParagraph(user, paragraphContext, previousSummary);
            String newSummary = paragraphModelOpt.map(ParagraphResultModel::getChunks)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(chunks -> chunks.get(0))
                    .map(ParagraphResultModel.Chunk::getSummary)
                    .orElse("");
            if (StringUtils.isNotEmpty(newSummary)) {
                return newSummary;
            }
        } catch (Exception e) {
            log.error("处理文本批次时发生异常，objectId: {}", paragraphContext.getActiveRecordData().getId(), e);
        }
        return previousSummary;
    }
}