package com.facishare.crm.web;

import com.facishare.crm.task.sfa.bizfeature.service.MethodologyMarkdownService;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 方法论Markdown控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("methodology/markdown")
@Slf4j
public class MethodologyMarkdownController {

    @Autowired
    private MethodologyMarkdownService methodologyMarkdownService;

    /**
     * 生成方法论Markdown
     *
     * @param methodologyId 方法论ID
     * @param tenantId 租户ID
     * @return Markdown内容
     */
    @GetMapping("/generate")
    public Map<String, Object> generateMarkdown(
            @RequestParam("methodologyId") String methodologyId,
            @RequestParam("tenantId") String tenantId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建系统用户
            User user = User.systemUser(tenantId);

            String markdown = methodologyMarkdownService.generateMethodologyMarkdown(methodologyId, user);

            result.put("success", true);
            result.put("data", markdown);
        } catch (Exception e) {
            log.error("生成方法论Markdown失败, methodologyId: {}, tenantId: {}", methodologyId, tenantId, e);
            result.put("success", false);
            result.put("message", "生成方法论Markdown失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 生成方法论Markdown并更新到方法论的process_overview字段
     *
     * @param methodologyId 方法论ID
     * @param tenantId 租户ID
     * @return 更新结果
     */
    @PostMapping("/update")
    public Map<String, Object> generateAndUpdateMarkdown(
            @RequestParam("methodologyId") String methodologyId,
            @RequestParam("tenantId") String tenantId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建系统用户
            User user = User.systemUser(tenantId);

            String markdown = methodologyMarkdownService.generateMethodologyMarkdown(methodologyId, user);
            boolean updated = methodologyMarkdownService.updateMethodologyProcessOverview(methodologyId, markdown, user);

            result.put("success", updated);
            result.put("data", markdown);

            if (!updated) {
                result.put("message", "更新方法论process_overview字段失败");
            }
        } catch (Exception e) {
            log.error("生成并更新方法论Markdown失败, methodologyId: {}, tenantId: {}", methodologyId, tenantId, e);
            result.put("success", false);
            result.put("message", "生成并更新方法论Markdown失败: " + e.getMessage());
        }

        return result;
    }
}
