package com.facishare.crm.task.sfa.services.rebate.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RebateRuleDimension implements Serializable {
    @J<PERSON><PERSON>ield(name = "object_api_name")
    @JsonProperty("object_api_name")
    private String objectApiName;
    @JSONField(name = "field_api_name")
    @JsonProperty("field_api_name")
    private String fieldApiName;
    @JSONField(name = "object_display_name")
    @<PERSON>sonProperty("object_display_name")
    private String objectDisplayName;
    @JSONField(name = "field_display_name")
    @JsonProperty("field_display_name")
    private String fieldDisplayName;
}
