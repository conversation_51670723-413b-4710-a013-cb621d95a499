package com.facishare.crm.task.sfa.activitysummary.model;

import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import lombok.Data;

import java.util.List;

public interface RequirementModel {
    @Data
    class Arg{
        private String tenantId;
        private String activeRecordId;
        private String linkAccountFieldApiName;
        private String linkAccountDataId;
        private String linkOpportunityFieldApiName;
        private String linkOpportunityDataId;
        private String linkLeadsFieldApiName;
        private String linkLeadsDataId;

        private String language;
        private String userId;
    }

    @Data
    class RequirementBatchMsg{
        private String tenantId;
        private List<String> requirementIds;
        private String language;
        private String userId;
    }

    @Data
    class RequirementSolve{
        private String assessmentDimension;
        private String requirementSolve;
        private List<AiRestProxyModel.Hit> hits;
    }
}
