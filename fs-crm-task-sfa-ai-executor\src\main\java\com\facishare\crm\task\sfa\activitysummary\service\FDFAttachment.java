package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.enums.FileTypeEnum;
import com.facishare.crm.task.sfa.rest.FileParseTextProxy;
import com.facishare.crm.task.sfa.rest.dto.FileParseText;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/17 10:40
 * @description:
 */
@Component
@Slf4j
public class FDFAttachment extends AbstractFile2Text implements File2Text{


    @Resource
    private FileParseTextProxy fileParseTextProxy;

    @Override
    public boolean support(FileTypeEnum fileType) {
        return false;
    }

    public void execute(ActivityMessage message, IObjectData activityData){
        String bigText = parseText(message);
        updateActivityText(message.getTenantId(), message.getObjectId(), message.getObjectApiName(), bigText);
        sendActivityMessage(message);
    }


    public String parseText(ActivityMessage message){
        List<String> pathWithExt = getPathWithExt(message);
        String ea = gdsHandler.getEAByEI(message.getTenantId());
        StringBuilder bigText = new StringBuilder();
        for (String s : pathWithExt) {
            bigText.append(getContent(ea, s));
        }
        return bigText.toString();
    }

    private String getContent(String ea, String path){
        StringBuilder result = new StringBuilder();
        FileParseText.Result textResult = fileParseTextProxy.execute(ea, path, Boolean.FALSE.toString());
        if (BooleanUtils.isFalse(textResult.getSuccess()) || Objects.isNull(textResult.getData())) {
            log.warn("FileParseTextResource execute error:{}", textResult);
            return result.toString();
        }
        List<FileParseText.ShardContents> shardContents = textResult.getData().getShardContents();
        for (FileParseText.ShardContents shardContent : shardContents) {
            result.append(shardContent.getContent() + "\n");
        }
        return result.toString();
    }

}
