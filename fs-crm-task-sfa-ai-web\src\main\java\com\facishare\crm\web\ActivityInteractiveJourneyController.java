package com.facishare.crm.web;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityInteractiveJourneyService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("activity_interactive_journey")
public class ActivityInteractiveJourneyController {
    private ActivityInteractiveJourneyService activityInteractiveJourneyService;

    @PostMapping("consume")
    public Object consume(@RequestBody ActivityMessage activityMessage) {
        activityInteractiveJourneyService.consume(activityMessage);
        return "OK";
    }
}
