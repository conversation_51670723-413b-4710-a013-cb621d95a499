package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 画像分项分数
 */
public interface ProfileAdviceConstants {
    /**
     * 画像
     */
    String PROFILE_ID = "profile_id";
    /**
     * 分析范围
     */
    String RANGE_TYPE = "range_type";
    enum RangeType {
        /**
         * 画像
         */
        PROFILE("profile"),
        /**
         * 维度
         */
        DIMENSION("dimension")
        ;

        private final String value;

        public String getValue() {
            return value;
        }

        RangeType(String value) {
            this.value = value;
        }
    }
    /**
     * 建议类型
     */
    String TYPE = "type";
    enum Type {
        /**
         * 纷享知识建议
         */
        FS("fs"),
        /**
         * 客户知识建议
         */
        CONS("customer")
        ;

        private final String value;

        public String getValue() {
            return value;
        }

        Type(String value) {
            this.value = value;
        }
    }
    /**
     * 维度
     */
    String FEATURE_DIMENSION_ID = "feature_dimension_id";
    /**
     * 任务
     */
    String TASK_ID = "task_id";
    /**
     * 建议
     */
    String ADVICE = "advice";
    /**
     * 序号
     */
    String SEQ = "seq";
    /**
     * 是否采纳
     */
    String IS_ACCEPT = "is_accept";
    /**
     * 特殊数组
     */
    String FEATURE_IDS = "feature_ids";
    /**
     * 关联知识库
     */
    String KNOWLEDGE_DOCUMENT_IDS = "knowledge_document_ids";
}