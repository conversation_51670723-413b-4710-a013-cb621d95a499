package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 标注规则常量
 *
 * <AUTHOR>
 */
public interface FeatureTagRuleConstants {
	String OBJECT_API_NAME = "FeatureTagRuleObj";
	/**
     * 启用状态
     */
	String ACTIVE_STATUS = "active_status";
	enum ActiveStatusType {
		/**
         * 启用
         */
		ENABLE("enable") ,
		/**
         * 禁用
         */
		DISABLE("disable") ;
		private final String activeStatus;

		public String getActiveStatusType() {
            return activeStatus;
        }


		ActiveStatusType(String activeStatus) {
            this.activeStatus = activeStatus;
        }
	}
	/**
     * 关联对象
     */
	String RELATED_OBJECT_API_NAME = "related_object_api_name";
	enum RelatedObjectApiNameType {
		/**
         * 线索
         */
		LEADSOBJ("LeadsObj") ,
		/**
         * 客户
         */
		ACCOUNTOBJ("AccountObj") ,
		/**
         * 商机
         */
		NEWOPPORTUNITYOBJ("NewOpportunityObj") ;
		private final String relatedObjectApiName;

		public String getRelatedObjectApiNameType() {
            return relatedObjectApiName;
        }


		RelatedObjectApiNameType(String relatedObjectApiName) {
            this.relatedObjectApiName = relatedObjectApiName;
        }
	}
	/**
     * 关注方
     */
	String FOLLOWER = "follower";
	enum FollowerType {
		/**
         * 我方
         */
		OUR_SIDE("our_side") ,
		/**
         * 客方
         */
		YOUR_SIDE("your_side") ,
		/**
         * 双方
         */
		BOTH_PARTIES("both_parties") ;
		private final String follower;

		public String getFollowerType() {
            return follower;
        }


		FollowerType(String follower) {
            this.follower = follower;
        }
	}
	/**
     * 正向判断标准
     */
	String POSITIVE_CRITERIA = "positive_criteria";
	/**
     * 正向样例
     */
	String POSITIVE_SAMPLE = "positive_sample";
	/**
     * 负向判断标准
     */
	String NEGATIVE_CRITERIA = "negative_criteria";
	/**
     * 负向样例
     */
	String NEGATIVE_SAMPLE = "negative_sample";
	/**
     * 是否更关注精准度
     */
	String FOCUS_ON_PRECISION = "focus_on_precision";
	enum FocusOnPrecisionType {
		/**
         * 是
         */
		TRUE("true") ,
		/**
         * 否
         */
		FALSE("false") ;
		private final String focusOnPrecision;

		public String getFocusOnPrecisionType() {
            return focusOnPrecision;
        }


		FocusOnPrecisionType(String focusOnPrecision) {
            this.focusOnPrecision = focusOnPrecision;
        }
	}
	/**
     * 数据类型
     */
	String SYSTEM_TYPE = "system_type";
	enum SystemType {
		/**
         * 系统
         */
		SYSTEM("system") ,
		/**
         * 自定义
         */
		UDEF("udef") ;
		private final String systemType;

		public String getSystemType() {
            return systemType;
        }


		SystemType(String systemType) {
            this.systemType = systemType;
        }
	}
	/**
     * 负责人所在部门
     */
	String OWNER_DEPARTMENT = "owner_department";
	/**
     * 相关团队
     */
	String RELEVANT_TEAM = "relevant_team";
}