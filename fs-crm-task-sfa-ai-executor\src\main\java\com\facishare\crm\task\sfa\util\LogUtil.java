package com.facishare.crm.task.sfa.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.log.LogServiceImpl;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

/**
 * 日志 class
 *
 * <AUTHOR>
 * @date 2019/2/20
 */
@Slf4j
public class LogUtil {
    private static final LogService logService = SpringUtil.getContext().getBean(LogServiceImpl.class);

    /**
     * 特殊行为添加日志
     *
     * @param user
     * @param objectDataList
     * @param eventType
     * @param actionType
     */
    public static void recordLogs(User user, List<IObjectData> objectDataList, IObjectDescribe objectDescribe, EventType eventType, ActionType actionType) {
        if (CollectionUtils.empty(objectDataList)) {
            log.warn("LogUtil->recordLogs  info:{}", "对象信息为空");
            return;
        }
        try {
            String describeApiName = objectDescribe.getApiName();
            Map<String, IObjectDescribe> objectDescribes = Maps.newHashMap();
            objectDescribes.put(describeApiName, objectDescribe);
            logService.masterDetailLog(user, eventType, actionType, objectDescribes, objectDataList);
        } catch (Exception e) {
            log.warn("LogUtil>recordLogs failed, user={} data={}", user, objectDataList, e);
        }
    }

    /**
     * 特殊行为添加日志
     *
     * @param user
     * @param objectDataList
     * @param eventType
     * @param actionType
     */
    public static void recordLogs(User user, List<IObjectData> objectDataList, IObjectDescribe objectDescribe, EventType eventType, ActionType actionType, String message) {
        if (CollectionUtils.empty(objectDataList)) {
            log.warn("LogUtil->recordLogs  info:{}", "对象信息为空");
            return;
        }
        try {
            logService.logWithCustomMessage(user, eventType, actionType, objectDescribe, objectDataList, message);
        } catch (Exception e) {
            log.warn("LogUtil>recordLogs failed, user={} data={}", user, objectDataList, e);
        }
    }

    /**
     * 特殊行为添加国际化的日志
     */
    public static void recordInternationalLogs(User user, IObjectData objectData, IObjectDescribe objectDescribe, EventType eventType, ActionType actionType, InternationalItem item) {
        if (ObjectUtils.isEmpty(objectData)) {
            log.warn("LogUtil->recordLogs  info:{}", "对象信息为空");
            return;
        }
        try {
            logService.logWithInternationalCustomMessage(user, eventType, actionType, objectDescribe, objectData, item.getDefaultInternationalValue(), item);
        } catch (Exception e) {
            log.warn("LogUtil>recordLogs failed, user={} data={}", user, objectData, e);
        }
    }

}
