package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl;

import com.facishare.crm.sfa.lto.activity.enums.AttendeesInsightType;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import org.springframework.stereotype.Component;


@Component
public class ImprovementSuggestionHandler extends AbstractInsightHandler<AttendeesInsightModel.InsightResult> {

    private static final String PROMPT = "prompt_attendee_insight_improvement_suggestion";

    @Override
    public String getInsightType() {
        return AttendeesInsightType.IMPROVEMENT_SUGGESTION;
    }

    @Override
    public void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        super.insightByOriginCorpus(attendeesInsightMessage, PROMPT);
    }

    @Override
    protected String getUserNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        return super.getOurSideNames(insightMessage);
    }
}
