remark1=本文档使用说明1：OpenApiIsDebug在线下请设置成true，则mq消费update接口时不会进行SupportedDescribe校验；如果是线上环境，请设置成false，并且设置好
2,isRtnJsonDebug是用来判断报错情况下是否把调用.net的REST服务的时候的转换之后的json返回给调用方。3,isDealLayout用于判断在初始化CRM元数据描述的时候是否初始化或者更新 Layout
udobjIsGray=true
udobjSupportedEIs=54819,2
isCheckDataPrivilege=true
isErrorMessageChinese=false
isRtnJsonDebug=false
openApiSupportedDescribeApiNames和openApiSupportedEIs。
openApiIsDebug=true
openApiSupportedDescribeApiNames=AccountObj,AccountAddrObj,AccountAttObj,AccountFinInfoObj,ContactObj,ContractObj,LeadsObj,LeadsPoolObj,InvoiceApplicationObj,MarketingEventObj,OpportunityObj,PaymentObj,ProductObj,RefundObj,ReturnedGoodsInvoiceObj,SalesOrderObj,SalesOrderProductObj,VisitingObj,HighSeasObj,ReturnedGoodsInvoiceProductObj,SaleActionObj,SaleActionStageObj
openApiSupportedEIs=7
printTemplateSupportApiNames=AccountObj,ContactObj,SalesOrderObj,SalesOrderProductObj,ProductObj,LeadsObj,PaymentObj,ContractObj,RefundObj
printTemplateSupportReference=AccountObj:SalesOrderObj,ContactObj,LeadsObj--SalesOrderObj:PaymentObj--ContactObj:LeadsObj
mqInitOpenApi=true
mqInitPrivilege=true
mqInitPrintTemplate=true
initFailReceiverEa=2
initFailReceiverIdList=1000
remark2=本文档使用说明2：如果是OpenApi支持的对象，原则上在下面必须要在下面指定主属性name的label、description、maxLength、isAutoNumber几个属性。
apiNames4SetLabel=ProductObj,SalesOrderObj,AccountObj,ContactObj,LeadsObj,PaymentObj,AccountFinInfoObj,InvoiceApplicationObj,ContractObj,AccountAddrObj,AccountAttObj,OpportunityObj,RefundObj,VisitingObj,LeadsPoolObj,ReturnedGoodsInvoiceObj,HighSeasObj,SaleActionObj,SaleActionStageObj
ProductObjNameLabel=产品名称
SalesOrderObjNameLabel=销售订单编号
AccountObjNameLabel=客户名称
ContactObjNameLabel=姓名
LeadsObjNameLabel=线索名称
PaymentObjNameLabel=回款编号
AccountFinInfoObjNameLabel=开票抬头
InvoiceApplicationObjNameLabel=开票申请编号
ContractObjNameLabel=合同编号
AccountAddrObjNameLabel=地址名称
AccountAttObjNameLabel=附件名称
OpportunityObjNameLabel=商机名称
RefundObjNameLabel=退款编号
VisitingObjNameLabel=拜访名称
LeadsPoolObjNameLabel=线索池名称
ReturnedGoodsInvoiceObjNameLabel=退货单编号
HighSeasObjNameLabel=公海名称
SaleActionObjNameLabel=销售流程名称
SaleActionStageObjNameLabel=销售阶段名称
ProductObjNameDescription=产品名称
SalesOrderObjNameDescription=销售订单编号
AccountObjNameDescription=客户名称
ContactObjNameDescription=姓名
LeadsObjNameDescription=线索名称
PaymentObjNameDescription=回款编号
AccountFinInfoObjNameDescription=开票抬头
InvoiceApplicationObjNameDescription=开票申请编号
ContractObjNameDescription=合同编号
AccountAddrObjNameDescription=地址名称
AccountAttObjNameDescription=附件名称
OpportunityObjNameDescription=商机名称
RefundObjNameDescription=退款编号
VisitingObjNameDescription=拜访名称
LeadsPoolObjNameDescription=线索池名称
ReturnedGoodsInvoiceObjNameDescription=退货单编号
HighSeasObjNameDescription=公海名称
SaleActionObjNameDescription=销售流程名称
SaleActionStageObjNameDescription=销售流程名称
ProductObjNameIsAutoNumber=false
SalesOrderObjNameIsAutoNumber=true,TradeCode
AccountObjNameIsAutoNumber=false
ContactObjNameIsAutoNumber=false
LeadsObjNameIsAutoNumber=false
PaymentObjNameIsAutoNumber=true,TradePaymentCode
AccountFinInfoObjNameIsAutoNumber=false
InvoiceApplicationObjNameIsAutoNumber=true, TradeBillCode
ContractObjNameIsAutoNumber=true, ContractNo
AccountAddrObjNameIsAutoNumber=false
AccountAttObjNameIsAutoNumber=false
OpportunityObjNameIsAutoNumber=false
RefundObjNameIsAutoNumber=true, TradeRefundCode
VisitingObjNameIsAutoNumber=false
LeadsPoolObjNameIsAutoNumber=false
HighSeasObjNameIsAutoNumber=false
SaleActionObjNameIsAutoNumber=false
SaleActionStageObjNameIsAutoNumber=false
ReturnedGoodsInvoiceObjNameIsAutoNumber=true,ReturnOrderCode
ProductObjNameMaxLength=100
SalesOrderObjNameMaxLength=100
AccountObjNameMaxLength=100
ContactObjNameMaxLength=100
LeadsObjNameMaxLength=100
PaymentObjNameMaxLength=100
AccountFinInfoObjNameMaxLength=100
InvoiceApplicationObjNameMaxLength=100
ContractObjNameMaxLength=100
AccountAddrObjNameMaxLength=100
AccountAttObjNameMaxLength=100
OpportunityObjNameMaxLength=100
RefundObjNameMaxLength=100
VisitingObjNameMaxLength=100
LeadsPoolObjNameMaxLength=100
HighSeasObjNameMaxLength=100
SaleActionObjNameMaxLength=100
SaleActionStageObjNameMaxLength=100
ReturnedGoodsInvoiceObjNameMaxLength=100
#对象不可见的字段
printTemplateInvisibleFiled={"AccountObj":["high_seas_id"]}

#调用.net的userdefine接口时，每个对象有些字段是不应该被更新的，配置在下面这里。
IgnoreUpdateFromDotNetUsrDefinedRest={"ProductObj":["product_status"],"AccountObj":["account_status"],"LeadsObj":["owner_department"],"SalesOrderObj":["owner","resource"],"ContactObj":["primary_contact","owner_department"],"PaymentObj":["owner"],"OpportunityObj":["sales_process_id"]}

#迷你版控制字段不显示的配置,InvisibleFieldsForVersionA控制不显示的字段，InvisibleRefObjsForVersionA控制不显示的关联对象,InvisibleActionsForVersionA控制不显示的按钮。
InvisibleFieldsForVersionA={"AccountObj":["high_seas_name","deal_status","recycled_reason","last_followed_time","remaining_time"],"LeadsObj":["marketing_event_name"]}
InvisibleRefObjsForVersionA={"AccountObj":["OpportunityObj","VisitingObj","SalesOrderObj","ContractObj","PaymentObj","RefundObj","InvoiceApplicationObj"],"ContactObj":["VisitingObj","OpportunityObj"]}
InvisibleActionsForVersionA={"AccountObj":["Move","Return"],"LeadsObj":[""]}
