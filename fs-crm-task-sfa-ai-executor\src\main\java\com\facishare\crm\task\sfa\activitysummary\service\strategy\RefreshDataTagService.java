package com.facishare.crm.task.sfa.activitysummary.service.strategy;

import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.DocumentProcessService;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.util.SFAConfigUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class RefreshDataTagService {

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private DirectTaggingService directTaggingService;

    @Autowired
    private DocumentProcessService documentProcessService;

    /**
     * 刷新数据 - 根据objectApiName查询销售记录并循环调用process方法
     *
     * @param tenantId      租户ID
     * @param objectId      对象ID
     * @param objectApiName 对象API名称
     */
    public void refreshData(String tenantId, String objectId, String objectApiName) {

        if (!SFAConfigUtil.isOpenCustomerProfileAgent(tenantId)) {
            log.info("[RefreshDataService] 未开启客户画像智能标签功能, tenantId: {}, objectId: {}, objectApiName: {}", tenantId, objectId, objectApiName);
            return;
        }

        log.info("[RefreshDataService] 开始刷新数据, tenantId: {}, objectId: {}, objectApiName: {}",
                tenantId, objectId, objectApiName);

        // 参数校验
        if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(objectId) || StringUtils.isEmpty(objectApiName)) {
            log.warn("[RefreshDataService] 参数不能为空, tenantId: {}, objectId: {}, objectApiName: {}",
                    tenantId, objectId, objectApiName);
            return;
        }

        // 创建系统用户
        User systemUser = createSystemUser(tenantId);

        // 根据objectApiName查询相关销售记录
        List<IObjectData> salesRecords = querySalesRecordsByObjectApiName(systemUser, objectId, objectApiName);
        if (CollectionUtils.isEmpty(salesRecords)) {
            log.info("[RefreshDataService] 未找到相关销售记录, objectId: {}, objectApiName: {}", objectId, objectApiName);
            return;
        }

        log.info("[RefreshDataService] 找到 {} 条销售记录，开始循环处理", salesRecords.size());

        // 循环处理每个销售记录
        for (IObjectData salesRecord : salesRecords) {
            try {
                // 非 call 类型 才发送消息
                if ("call".equals(salesRecord.get("interactive_types", String.class))) {
                    return;
                }
                processSalesRecord(systemUser, salesRecord);
            } catch (Exception e) {
                log.error("[RefreshDataService] 处理销售记录失败, salesRecordId: {}", salesRecord.getId(), e);
            }
        }

        log.info("[RefreshDataService] 数据刷新完成, 共处理 {} 条销售记录", salesRecords.size());
    }

    /**
     * 创建系统用户
     *
     * @param tenantId 租户ID
     * @return 系统用户
     */
    private User createSystemUser(String tenantId) {
        return new User(tenantId, CommonConstant.SUPER_USER);
    }

    /**
     * 根据objectApiName查询相关的销售记录
     *
     * @param user          用户信息
     * @param objectId      对象ID
     * @param objectApiName 对象API名称
     * @return 销售记录列表
     */
    public List<IObjectData> querySalesRecordsByObjectApiName(User user, String objectId, String objectApiName) {
        SearchTemplateQueryPlus searchQuery = new SearchTemplateQueryPlus();
        searchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()));

        // 根据objectApiName判断查询条件
        if (CommonConstant.LEADS_API_NAME.equals(objectApiName)) {
            // 如果是线索，查询销售记录的线索字段等于objectId
            searchQuery.addFilter(CommonConstant.LEADS_ID, Operator.EQ, objectId);
        } else if (CommonConstant.NEW_OPPORTUNITY_API_NAME.equals(objectApiName)) {
            // 如果是商机，查询销售记录的商机字段等于objectId
            searchQuery.addFilter(CommonConstant.NEW_OPPORTUNITY_ID, Operator.EQ, objectId);
        } else {
            log.warn("[RefreshDataService] 不支持的objectApiName: {}", objectApiName);
            return Lists.newArrayList();
        }

        searchQuery.setLimit(1000);
        searchQuery.setFindExplicitTotalNum(false);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        searchQuery.setSearchSource("db");

        OrderBy orderBy = new OrderBy(DBRecord.CREATE_TIME, false);
        searchQuery.setOrders(Lists.newArrayList(orderBy));

        try {
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(
                    user, CommonConstant.ACTIVE_RECORD_API_NAME, searchQuery);

            if (queryResult != null && CollectionUtils.isNotEmpty(queryResult.getData())) {
                log.info("[RefreshDataService] 查询到 {} 条销售记录, objectId: {}, objectApiName: {}",
                        queryResult.getData().size(), objectId, objectApiName);
                return queryResult.getData();
            }
        } catch (Exception e) {
            log.error("[RefreshDataService] 查询销售记录失败, objectId: {}, objectApiName: {}",
                    objectId, objectApiName, e);
        }

        return Lists.newArrayList();
    }

    /**
     * 处理单个销售记录
     *
     * @param user        用户信息
     * @param salesRecord 销售记录
     */
    private void processSalesRecord(User user, IObjectData salesRecord) {
        String salesRecordId = salesRecord.getId();
        log.info("[RefreshDataService] 开始处理销售记录, salesRecordId: {}", salesRecordId);

        try {
            // 判断使用mongo还是text类型
            String type = determineContentType(user, salesRecordId);
            log.info("[RefreshDataService] 销售记录内容类型判断结果, salesRecordId: {}, type: {}", salesRecordId, type);

            // 构造ActivityMessage
            ActivityMessage activityMessage = createActivityMessage(user, salesRecord);

            // 调用DirectTaggingService的process方法
            directTaggingService.process(user, activityMessage, type);

            log.info("[RefreshDataService] 销售记录处理完成, salesRecordId: {}, type: {}", salesRecordId, type);
        } catch (Exception e) {
            log.error("[RefreshDataService] 处理销售记录异常, salesRecordId: {}", salesRecordId, e);
        }
    }

    /**
     * 判断内容类型 - 查询mongo数据判断使用mongo还是text
     *
     * @param user          用户信息
     * @param salesRecordId 销售记录ID
     * @return "mongo" 如果有mongo数据，"text" 如果没有mongo数据
     */
    private String determineContentType(User user, String salesRecordId) {
        try {
            // 查询第一页的mongo数据，如果有数据则使用mongo，否则使用text
            List<InteractiveDocument> mongoDocuments = documentProcessService.queryDocumentsPage(
                    user.getTenantId(), salesRecordId, 0);

            if (CollectionUtils.isNotEmpty(mongoDocuments)) {
                log.debug("[RefreshDataService] 找到mongo数据, salesRecordId: {}, 文档数量: {}",
                        salesRecordId, mongoDocuments.size());
                return "mongo";
            } else {
                log.debug("[RefreshDataService] 未找到mongo数据, salesRecordId: {}, 使用text类型", salesRecordId);
                return "text";
            }
        } catch (Exception e) {
            log.error("[RefreshDataService] 查询mongo数据异常, salesRecordId: {}, 默认使用text类型", salesRecordId, e);
            return "text";
        }
    }

    /**
     * 创建ActivityMessage对象
     *
     * @param user        用户信息
     * @param salesRecord 销售记录
     * @return ActivityMessage对象
     */
    private ActivityMessage createActivityMessage(User user, IObjectData salesRecord) {
        return ActivityMessage.builder()
                .tenantId(user.getTenantId())
                .objectId(salesRecord.getId())
                .objectApiName(CommonConstant.ACTIVE_RECORD_API_NAME)
                .opId(user.getUserId())
                .stage("refresh")
                .build();
    }
}
