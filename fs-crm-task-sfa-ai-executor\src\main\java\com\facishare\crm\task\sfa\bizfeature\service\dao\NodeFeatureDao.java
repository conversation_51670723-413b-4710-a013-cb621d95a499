package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.NodeFeatureConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 节点特征关联数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NodeFeatureDao {

    @Autowired
    private ServiceFacade serviceFacade;


    /**
     * 根据节点ID列表和方法论ID查询节点特征关联
     */
    public List<IObjectData> fetchNodeFeaturesByNodeIdsAndMethodologyId(User user, List<String> nodeIds, String methodologyId) {
        if (CollectionUtils.isEmpty(nodeIds)) {
            return Lists.newArrayList();
        }
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, "0")
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(NodeFeatureConstants.NODE_ID, Operator.IN, nodeIds)
                .addFilter(NodeFeatureConstants.METHODOLOGY_ID, Operator.EQ, methodologyId)
                .addFilter(NodeFeatureConstants.STATUS, Operator.EQ, NodeFeatureConstants.StatusType.ENABLED.getStatusType());
        return Optional.ofNullable(serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.NODE_FEATURE, query))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }


}
