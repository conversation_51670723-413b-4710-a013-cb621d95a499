package com.facishare.crm.task.sfa.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @IgnoreI18nFile
 */
public interface ObjectLimitRuleModel {

    @Data
    @Builder
    class ObjectLimitRule {
        private String id;
        private String objectApiName;
        private String groupId;
        private String name;
        private String dataId;
        private String dataType;
        private String dataWheres;
        private String calculateId;
        private int isDeleted;
        private boolean defaultRule;
        private String ruleType;
        private List<ObjectLimitFilter> objectLimitFilterList;
    }

    @Data
    @Builder
    class ObjectLimitFilter {
        private String id;
        private String objectApiName;
        private String groupId;
        private String wheres;
        private int limitNumber;
        private String calculateId;
        private int isDeleted;
        private Long createTime;
    }

    @Data
    @Builder
    class ObjectLimitEmployeeRule{
        private String id;
        private String objectApiName;
        private String groupId;
        private String employeeId;
        private boolean defaultRule;
        private String calculateId;
        private int isDeleted;
    }

    @Data
    @Builder
    class ObjectLimitOverRule{
        private String id;
        private String objectApiName;
        private String objectPoolId;
    }

    enum DataTypeEnum {
        DEPARTMENT("department", "部门"),
        EMPLOYEE("employee", "人员"),
        USER_GROUP("user_group", "用户组"),
        USER_ROLE("user_role", "用户角色"),
        CUSTOM("custom", "自定义"),
        PARTNER("partner", "合作伙伴"),
        PARTNER_EMPLOYEE("partner_employee", "合作伙伴人员"),
        PARTNER_CUSTOM("partner_custom", "合作伙伴自定义");


        private String code;
        private String description;

        DataTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    enum RuleTypeEnum {
        EMPLOYEE("employee", "人员"),
        ORGANIZATION("organization", "组织");

        private String code;
        private String description;

        RuleTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static boolean isCorrectRuleType(String ruleTypeString) {
            for (RuleTypeEnum ruleType : RuleTypeEnum.values()) {
                if (ruleType.getCode().equals(ruleTypeString)) {
                    return true;
                }
            }
            return false;
        }
    }

    @Data
    @Builder
    class ObjectLimitRuleGlobalFilter{
        private String id;
        private String objectApiName;
        private String wheres;
        private int isDeleted;
    }
}
