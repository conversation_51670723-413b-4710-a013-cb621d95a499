[{"id": "686e2e6df847ab0001abf02e", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "67ed12c8829bce000139a8d5", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed11e8829bce0001398b61", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473321, "last_modified_by": "-10000", "last_modified_time": 1752486473321, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503819342}, {"id": "686e2e8ff847ab0001abfe16", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2a18f847ab0001a9995b", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed11e8829bce0001398b61", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473322, "last_modified_by": "-10000", "last_modified_time": 1752486473322, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503886607}, {"id": "686e2e9ef847ab0001ac09fe", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2a38f847ab0001a9a51c", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed11e8829bce0001398b61", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473323, "last_modified_by": "-10000", "last_modified_time": 1752486473323, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503299230}, {"id": "686e2ef2f847ab0001ac2838", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "6836b28a938aa30001c8bead", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473324, "last_modified_by": "-10000", "last_modified_time": 1752486473324, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503891199}, {"id": "686e2f20f847ab0001ac330f", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2a73f847ab0001a9bd95", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473325, "last_modified_by": "-10000", "last_modified_time": 1752486473325, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503402063}, {"id": "686e2f2ff847ab0001ac3881", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "67ed1348829bce000139aec8", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473326, "last_modified_by": "-10000", "last_modified_time": 1752486473326, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503302259}, {"id": "686e2f4bf847ab0001ac4027", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "67ed1376829bce000139b7a9", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473327, "last_modified_by": "-10000", "last_modified_time": 1752486473327, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503303071}, {"id": "686e2f5ef847ab0001ac465e", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2a18f847ab0001a9995b", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473328, "last_modified_by": "-10000", "last_modified_time": 1752486473328, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503303572}, {"id": "686e2f6ff847ab0001ac503b", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2a38f847ab0001a9a51c", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1206829bce0001398f4f", "must_do": false, "task_order": 60, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473329, "last_modified_by": "-10000", "last_modified_time": 1752486473329, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503304112}, {"id": "686e2fb4f847ab0001ac8fd9", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "6836b368938aa30001c8cf05", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473330, "last_modified_by": "-10000", "last_modified_time": 1752486473330, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503820691}, {"id": "686e2fc5f847ab0001ac9ba2", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2a88f847ab0001a9c75b", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473331, "last_modified_by": "-10000", "last_modified_time": 1752486473331, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503441785}, {"id": "686e2fd2f847ab0001ac9f9a", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2a9af847ab0001a9cc2d", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473332, "last_modified_by": "-10000", "last_modified_time": 1752486473332, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503821295}, {"id": "686e2fe0f847ab0001aca537", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2a51f847ab0001a9b63b", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473333, "last_modified_by": "-10000", "last_modified_time": 1752486473333, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503891722}, {"id": "686e2fedf847ab0001aca9bf", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2a18f847ab0001a9995b", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473334, "last_modified_by": "-10000", "last_modified_time": 1752486473334, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503821983}, {"id": "686e2ffcf847ab0001acaec3", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2a38f847ab0001a9a51c", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836ad89a7c3f00001710a75", "must_do": false, "task_order": 60, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473335, "last_modified_by": "-10000", "last_modified_time": 1752486473335, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503443105}, {"id": "686e30f8f847ab0001acf57b", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "680f714b1607ea000175adb6", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1262829bce0001399d1b", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473336, "last_modified_by": "-10000", "last_modified_time": 1752486473336, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503892198}, {"id": "686e3106f847ab0001ad020e", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "680f71b51607ea000175b87e", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1262829bce0001399d1b", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473337, "last_modified_by": "-10000", "last_modified_time": 1752486473337, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503885980}, {"id": "686e31b0f847ab0001ad2e57", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1262829bce0001399d1b", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473338, "last_modified_by": "-10000", "last_modified_time": 1752486473338, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503443636}, {"id": "686e31bef847ab0001ad36e9", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d99f847ab0001abaf3f", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1262829bce0001399d1b", "must_do": false, "task_order": 60, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473339, "last_modified_by": "-10000", "last_modified_time": 1752486473339, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503304677}, {"id": "686e31cbf847ab0001ad3b07", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed1262829bce0001399d1b", "must_do": false, "task_order": 70, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473340, "last_modified_by": "-10000", "last_modified_time": 1752486473340, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503444218}, {"id": "686e31ecf847ab0001ad502f", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "680f71f01607ea000175c018", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed124b829bce00013998b3", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473341, "last_modified_by": "-10000", "last_modified_time": 1752486473341, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503305377}, {"id": "686e31f9f847ab0001ad58e7", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "6836b3e9938aa30001c8dda8", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed124b829bce00013998b3", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473342, "last_modified_by": "-10000", "last_modified_time": 1752486473342, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503306246}, {"id": "686e3206f847ab0001ad5e7f", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed124b829bce00013998b3", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473343, "last_modified_by": "-10000", "last_modified_time": 1752486473343, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503444717}, {"id": "686e3212f847ab0001ad62ca", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed124b829bce00013998b3", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473344, "last_modified_by": "-10000", "last_modified_time": 1752486473344, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503889928}, {"id": "686e3222f847ab0001ad6ede", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "67e37400d4bcec0001465996", "node_id": "67ed124b829bce00013998b3", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473345, "last_modified_by": "-10000", "last_modified_time": 1752486473345, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503892701}, {"id": "686e323af847ab0001ad74ab", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "6836b404938aa30001c8e011", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836aed1938aa30001c8898e", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473346, "last_modified_by": "-10000", "last_modified_time": 1752486473346, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503306734}, {"id": "686e3247f847ab0001ad7b09", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836aed1938aa30001c8898e", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473347, "last_modified_by": "-10000", "last_modified_time": 1752486473347, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503823337}, {"id": "686e3252f847ab0001ad7f90", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d99f847ab0001abaf3f", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836aed1938aa30001c8898e", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473348, "last_modified_by": "-10000", "last_modified_time": 1752486473348, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503307251}, {"id": "686e325df847ab0001ad8590", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836aed1938aa30001c8898e", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473349, "last_modified_by": "-10000", "last_modified_time": 1752486473349, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503322385}, {"id": "686e326ff847ab0001ad9088", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "6836b419938aa30001c8e218", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473350, "last_modified_by": "-10000", "last_modified_time": 1752486473350, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503323375}, {"id": "686e327bf847ab0001ad9630", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "6836b42b938aa30001c8e40e", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473351, "last_modified_by": "-10000", "last_modified_time": 1752486473351, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503445188}, {"id": "686e3285f847ab0001ad9a5f", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "6836b43e938aa30001c8e677", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473352, "last_modified_by": "-10000", "last_modified_time": 1752486473352, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503825794}, {"id": "686e3290f847ab0001ada148", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473353, "last_modified_by": "-10000", "last_modified_time": 1752486473353, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503893166}, {"id": "686e329df847ab0001adab97", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d99f847ab0001abaf3f", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473354, "last_modified_by": "-10000", "last_modified_time": 1752486473354, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503445684}, {"id": "686e32bff847ab0001adc670", "tenant_id": "93747", "name": "L2C管理流程任务", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "67e37400d4bcec0001465996", "node_id": "6836af01938aa30001c88da4", "must_do": false, "task_order": 60, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752486473355, "last_modified_by": "-10000", "last_modified_time": 1752486473355, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752486503893666}, {"id": "687787f5461ee02740de5b64", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685922a0ed27860001ebdb7b", "must_do": false, "task_order": 60, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053595, "last_modified_by": "-10000", "last_modified_time": 1752664053595, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063392631}, {"id": "687787f5461ee02740de5b65", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d99f847ab0001abaf3f", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685922a0ed27860001ebdb7b", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053596, "last_modified_by": "-10000", "last_modified_time": 1752664053596, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063649471}, {"id": "687787f5461ee02740de5b66", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685922a0ed27860001ebdb7b", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053597, "last_modified_by": "-10000", "last_modified_time": 1752664053597, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063352258}, {"id": "687787f5461ee02740de5b67", "tenant_id": "93747", "name": "商机管理流程", "task_id": "6836b43e938aa30001c8e677", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685922a0ed27860001ebdb7b", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053598, "last_modified_by": "-10000", "last_modified_time": 1752664053598, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063393171}, {"id": "687787f5461ee02740de5b68", "tenant_id": "93747", "name": "商机管理流程", "task_id": "6836b42b938aa30001c8e40e", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685922a0ed27860001ebdb7b", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053599, "last_modified_by": "-10000", "last_modified_time": 1752664053599, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063650494}, {"id": "687787f5461ee02740de5b69", "tenant_id": "93747", "name": "商机管理流程", "task_id": "6836b419938aa30001c8e218", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685922a0ed27860001ebdb7b", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053600, "last_modified_by": "-10000", "last_modified_time": 1752664053600, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063353040}, {"id": "687787f5461ee02740de5b6a", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "685920fbed27860001ebba4b", "node_id": "6859227fed27860001ebd5fa", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053601, "last_modified_by": "-10000", "last_modified_time": 1752664053601, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063393597}, {"id": "687787f5461ee02740de5b6b", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d99f847ab0001abaf3f", "methodology_id": "685920fbed27860001ebba4b", "node_id": "6859227fed27860001ebd5fa", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053602, "last_modified_by": "-10000", "last_modified_time": 1752664053602, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063651027}, {"id": "687787f5461ee02740de5b6c", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "685920fbed27860001ebba4b", "node_id": "6859227fed27860001ebd5fa", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053603, "last_modified_by": "-10000", "last_modified_time": 1752664053603, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063353510}, {"id": "687787f5461ee02740de5b6d", "tenant_id": "93747", "name": "商机管理流程", "task_id": "6836b404938aa30001c8e011", "methodology_id": "685920fbed27860001ebba4b", "node_id": "6859227fed27860001ebd5fa", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053604, "last_modified_by": "-10000", "last_modified_time": 1752664053604, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063394070}, {"id": "687787f5461ee02740de5b6e", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "685920fbed27860001ebba4b", "node_id": "68592257ed27860001ebd172", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053605, "last_modified_by": "-10000", "last_modified_time": 1752664053605, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063651515}, {"id": "687787f5461ee02740de5b6f", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "685920fbed27860001ebba4b", "node_id": "68592257ed27860001ebd172", "must_do": false, "task_order": 40, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053606, "last_modified_by": "-10000", "last_modified_time": 1752664053606, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063353935}, {"id": "687787f5461ee02740de5b70", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "685920fbed27860001ebba4b", "node_id": "68592257ed27860001ebd172", "must_do": false, "task_order": 30, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053607, "last_modified_by": "-10000", "last_modified_time": 1752664053607, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063394596}, {"id": "687787f5461ee02740de5b71", "tenant_id": "93747", "name": "商机管理流程", "task_id": "6836b3e9938aa30001c8dda8", "methodology_id": "685920fbed27860001ebba4b", "node_id": "68592257ed27860001ebd172", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053608, "last_modified_by": "-10000", "last_modified_time": 1752664053608, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063652035}, {"id": "687787f5461ee02740de5b72", "tenant_id": "93747", "name": "商机管理流程", "task_id": "680f71f01607ea000175c018", "methodology_id": "685920fbed27860001ebba4b", "node_id": "68592257ed27860001ebd172", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053609, "last_modified_by": "-10000", "last_modified_time": 1752664053609, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063354387}, {"id": "687787f5461ee02740de5b73", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d8df847ab0001aba83a", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685921d7ed27860001ebc41e", "must_do": false, "task_order": 70, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053610, "last_modified_by": "-10000", "last_modified_time": 1752664053610, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063395032}, {"id": "687787f5461ee02740de5b74", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d99f847ab0001abaf3f", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685921d7ed27860001ebc41e", "must_do": false, "task_order": 60, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053611, "last_modified_by": "-10000", "last_modified_time": 1752664053611, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063652494}, {"id": "687787f5461ee02740de5b75", "tenant_id": "93747", "name": "商机管理流程", "task_id": "686e2d7cf847ab0001ab8442", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685921d7ed27860001ebc41e", "must_do": false, "task_order": 50, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053612, "last_modified_by": "-10000", "last_modified_time": 1752664053612, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063355016}, {"id": "687787f5461ee02740de5b76", "tenant_id": "93747", "name": "商机管理流程", "task_id": "680f71b51607ea000175b87e", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685921d7ed27860001ebc41e", "must_do": false, "task_order": 20, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053613, "last_modified_by": "-10000", "last_modified_time": 1752664053613, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063395453}, {"id": "687787f5461ee02740de5b77", "tenant_id": "93747", "name": "商机管理流程", "task_id": "680f714b1607ea000175adb6", "methodology_id": "685920fbed27860001ebba4b", "node_id": "685921d7ed27860001ebc41e", "must_do": false, "task_order": 10, "step_by_step": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": null, "created_by": "-10000", "create_time": 1752664053614, "last_modified_by": "-10000", "last_modified_time": 1752664053614, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "NodeTaskObj", "version": 1, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "85884f97657d7755", "change_type": null, "out_data_auth_code": "73efa2fafc7727cd", "order_by": null, "data_auth_id": 7800756, "out_data_auth_id": 631552, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1752664063653258}]