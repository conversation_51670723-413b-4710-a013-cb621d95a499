//配置项print_one_to_many_ref_support_config用于配置某个主对象打印支持的一对多的关联对象的对象列表。目前的需求是客户支持关联: 订单、联系人、线索、商机，销售订单打印模板支持关联：  回款信息 。 联系人：销售线索。商机：销售线索、订单、联系人、产品和拜访。
print_one_to_many_ref_support_config={"AccountObj":["SalesOrderObj","ContactObj","LeadsObj","OpportunityObj"],"SalesOrderObj":["PaymentObj","ContractObj","RefundObj","RefundObj","InvoiceApplicationObj","VisitingObj"],"ContactObj":["LeadsObj"],"OpportunityObj":["LeadsObj","SalesOrderObj","ContactObj","ProductObj","VisitingObj"],"ContractObj":[]}

//配置项print_lookup_field_support_config用于配置某个主对象打印支持的LookUP类型字段列表。目前的需求是，线索支持：客户，联系人，商机。 客户：无。销售订单支持：客户，商机，收货人（联系人）。产品支持：无。联系人支持：客户，介绍人（联系人）。回款支持：客户，销售订单，合同支持：客户
print_lookup_field_support_config={"LeadsObj":["account_id","opportunity_id","contact_id"],"SalesOrderObj":["account_id","opportunity_id","ship_to_id"],"ContactObj":["account_id","introducer"],"PaymentObj":["account_id","order_id"],"ProductObj":[""],"ContractObj":["account_id"]}

//print_hidden_field_config 用来配置需要被隐藏的字段
print_hidden_field_config={"AccountObj":["pin_yin","last_deal_closed_time","last_deal_closed_amount","total_refund_amount","high_seas_id","_id","package","tenant_id","object_describe_id","object_describe_api_name","create_time","created_by","is_deleted","version","record_type","filling_checker_id","is_remind_recycling"],"LeadsObj":["_id","package","tenant_id","object_describe_id","object_describe_api_name","is_deleted","version","record_type","picture_path"],"SalesOrderObj":["_id","package","is_user_define_work_flow","tenant_id","object_describe_id","object_describe_api_name","is_deleted","version","record_type"],"ContactObj":["_id","package","tenant_id","object_describe_id","object_describe_api_name","is_deleted","version","record_type"],"PaymentObj":["_id","package","tenant_id","object_describe_id","object_describe_api_name","is_deleted","version","record_type"],"ProductObj":["_id","package","tenant_id","object_describe_id","object_describe_api_name","is_deleted","version","record_type"],"OpportunityObj":["_id","package","tenant_id","object_describe_id","object_describe_api_name","is_deleted","version","record_type","is_start_after_sale","is_bind_after_sale","sales_process_id","oppo_stage_id"],"ReturnedGoodsInvoiceObj":["is_user_define_work_flow","is_unread"]}
//print_hidden_field_config_custom用来所有自定义对象配置需要被隐藏的字段
print_hidden_field_config_custom=["relevant_team","lock_rule","lock_user","life_status_before_invalid","sign_in_info__c"]

//配置需要隐藏的describe类型，main代表主对象隐藏，reference代表主对象1对1关联的对象隐藏类型，oneToMany代表主对象1对多关联的对象隐藏类型
print_hidden_field_type_describe={"main":["image","group"],"reference":["image","object_reference","master_detail","embedded_object_list","group"],"oneToMany":["image","embedded_object_list","object_reference","master_detail","group"]}

#控制那些老对象可以打印
oldObjectPrintTemplateListInfo=SalesOrderObj:订单打印模板,AccountObj:客户打印模板,ProductObj:产品打印模板,ContactObj:联系人打印模板,LeadsObj:销售线索打印模板,PaymentObj:回款打印模板,RefundObj:退款打印模板,ContractObj:合同打印模板

#打印模板支持打印的老对象列表信息，包括apiname、个数控制标识等字段
print_support_oldApiName_ListInfo={"SalesOrderObj":{"title":"销售订单"},"AccountObj":{"title":"客户"},"ProductObj":{"title":"产品"},"ContactObj":{"title":"联系人"},"LeadsObj":{"title":"销售线索"},"PaymentObj":{"title":"回款"},"RefundObj":{"title":"退款"},"ContractObj":{"title":"合同"},"InvoiceApplicationObj":{"title":"开票申请"},"ReturnedGoodsInvoiceObj":{"title":"退货单"},"OpportunityObj":{"title":"商机"}}

#文件服务器的本地根访问地址
fileServer_localBasepath=http://10.113.32.20:8028
#html中字符串的替换规则，regex代表要替换的表达式，replace代表要替换的字符串，112地址和线上暂时无法使用,等测试时，找光哥沟通地址问题
html_replace_rule=[{"regex":"https://www.ceshi113.com/html",replace:"https://intranet.ceshi113.com/html"}]
#二维码生成时的加密key
qQir_key=Zc-Zr525]5xQgT>{