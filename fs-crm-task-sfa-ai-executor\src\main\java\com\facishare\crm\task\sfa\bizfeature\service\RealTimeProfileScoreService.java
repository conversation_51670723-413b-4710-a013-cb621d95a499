package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyInstanceConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileConstants;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileAdviceMqModel;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileScoreModel;
import com.facishare.crm.task.sfa.util.ProfileUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Component
@Slf4j
public class RealTimeProfileScoreService extends ProfileScoreService {

    /**
     * 实时计算
     */
    @Override
    public void scoreCalc(ProfileScoreModel profileScoreModel) {
        log.info("realtime profile scoreCalc, profileScoreModel:{}", profileScoreModel);
        User user = User.systemUser(profileScoreModel.getTenantId());
        if (StringUtils.isNotEmpty(profileScoreModel.getUserId())) {
            user = new User(profileScoreModel.getTenantId(), profileScoreModel.getUserId());
        }
        SearchTemplateQueryPlus methodologyInstanceQuery = SearchUtil.buildBaseSearchQuery();
        String filterField = ProfileUtil.parseFilterFiled(profileScoreModel.getObjectApiName());
        if (StringUtils.isBlank(filterField)) {
            log.warn("filterField is null, tenantId:{}", user.getTenantId());
            return;
        }
        SearchTemplateQueryExt.of(methodologyInstanceQuery).addFilter(Operator.EQ, filterField, profileScoreModel.getObjectId());
        SearchTemplateQueryExt.of(methodologyInstanceQuery).addFilter(Operator.EQ, MethodologyInstanceConstants.STATUS, MethodologyInstanceConstants.StatusType.ENABLE.getStatusType());
        if (StringUtils.isNotBlank(profileScoreModel.getMethodologyInstanceId())) {
            SearchTemplateQueryExt.of(methodologyInstanceQuery).addFilter(Operator.EQ, DBRecord.ID, profileScoreModel.getMethodologyInstanceId());
        }
        List<IObjectData> methodologyInstanceList = serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.METHODOLOGY_INSTANCE, methodologyInstanceQuery).getData();
        if (CollectionUtils.isEmpty(methodologyInstanceList)) {
            log.info("methodologyInstanceList is empty, tenantId:{}", user.getTenantId());
            return;
        }
        Map<String, List<String>> methodologyToObjectApiNameMap = Maps.newHashMap();
        for (IObjectData methodologyInstance : methodologyInstanceList) {
            methodologyToObjectApiNameMap.put(methodologyInstance.getId(), Lists.newArrayList(profileScoreModel.getObjectApiName()));
        }
        commonScoreCalc(user, methodologyToObjectApiNameMap, methodologyInstanceList);
    }

    /**
     * 保存画像数据
     */
    @Override
    public void saveProfileDate(User user, List<IObjectData> addProfileList, List<IObjectData> updateProfileList,
                                List<IObjectData> addProfileItemScoreList) {
        // 实时计算
        if (CollectionUtils.isNotEmpty(addProfileList)) {
            addProfileQuotaLimit(user, addProfileList);
        }
        if (CollectionUtils.isNotEmpty(updateProfileList)) {
            IActionContext actionContext = ActionContextExt.of(user).getContext();
            actionContext.put("specify_time", true);
            actionContext.put("skip_remove_not_change_data", true);
            serviceFacade.batchUpdate(actionContext, updateProfileList, user);
            List<String> profileIds = updateProfileList.stream().map(DBRecord::getId).collect(Collectors.toList());
            // 删除历史画像各项得分
            specialTableMapper.setTenantId(user.getTenantId()).
                    deleteBySql(String.format("delete from biz_profile_item_score where tenant_id = '%s' and profile_id in ('%s') ",
                            user.getTenantId(), Joiner.on("','").join(profileIds)));
        }
        // 保存新画像各项得分
        if (CollectionUtils.isNotEmpty(addProfileItemScoreList)) {
            serviceFacade.bulkSaveObjectData(addProfileItemScoreList, user);
        }
    }

    @Override
    public void processAddOrUpdateProfile(IObjectData profile, IObjectData existProfile, List<IObjectData> addProfileList, List<IObjectData> updateProfileList) {
        if (existProfile != null) {
            existProfile.setLastModifiedTime(System.currentTimeMillis());
            updateProfileList.add(existProfile);
        } else {
            addProfileList.add(profile);
        }
    }

    @Override
    public IObjectData buildProfileData(User user, String methodologyInstanceId, String methodologyId, String objectApiName,
                                        String objectId, IObjectData existProfile) {
        if (existProfile != null) {
            return existProfile;
        }
        return super.buildProfileData(user, methodologyInstanceId, methodologyId, objectApiName, objectId, null);
    }

    @Override
    public void sendProfileAdvice(User user, List<IObjectData> addProfileList, List<IObjectData> updateProfileList) {
        Stream.concat(
                CollectionUtils.isEmpty(addProfileList) ? Stream.empty() : addProfileList.stream(),
                CollectionUtils.isEmpty(updateProfileList) ? Stream.empty() : updateProfileList.stream()
        ).forEach(profile -> {
            ProfileAdviceMqModel.Message profileAdviceMqModel = ProfileAdviceMqModel.Message.builder()
                    .tenantId(user.getTenantId())
                    .profileId(profile.getId())
                    .refreshType(ProfileConstants.RefreshType.REALTIME.getValue())
                    .receiverId(user.getUserId())
                    .build();
            profileAdviceProducer.sendMessage(profileAdviceMqModel);
            log.info("send profile advice, profileAdviceMqModel:{}", profileAdviceMqModel);
        });

    }
}
