package com.facishare.crm.task.sfa.activitysummary.service.paragraph;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTagMessage;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

@Slf4j
@Component
public class ParagraphProducer {

    private static final String MQ_PRODUCER_CONFIG_NAME = "fs-crm-task-sfa-mq.ini";
    private static final String MQ_PRODUCER_CONFIG_SECTION_NAME = "sfa-ai-activity-label-producer";
    public static final String TAG_NAME = "mark";
    public static final String TAG_FEATURE = "feature";

    private AutoConfMQProducer producer;

    @PostConstruct
    public void init() {
        producer = new AutoConfMQProducer(MQ_PRODUCER_CONFIG_NAME, MQ_PRODUCER_CONFIG_SECTION_NAME);
    }

    @PreDestroy
    public void destroy() {
        if (producer != null) {
            producer.close();
        }
    }

    /**
     * 发送消息
     *
     * @param activityMessage 活动消息对象
     */
    public void sendMessage(ActivityTagMessage activityMessage) {
        doSendMessage(activityMessage, TAG_NAME);
    }

    private void doSendMessage(ActivityTagMessage activityMessage, String tagName) {
        if (activityMessage == null) {
            log.error("活动标注对象不能为空");
            return;
        }
        if (StringUtils.isBlank(activityMessage.getTenantId())) {
            log.error("租户ID不能为空, message: {}", activityMessage);
            return;
        }

        JSONObject messageObject = (JSONObject) JSON.toJSON(activityMessage);
        sendMessage(tagName, messageObject, activityMessage.getTenantId());
    }

    /**
     * 发送消息
     *
     * @param activityMessage 活动消息对象
     */
    public void sendMessage(String tag, ActivityTagMessage activityMessage) {
        doSendMessage(activityMessage, tag);
    }


    /**
     * 通用的消息发送方法
     *
     * @param tag           消息标签
     * @param messageObject 消息内容
     * @param tenantId      租户ID
     */
    public void sendMessage(String tag, JSONObject messageObject, String tenantId) {
        if (StringUtils.isBlank(tag)) {
            log.error("消息tag不能为空，tenantId: {}, message: {}", tenantId, messageObject);
            return;
        }

        Message message = getMessage(tag, messageObject, tenantId);
        try {
            SendResult sendResult = producer.send(message);
            if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                log.error("发送消息失败，tag: {}, tenantId: {}, message: {}, status: {}",
                        tag, tenantId, messageObject, sendResult.getSendStatus());
                return;
            }
            log.info("发送消息成功，tag: {}, tenantId: {}, message: {}", tag, tenantId, messageObject);
        } catch (Exception e) {
            log.error("发送消息异常，tag: {}, tenantId: {}, message: {}", tag, tenantId, messageObject, e);
        }
    }

    /**
     * 组装消息
     */
    private Message getMessage(String tags, JSONObject messageObject, String tenantId) {
        String messageString = JSON.toJSONString(messageObject);
        Message message = new Message(producer.getDefaultTopic(), tags, messageString.getBytes());
        message.putUserProperty("x-fs-ei", tenantId);
        message.setTags(tags);
        return message;
    }
}