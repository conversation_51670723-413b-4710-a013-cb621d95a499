package com.facishare.crm.task.sfa.service;

import com.facishare.crm.sfa.lto.activity.mongo.InteractionStrategyTaskDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * 策略任务服务接口
 */
public interface StrategyTaskService {

    /**
     * 查询策略
     *
     * @param user 用户
     * @param strategyId 策略ID
     * @return 策略对象
     */
    IObjectData queryStrategy(User user, String strategyId);

    /**
     * 查询策略明细
     *
     * @param user 用户
     * @param strategyId 策略ID
     * @return 策略明细列表
     */
    List<IObjectData> queryStrategyDetails(User user, String strategyId);

    /**
     * 统计符合策略条件的客户数量
     *
     * @param user 用户
     * @param strategy 策略对象
     * @return 客户数量
     */
    Integer countCustomersByStrategy(User user, IObjectData strategy);

    /**
     * 处理策略下的客户数据
     *
     * @param user 用户
     * @param strategy 策略对象
     * @param strategyDetails 策略明细列表
     * @param task 任务对象
     * @return 处理的客户数量
     */
    int processCustomersForStrategy(User user, IObjectData strategy, List<IObjectData> strategyDetails, InteractionStrategyTaskDocument task);
} 