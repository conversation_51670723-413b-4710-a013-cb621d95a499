package com.facishare.crm.web;

import com.facishare.crm.task.sfa.services.TermBankConfusableService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("term_bank_confusable")
public class TermBankConfusableController {
    private final TermBankConfusableService termBankConfusableService;

    @PostMapping("save")
    public Object save(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId, @RequestBody List<TermBankConfusableService.Entry> list) {
        termBankConfusableService.save(tenantId, list);
        return "OK";
    }

    @PostMapping("deleteByIds")
    public Object deleteByIds(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId, @RequestBody List<String> ids) {
        termBankConfusableService.deleteByIds(tenantId, ids);
        return "OK";
    }
}
