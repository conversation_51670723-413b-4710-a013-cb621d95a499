package com.facishare.crm.task.sfa.util;

import com.facishare.crm.task.sfa.common.enums.ConfigType;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * Created by renlb on 2019/1/17.
 */
public class SFAConfigUtil {

    private static final ConfigService configService = SpringUtil.getContext().getBean(ConfigService.class);

    public static String getConfigValue(String tenantId, String configKey, String userId) {
        String queryRst = getConfigValueNotDefaultValue(tenantId, configKey, userId);
        if (StringUtils.isBlank(queryRst)) {
            return getDefaultConfigValue(configKey);
        } else {
            return queryRst;
        }
    }

    public static String getDefaultConfigValue(String key) {
        if (ConfigType.getConfigType(key).equals(ConfigType.NONE)) {
            return "";
        } else {
            return ConfigType.getConfigType(key).getDefaultValue();
        }
    }

    public static String getConfigValueNotDefaultValue(String tenantId, String configKey, String userId) {
        User user = new User(tenantId, userId);
        return configService.findTenantConfig(user, configKey);
    }

    private static final String OPEN = "1";

    public static boolean isOpenCustomerProfileAgent(String tenantId) {
        return Objects.equals(OPEN, SFAConfigUtil.getConfigValue(tenantId, ConfigType.CUSTOMER_PROFILE_AGENT.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean isOpenPricePolicy(String tenantId) {
        return Objects.equals(OPEN, SFAConfigUtil.getConfigValue(tenantId, ConfigType.PRICE_POLICY.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean isOpenPricePolicySalesOrder(String tenantId) {
        return Objects.equals(OPEN, SFAConfigUtil.getConfigValue(tenantId, ConfigType.PRICE_POLICY_SALES_ORDER_OBJ.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean isOpenPricePolicyQuote(String tenantId) {
        return Objects.equals(OPEN, SFAConfigUtil.getConfigValue(tenantId, ConfigType.PRICE_POLICY_QUOTE_OBJ.getKey(), User.SUPPER_ADMIN_USER_ID));
    }


    public static boolean isOpenRebate(String tenantId) {
        return Objects.equals(OPEN, SFAConfigUtil.getConfigValue(tenantId, ConfigType.REBATE.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean isOpenCoupon(String tenantId) {
        return Objects.equals(OPEN, SFAConfigUtil.getConfigValue(tenantId, ConfigType.COUPON.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean isOpenBomDuplicateCheck(String tenantId) {
        return Objects.equals(OPEN, SFAConfigUtil.getConfigValue(tenantId, ConfigType.BOM_DUPLICATE_CHECK.getKey(), User.SUPPER_ADMIN_USER_ID));
    }

    public static boolean isOpenAutoMatch(String tenantId) {
        return Objects.equals(OPEN, SFAConfigUtil.getConfigValue(tenantId, ConfigType.IS_OPEN_AUTO_MATCH.getKey(), User.SUPPER_ADMIN_USER_ID));
    }
}
