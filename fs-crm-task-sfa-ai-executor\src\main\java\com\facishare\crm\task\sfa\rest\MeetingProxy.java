package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.task.sfa.rest.dto.ActivityMeetingModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "FS-CRM-MEETING", desc = "Meeting", contentType = "application/json")
public interface MeetingProxy {

    @POST(value = "/activity_meeting/update_by_schedule", desc = "通过日程变更更新会议")
    ActivityMeetingModel.Result<String> updateBySchedule(@Body ActivityMeetingModel.UpdateByScheduleArg arg, @HeaderMap Map<String, String> headers);

}
