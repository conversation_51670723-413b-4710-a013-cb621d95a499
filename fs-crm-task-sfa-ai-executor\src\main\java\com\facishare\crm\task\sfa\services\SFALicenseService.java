package com.facishare.crm.task.sfa.services;

import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.exception.LicenseException;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.Result.ParaInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.arg.QueryModuleParaArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.exception.PaasMessage;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.google.common.collect.Sets;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/12/9 16:42
 */

@Component
@Slf4j
public class SFALicenseService {

    @Autowired
    private LicenseClient licenseClient;

    public boolean checkModuleLicenseExist(String tenantId, String packageName) {
        QueryModuleArg queryModuleArg = new QueryModuleArg();
        LicenseContext licenseContext = getLicenseContext(tenantId);
        queryModuleArg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(queryModuleArg);
        if (result == null) {
            return false;
        }
        List<ModuleInfoPojo> modules = result.getResult();
        if (modules != null && modules.stream().anyMatch(module -> Objects.equals(module.getModuleCode(), packageName))) {
            return true;
        }
        return false;
    }

    @NotNull
    private LicenseContext getLicenseContext(String tenantId) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(tenantId);
        licenseContext.setUserId(User.SUPPER_ADMIN_USER_ID);
        return licenseContext;
    }



    public boolean checkModuleLicenseExist(String tenantId, List<String> packageNames) {
        QueryModuleArg queryModuleArg = new QueryModuleArg();
        LicenseContext licenseContext = getLicenseContext(tenantId);
        queryModuleArg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(queryModuleArg);
        if (result == null) {
            return false;
        }
        List<ModuleInfoPojo> modules = result.getResult();
        if (modules != null && modules.stream().anyMatch(module -> packageNames.contains(module.getModuleCode()))) {
            return true;
        }
        return false;
    }
    public boolean queryProductVersionByKey(String tenantId,String key)  {
        QueryProductArg queryProductArg = new QueryProductArg();
        LicenseContext licenseContext = getLicenseContext(tenantId);
        queryProductArg.setLicenseContext(licenseContext);
        LicenseVersionResult result = licenseClient.queryProductVersion(queryProductArg);
        if (result == null && CollectionUtils.isEmpty(result.getResult())) {
            return false;
        }
        if (result.getResult().stream()
                .anyMatch(productVersionPojo -> Objects.equals(productVersionPojo.getCurrentVersion(), key))) {
            return true;
        }
        return false;
    }

    public int getQuotaByModule(String tenantId, String paraKey) {
        LicenseContext context = getLicenseContext(tenantId);
        QueryModuleParaArg arg = new QueryModuleParaArg();
        arg.setContext(context);
        arg.setParaKeys(Sets.newHashSet(paraKey));
        ParaInfoResult paraInfoResult = licenseClient.queryModulePara(arg);

        if (Objects.isNull(paraInfoResult)) {
            throw new APPException("license service error");
        }

        if (!Objects.equals(paraInfoResult.getErrCode(), PaasMessage.SUCCESS.getCode())) {
            log.error("queryModulePara error,arg:{},result:{}", arg, paraInfoResult);
            throw new LicenseException(paraInfoResult.getErrMessage());
        }

        return CollectionUtils.isEmpty(paraInfoResult.getResult()) ? 0 :
                Integer.parseInt(paraInfoResult.getResult().get(0).getParaValue());
    }
}
