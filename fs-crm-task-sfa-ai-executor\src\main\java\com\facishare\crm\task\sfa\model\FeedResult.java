package com.facishare.crm.task.sfa.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/11 12:29
 * @description:
 */
public interface FeedResult {


    @Data
    class Result {
        List<FeedList> feedList;
        boolean hasMore;
        JSONObject nextPageArg;
    }

    @Data
    class FeedList {
        public String feedId;
        public String feedTitle;
        public Integer feedType;
        public JSONObject feedProfile;
        public JSONObject feedStatus;
        public List<JSONObject> bizDataArea;
        public JSONObject feedTabStatus;
        public JSONObject actionBar;
        public String feedPresentation;
    }
}