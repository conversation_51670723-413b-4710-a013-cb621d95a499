package com.facishare.crm.task.sfa.bizfeature.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureMqModel;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureEngineService;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * MQ特征计算监听
 */
@Slf4j
@Component
public class FeatureValueListener implements ApplicationListener<ContextRefreshedEvent> {

    @Resource
    private FeatureEngineService featureEngineService;

    private AutoConfMQPushConsumer consumer;

    private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "crm-feature-value-consumer",
                (MessageListenerConcurrently) (msgs, context) -> {
                    for (MessageExt msg : msgs) {
                        try {
                            FeatureMqModel.Message message = messageParse(msg);
                            consume(message);
                        } catch (Exception e) {
                            log.error("FeatureValueListener consume error, msg:{}", msg, e);
                            //throw new RuntimeException(e);
                        }
                    }
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                });
    }

    private void consume(FeatureMqModel.Message message) {
        if (message == null || StringUtils.isBlank(message.getTenantId())
                || StringUtils.isBlank(message.getObjectId())
                || StringUtils.isBlank(message.getType())
                || StringUtils.isBlank(message.getObjectApiName())) {
            log.error("featureValue error, feature parm:{}", message);
            return;
        }
        if (gray.isAllow("feature-value-skip-tenant", message.getTenantId())) {
            return;
        }
        if (FeatureMqModel.MessageType.TAG.getMessageType().equals(message.getType())) {
            featureEngineService.generateTag(message);
        } else if (FeatureMqModel.MessageType.PARTICIPANT.getMessageType().equals(message.getType())
                || FeatureMqModel.MessageType.REQUIREMENT.getMessageType().equals(message.getType())) {
            featureEngineService.generateActivity(message);
        }
    }

    private FeatureMqModel.Message messageParse(MessageExt messageExt) {
        try {
            return JSON.parseObject(StringHelper.toString(messageExt.getBody()), FeatureMqModel.Message.class);
        } catch (Exception e) {
            log.error("feature value message format failed. msgId:{}, body:{}", messageExt.getMsgId(),
                    StringHelper.toString(messageExt.getBody()));
        }
        return null;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

}