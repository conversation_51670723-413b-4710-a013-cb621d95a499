package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.model.ProfileScoreModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

public interface IProfileScoreService {

    void scoreCalc(ProfileScoreModel profileScoreModel);

    void saveProfileDate(User user, List<IObjectData> addProfileList, List<IObjectData> updateProfileList,
                         List<IObjectData> addProfileItemScoreList);

    void processAddOrUpdateProfile(IObjectData profile, IObjectData existProfile, List<IObjectData> addProfileList,
                                   List<IObjectData> updateProfileList);

    IObjectData buildProfileData(User user, String methodologyInstanceId, String methodologyId, String objectApiName,
                                 String objectId, IObjectData existProfile);
}
