package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 知识文档常量
 *
 * <AUTHOR>
 */
public interface KnowledgeDocumentConstants {
	/**
	 * 文档名称
	 */
	String NAME = "name";
	/**
     * 分类
     */
	String CATEGORY = "category";
	/**
     * 分类路径
     */
	String CATEGORY_PATH = "category_path";
	/**
     * 特征
     */
	String FEATURE = "feature";
	/**
     * 文档附件
     */
	String FILE = "file";
	/**
     * 类型
     */
	String TYPE = "type";
	enum KnowledgeType {
		/**
         * 模版
         */
		TEMPLATE("template") ,
		/**
         * 建议
         */
		SUGGESTION("suggestion") ;
		private final String type;

		public String getType() {
            return type;
        }


		KnowledgeType(String type) {
            this.type = type;
        }
	}
	/**
     * 摘要
     */
	String SUMMARY = "summary";
	/**
     * 负责人所在部门
     */
	String OWNER_DEPARTMENT = "owner_department";
	/**
     * 相关团队
     */
	String RELEVANT_TEAM = "relevant_team";
}