package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 方法论常量
 *
 * <AUTHOR>
 */
public interface MethodologyConstants {
    String API_NAME = "MethodologyObj";
    /**
     * 优先级
     */
    String PRIORITY = "priority";

    /**
     * 类型
     */
    String TYPE = "type";

    /**
     * 关联方法论
     */
    String METHODOLOGY_IDS = "methodology_ids";
    /**
     * 负责人所在部门
     */
    String OWNER_DEPARTMENT = "owner_department";
    /**
     * 相关团队
     */
    String RELEVANT_TEAM = "relevant_team";

    /**
     * 状态
     */
    String STATUS = "status";

    /**
     * 临时实例id
     */
    String TEMP_INSTANCE = "temp_instance";

    enum Type {
        /**
         * 流程
         */
        FLOW("flow"),
        /**
         * 画像
         */
        PROFILE("profile");

        private final String type;

        public String getType() {
            return type;
        }

        Type(String type) {
            this.type = type;
        }
    }

    /**
     * 已发布
     */
    String STATUS_PUBLISHED = "published";
    /**
     * 未发布
     */
    String STATUS_UNPUBLISHED = "unpublished";
    /**
     * 描述
     */
    String REMARK = "remark";
    /**
     * 节点级别
     */
    String STAGE_LEVEL = "stage_level";
    enum StageLevelType {
        /**
         * 一级
         */
        ONE("1"),
        /**
         * 二级
         */
        TWO("2"),
        /**
         * 三级
         */
        THREE("3"),
        /**
         * 四级
         */
        FOUR("4"),
        /**
         * 五级
         */
        FIVE("5"),
        /**
         * 六级
         */
        SIX("6");

        private final String level;

        public String getStageLevelType() {
            return level;
        }

        StageLevelType(String level) {
            this.level = level;
        }
    }

    /**
     * 流程概览
     */
    String PROCESS_OVERVIEW = "process_overview";

    String C139 = "C139";

    /**
     * 展示层级
     */
    String SHOW_LEVEL = "show_level";
}