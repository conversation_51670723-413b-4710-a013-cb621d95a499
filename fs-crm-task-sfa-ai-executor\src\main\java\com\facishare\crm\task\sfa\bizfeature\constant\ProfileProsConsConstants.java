package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 画像分项分数
 */
public interface ProfileProsConsConstants {
    /**
     * 画像
     */
    String PROFILE_ID = "profile_id";
    /**
     * 类型
     */
    String TYPE = "type";
    enum Type {
        /**
         * 优势
         */
        PROS("pros"),
        /**
         * 劣势
         */
        CONS("cons"),
        /**
         * 风险
         */
        RISK("risk")
        ;

        private final String value;

        public String getValue() {
            return value;
        }

        Type(String value) {
            this.value = value;
        }
    }
    /**
     * 分析范围
     */
    String RANGE_TYPE = "range_type";
    enum RangeType {
        /**
         * 画像
         */
        PROFILE("profile"),
        /**
         * 维度
         */
        DIMENSION("dimension")
        ;

        private final String value;

        public String getValue() {
            return value;
        }

        RangeType(String value) {
            this.value = value;
        }
    }
    /**
     * 维度
     */
    String FEATURE_DIMENSION_ID = "feature_dimension_id";
    /**
     * 信息
     */
    String INFORMATION = "information";
    /**
     * 序号
     */
    String SEQ = "seq";
    /**
     * 是否采纳
     */
    String IS_ACCEPT = "is_accept";
    /**
     * 是否忽略
     */
    String IS_IGNORE = "is_ignore";
    /**
     * 特殊数组
     */
    String FEATURE_IDS = "feature_ids";

    String VIRTUAL_SEQ = "virtual_seq";
    String VIRTUAL_TYPE = "virtual_type";
    String VIRTUAL_SCORE = "virtual_score";
    String VIRTUAL_WEIGHT = "virtual_weight";
    String VIRTUAL_INFORMATION = "virtual_information";
}