package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler;

import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;

public interface InsightHandler {

    String getInsightType();

    void insight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage);

    void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage);

}
