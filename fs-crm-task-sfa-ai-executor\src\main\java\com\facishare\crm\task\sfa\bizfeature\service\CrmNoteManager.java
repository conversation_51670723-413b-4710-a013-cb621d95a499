package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.constant.ProfileConstants;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureCrmNoteContext;
import com.facishare.crm.task.sfa.common.util.CRMRecordUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Lists;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Component
public class CrmNoteManager {
    @Resource
    private CRMNotificationServiceImpl crmNotificationService;
    @Resource
    private ServiceFacade serviceFacade;
    public void sendCrmNote(FeatureCrmNoteContext featureCrmNoteContext) {
        if (!Objects.equals(ProfileConstants.RefreshType.REALTIME.getValue(), featureCrmNoteContext.getRefreshType())) {
            return;
        }
        String title = "画像生成完毕";
        String internationalTitle = "sfa.profile.generate.finish";
        if (!featureCrmNoteContext.isSucess()) {
            title = "画像生成失败";
            internationalTitle = "sfa.profile.generate.error";
        }
        String objectName = Strings.EMPTY;
        Map<String, String> nameMap = serviceFacade.findNameByIds(User.systemUser(featureCrmNoteContext.getTenantId())
                , featureCrmNoteContext.getApiName(), Lists.newArrayList(featureCrmNoteContext.getObjectId()));
        if (CollectionUtils.notEmpty(nameMap)) {
            objectName = nameMap.get(featureCrmNoteContext.getObjectId());
        }
        String fullContentInternationalParameters = I18N.text(featureCrmNoteContext.getApiName() + ".attribute.self.display_name") + ":" + objectName;
        String fullContent = I18N.text("sfa.profile.generate.info",fullContentInternationalParameters);
        CRMRecordUtil.sendNewCRMRecord(
                crmNotificationService,
                User.systemUser(featureCrmNoteContext.getTenantId()),
                NewCrmNotification.CUSTOM_REMIND_RECORD_TYPE,
                Lists.newArrayList(featureCrmNoteContext.getReceiverIds()),
                User.SUPPER_ADMIN_USER_ID,
                title,
                fullContent,
                internationalTitle,
                null,
                "sfa.profile.generate.info",
                Lists.newArrayList(fullContentInternationalParameters),
                CRMRecordUtil.getUrlParameter(featureCrmNoteContext.getApiName(), featureCrmNoteContext.getObjectId())
        );
    }

    public void sendFeatureInitNote(String tenantId,String receiverId,boolean isSuccess) {
        String title = "销售Agent开启成功";
        String internationalTitle = "sfa.init.feature.finish";
        if (!isSuccess) {
            title = "销售Agent开启失败";
            internationalTitle = "sfa.init.feature.error";
        }
        CRMRecordUtil.sendNewCRMRecord(
                crmNotificationService,
                User.systemUser(tenantId),
                NewCrmNotification.CUSTOM_REMIND_RECORD_TYPE,
                Lists.newArrayList(Integer.valueOf(receiverId)),
                User.SUPPER_ADMIN_USER_ID,
                title,
                title,
                internationalTitle,
                null,
                internationalTitle,
                null,
                null
        );
    }
}
