package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 流程节点常量
 *
 * <AUTHOR>
 */
public interface MethodologyNodeConstants {
	String API_NAME = "MethodologyNodeObj";
	/**
	 * 方法论
	 */
	String METHODOLOGY_ID = "methodology_id";
	/**
	 * 匹配对象
	 */
	String OBJECT_API_NAME = "object_api_name";
	/**
	 * 流程语义匹配规则
	 */
	String SUMMARY = "summary";
	/**
	 * 是否行为节点
	 */
	String IS_ACTION = "is_action";
	/**
	 * 节点完成规则
	 */
	String COMPLETION_RULE = "completion_rule";
	/**
	 * 节点完成状态
	 */
	String STATUS = "status";

	enum StatusType {
		/**
		 * 禁用
		 */
		DISABLE("0"),
		/**
		 * 启用
		 */
		ENABLE("1");

		private final String status;

		public String getStatusType() {
			return status;
		}

		StatusType(String status) {
			this.status = status;
		}
	}

	/**
	 * 流程根节点
	 */
	String ROOT_ID = "root_id";
	/**
	 * 流程父节点
	 */
	String PARENT_ID = "parent_id";
	/**
	 * 流程路径
	 */
	String TREE_PATH = "tree_path";

	/**
	 * 顺序
	 */
	String NODE_ORDER = "node_order";

	/**
	 * 层级
	 */
	String LEVEL = "level";
	/**
	 * 负责人所在部门
	 */
	String OWNER_DEPARTMENT = "owner_department";
	/**
	 * 相关团队
	 */
	String RELEVANT_TEAM = "relevant_team";

	/**
	 * 简称
	 */
	String SHORT_NAME = "short_name";
}