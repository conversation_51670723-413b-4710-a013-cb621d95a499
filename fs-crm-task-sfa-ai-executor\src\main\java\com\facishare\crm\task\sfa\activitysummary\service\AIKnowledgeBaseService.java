package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.api.agent.model.Execute;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.model.service.FsAI;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.task.sfa.activitysummary.model.RequirementModel;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.rest.AiRestProxy;
import com.facishare.crm.task.sfa.util.ActivityUtils;
import com.facishare.eservice.base.result.EserviceResult;
import com.facishare.eservice.rest.common.Arg1;
import com.facishare.eservice.rest.common.HeaderObj;
import com.facishare.eservice.rest.online.model.SearchSceneKnowledgeModel;
import com.facishare.eservice.rest.online.service.KnowledgeService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class AIKnowledgeBaseService {
    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired(required=true)
    private KnowledgeService knowledgeService;
    @Autowired
    private AiRestProxy aiRestProxy;
    @Resource
    private CompletionsService completions;
    @Autowired
    public ActivitySummaryService activitySummaryService;
    @Autowired
    private LicenseClient licenseClient;

    /**知识库的场景*/
    public static String KNOWLEDGE_BASE_FIND_SCENE = "";

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            KNOWLEDGE_BASE_FIND_SCENE = config.get("knowledge_base_find_scene", "");
        });
    }
    public SearchSceneKnowledgeModel.SearchResult proxyKnowledgeService(User user, String searchWord){
        HeaderObj header = HeaderObj.newInstance(Integer.parseInt(user.getTenantId()));
        Arg1<SearchSceneKnowledgeModel.SearchArg> arg1 = new Arg1<>();
        SearchSceneKnowledgeModel.SearchArg knowledgeArg = SearchSceneKnowledgeModel.SearchArg.builder()
                .fsEa(eieaConverter.enterpriseIdToAccount(Integer.parseInt(user.getTenantId())))
                .content(searchWord)
                .scene(KNOWLEDGE_BASE_FIND_SCENE)
                .matchChatGPTContent(true)
                .fsUserId(Long.valueOf(user.getUserId()))
                .build();
        arg1.setArg1(knowledgeArg);
        EserviceResult<SearchSceneKnowledgeModel.SearchResult> knowledgeRet = knowledgeService.searchKnowledgeResult(header, arg1);
        if(ObjectUtils.isNotEmpty(knowledgeRet) && ObjectUtils.isNotEmpty(knowledgeRet.getData())){
            return knowledgeRet.getData();
        }
        return null;
    }


    public SearchSceneKnowledgeModel.SearchResult proxyKnowledgeAgent(User user, String searchWord){
        BaseArgument baseArgument =  new BaseArgument();
        baseArgument.setTenantId(user.getTenantId());
        baseArgument.setBusiness("SFA-ACTIVITY");
        Execute.Arg arg =  new Execute.Arg();
        arg.setApiName("Copilot_sfa_query_knowledge_base");
        arg.setMessages(null);
        arg.setInstructions(searchWord);
        arg.setSessionId(serviceFacade.generateId());
        try {
            Execute.Result result =  FsAI.agent().execute(baseArgument,arg);
            if(ObjectUtils.isEmpty(result.getActionResult()) || ObjectUtils.isEmpty(result.getActionResult().getOriginalData())){
                log.warn("AIKnowledgeBaseService proxyKnowledgeAgent result is null result :{}", JSONObject.toJSONString(result));
                return null;
            }
            return JSONObject.parseObject(result.getActionResult().getOriginalData().toString(),SearchSceneKnowledgeModel.SearchResult.class);

        }catch (Exception e){
            log.error("AIKnowledgeBaseService proxyKnowledgeAgent result ",e);
        }
        return null;
    }


    public RequirementModel.RequirementSolve queryKnowledgeBaseByAISummary(User user, String searchWord){
        AiRestProxyModel.FastQueryArg arg =  new AiRestProxyModel.FastQueryArg();
        arg.setQuery(searchWord);
        arg.setRagApiName("rag_service_knowledge__c");
        List<AiRestProxyModel.Filter> filters = new ArrayList<>();
        AiRestProxyModel.Filter filter = new AiRestProxyModel.Filter();
        filter.setFieldName("public_status");
        filter.setPaasFieldValue(Lists.newArrayList("1"));
        filter.setPaasOperator("EQ");
        filters.add(filter);
        arg.setFilters(filters);

        AiRestProxyModel.FastQueryResposne queryResult=aiRestProxy.fastQuery(arg,AiRestProxy.getHeaders(user.getTenantId(), user.getUserId()));
        if (queryResult != null && queryResult.getErrCode() != 0) {
            log.error("fastQuery error, code:{}, message:{}", queryResult.getErrCode(), queryResult.getErrMessage());
            return null;
        }
        AiRestProxyModel.FastQueryResult  result=queryResult.getResult();
        if(ObjectUtils.isEmpty(result) || CollectionUtil.isEmpty(result.getHits())){
            log.warn("fastQuery result is null");
            return null;
        }
        List<AiRestProxyModel.Hit> validHits = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        for(int i=0;i<result.getHits().size() && i<5;i++){
            AiRestProxyModel.Hit  hit = result.getHits().get(i);
            sb.append(hit.getContent());
            validHits.add(hit);
        }
        AiRestProxyModel.Arg aiArg = new AiRestProxyModel.Arg();
        aiArg.setApiName("prompt_sfa_requirement_ai_summary_solution");
        Map<String, Object>  sceneParamMap = new HashMap<>();
        sceneParamMap.put("knowledgeWord",sb.toString());
        sceneParamMap.put("requirementTitle",searchWord);
        aiArg.setSceneVariables(sceneParamMap);
        String aiResult = completions.requestCompletion(user,aiArg);
        aiResult = activitySummaryService.captureAiResult(aiResult);
        RequirementModel.RequirementSolve solve =  new RequirementModel.RequirementSolve();
        try {
            String newAiString = ActivityUtils.replaceStr(aiResult);
            solve = JSONObject.parseObject(newAiString, RequirementModel.RequirementSolve.class);
        }catch (Exception e){
         log.error("AIKnowledgeBaseService requestCompletion error aiResult:{},aiArg:{},e:",aiResult,JSONObject.toJSONString(aiArg),e);
        }
        solve.setHits(validHits);
        return solve;
    }

    public String queryKnowledgeBaseByAISummary(User user, String searchWord, String ragApiName
            , List<AiRestProxyModel.Filter> filters, int getHisCount, String promptApiName) {
        AiRestProxyModel.FastQueryArg arg = new AiRestProxyModel.FastQueryArg();
        arg.setQuery(searchWord);
        arg.setRagApiName(ragApiName);
        arg.setFilters(filters);
        AiRestProxyModel.FastQueryResposne queryResult = aiRestProxy.fastQuery(arg, AiRestProxy.getHeaders(user.getTenantId(), user.getUserId()));
        if (queryResult != null && queryResult.getErrCode() != 0) {
            log.error("fastQuery error, code:{}, message:{}", queryResult.getErrCode(), queryResult.getErrMessage());
            return null;
        }
        AiRestProxyModel.FastQueryResult result = queryResult.getResult();
        if (ObjectUtils.isEmpty(result) || CollectionUtil.isEmpty(result.getHits())) {
            log.warn("fastQuery result is null");
            return null;
        }
        List<AiRestProxyModel.Hit> validHits = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < result.getHits().size() && i < getHisCount; i++) {
            AiRestProxyModel.Hit hit = result.getHits().get(i);
            sb.append(hit.getContent());
            validHits.add(hit);
        }
        AiRestProxyModel.Arg aiArg = new AiRestProxyModel.Arg();
        aiArg.setApiName(promptApiName);
        Map<String, Object>  sceneParamMap = new HashMap<>();
        sceneParamMap.put("knowledgeWord",sb.toString());
        sceneParamMap.put("requirementTitle",searchWord);
        aiArg.setSceneVariables(sceneParamMap);
        return completions.requestCompletion(user, aiArg);
    }

    public String queryKnowledgeForProfileAdvice(User user, String searchWord, String ragApiName
            , List<AiRestProxyModel.Filter> filters,List<String> knowledgeIds) {
        AiRestProxyModel.FastQueryArg arg = new AiRestProxyModel.FastQueryArg();
        arg.setQuery(searchWord);
        arg.setRagApiName(ragApiName);
        arg.setFilters(filters);

        AiRestProxyModel.FastQueryResposne queryResult = aiRestProxy.fastQuery(arg, AiRestProxy.getHeaders(user.getTenantId(), user.getUserId()));
        if (queryResult != null && queryResult.getErrCode() != 0) {
            log.error("fastQuery error, code:{}, message:{}", queryResult.getErrCode(), queryResult.getErrMessage());
            return searchWord;
        }
        AiRestProxyModel.FastQueryResult result = queryResult.getResult();
        if (ObjectUtils.isEmpty(result) || CollectionUtil.isEmpty(result.getHits())) {
            log.warn("fastQuery result is null");
            return searchWord;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(searchWord);
        for (int i = 0; i < result.getHits().size()&& i<3; i++) {
            AiRestProxyModel.Hit hit = result.getHits().get(i);
            if (hit.getRetrievalType().equals("paragraph")) {
                sb.append(hit.getContent());
                if (!knowledgeIds.contains(hit.getId())) {
                    knowledgeIds.add(hit.getId());
                }
            }
        }
        return sb.toString();
    }

    public boolean hasLicense(User user){
        QueryModuleArg arg = new QueryModuleArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(user.getTenantId());
        licenseContext.setUserId(user.getUpstreamOwnerIdOrUserId());
        arg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(arg);
        if (result == null) {
            return false;
        }
        List<ModuleInfoPojo> modules = result.getResult();
        return modules.stream().filter(e -> e.getModuleCode().equals("customerservice_ai_plugin_app") || e.getModuleCode().equals("knowledgemanagement_app")).distinct().count() == 2;
    }
}
