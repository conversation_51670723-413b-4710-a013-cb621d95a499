package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.activity.enums.TaskStatusEnum;
import com.facishare.crm.sfa.lto.activity.model.ActivityPollingConstants;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.service.ActivityLongPollingService;
import com.facishare.crm.sfa.lto.activity.service.ActivityTaskStateService;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.activitysummary.model.auditlog.ActivityMessageConverter;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.functions.utils.Maps;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ActivityTodoService {

    private static final String ACTIVITY_TODO_API_NAME = "ActivityTodoObj";
    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    /* 分割文本每段的长度 **/
    private static int TEXT_MAX_LENGTH = 10000;
    /* 分割文本最小的长度，小于这个长度就不能单独成段 **/
    private static int TEXT_MIN_LENGTH = 3000;
    /* 覆盖前文长度 **/
    private static int TEXT_CONTEXT_OVERLAP_LENGTH = 1300;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private RedissonServiceImpl redissonService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ActivityTaskStateService activityTaskStateService;
    @Resource
    private ActivityMongoDao activityMongoDao;
    @Resource
    private ActivityLongPollingService activityLongPollingService;
    @Autowired
    private CompletionsService completions;

    static {
        ConfigFactory.getInstance().getConfig("fs-sfa-ai", (config) -> {
            // {"text_max_length":10000,"text_min_length":3000,"text_context_overlap_length":1300}
            String textTruncationConfig = config.get("text_truncation_config", "{}");
            try {
                Map<String, Integer> json = JSON.parseObject(textTruncationConfig, new TypeReference<HashMap<String, Integer>>() {
                });
                TEXT_MAX_LENGTH = json.getOrDefault("text_max_length", 10000);
                TEXT_MIN_LENGTH = json.getOrDefault("text_min_length", 3000);
                TEXT_CONTEXT_OVERLAP_LENGTH = json.getOrDefault("text_context_overlap_length", 1300);
            } catch (Exception e) {
                log.error("text_truncation_config", e);
                TEXT_MAX_LENGTH = 10000;
                TEXT_MIN_LENGTH = 3000;
                TEXT_CONTEXT_OVERLAP_LENGTH = 1300;
            }
        });
    }

    /**
     * activity待办生成，异步处理
     * 0(未生成)  -1(生成错误)  1(生成中)  2(已完成)
     */
    @SFAAuditLog(bizName = "activity_todo", entityClass = ActivityMessage.class, convertClass = ActivityMessageConverter.class)
    public void execute(ActivityMessage activityMessage, boolean isAuto, boolean skipMongo) {
        TraceContext.get().setTraceId(UUID.randomUUID().toString().replace("-", ""));
        log.info("activityTodo execute. objectId={}", activityMessage.getObjectId());
        if (!"ActiveRecordObj".equals(activityMessage.getObjectApiName())) {
            log.info("activityTodo apiName is not ActiveRecordObj.");
            return;
        }
        String tenantId = activityMessage.getTenantId();
        String objectId = activityMessage.getObjectId();
        if (ObjectUtils.isEmpty(tenantId) || ObjectUtils.isEmpty(objectId)) {
            log.warn("activityTodo arg is empty. Message={}", activityMessage);
            return;
        }
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        task.submit(() -> {
            if (checkTaskStatusIgnore(activityMessage)) {
                log.warn("activityTodo ignore. Message={}", activityMessage);
                return;
            }
            StopWatch stopWatch = new StopWatch("ActivityTodoExecute");
            if (ObjectUtils.isEmpty(activityMessage.getLanguage())) {
                activityMessage.setLanguage("zh_CN");
            }
            try {
                createActivityTodo(activityMessage, skipMongo);
                stopWatch.lap("ActivityTodoCreate");
                activityTaskStateService.insOrUpdate(tenantId, ACTIVITY_TODO_API_NAME, objectId, TaskStatusEnum.COMPLETED, null);
                stopWatch.lap("ActivityTodoInsTaskState");
                log.info("activityTodo create success.objectId={}", activityMessage.getObjectId());
            } catch (Exception e) {
                stopWatch.lap("ActivityTodoCreateError");
                log.error("activityTodo create error. objectId={}", activityMessage.getObjectId(), e);
                activityTaskStateService.insOrUpdate(tenantId, ACTIVITY_TODO_API_NAME, objectId, TaskStatusEnum.FAILED, null);
                stopWatch.lap("ActivityTodoInsTaskState");
            }
            redissonClient.getBucket("ActivityTodo_task_tem_" + activityMessage.getObjectId()).delete();
            if (!isAuto) {
                ActivityPollingConstants.PollingMessageData pollingMessageData = ActivityPollingConstants.PollingMessageData.builder()
                        .functionModule(ActivityPollingConstants.Function.ACTIVITY)
                        .primaryModule(ActivityPollingConstants.Primary.TO_DO)
                        .action(ActivityPollingConstants.Action.REFRESH_ALL)
                        .tenantId(tenantId)
                        .userId(activityMessage.getOpId())
                        .build();
                activityLongPollingService.sendPollingMessage(pollingMessageData);
            }
            stopWatch.lap("ActivityTodoExecute");
            stopWatch.logSlow(10000);
        }).run();
    }

    private void createActivityTodo(ActivityMessage activityMessage, boolean skipMongo) {
        StopWatch stopWatch = new StopWatch("todoCreate");
        log.info("activityTodo create todo start.");
        String tenantId = activityMessage.getTenantId();
        String objectId = activityMessage.getObjectId();
        User user = User.systemUser(tenantId);
        // 查询数据
        QueryResult<IObjectData> queryResult = selectTodoData(tenantId, objectId);
        stopWatch.lap("selectTodoData");
        if (!ObjectUtils.isEmpty(queryResult) && !ObjectUtils.isEmpty(queryResult.getData())) {
            log.info("activityTodo del todo count={}", queryResult.getData().size());
            serviceFacade.bulkDeleteDirect(queryResult.getData(), user);
        }
        stopWatch.lap("deleteTodoData");
        // 查询MongoDB中的转写数据
        if (!skipMongo){
            List<InteractiveDocument> interactiveDocuments = activityMongoDao.queryListByActiveRecordId(tenantId, objectId, 0, 500, true);
            stopWatch.lap("queryMongo");
            if (!ObjectUtils.isEmpty(interactiveDocuments)) {
                generateTodo(activityMessage, interactiveDocuments);
                stopWatch.lap("generateTodo");
                stopWatch.logSlow(10000);
                return;
            }
        }
        // 查询销售记录中的数据，文本类型
        log.warn("activityTodo MongoDB documents is null. Message={}", activityMessage);
        IObjectData objectData = serviceFacade.findObjectData(user, activityMessage.getObjectId(), activityMessage.getObjectApiName());
        stopWatch.lap("findObjectData");
        if (ObjectUtils.isEmpty(objectData) || ObjectUtils.isEmpty(objectData.get("interactive_content"))) {
            log.warn("activityTodo objectData null. objectData={}", objectData);
            activityTaskStateService.insOrUpdate(tenantId, ACTIVITY_TODO_API_NAME, objectId, TaskStatusEnum.FAILED, null);
            stopWatch.lap("insOrUpdateTaskState");
            stopWatch.logSlow(10000);
            return;
        }
        List<IObjectData> addToDoList = Lists.newArrayList();
        getAiResults(activityMessage, objectData.get("interactive_content", String.class))
                .forEach(x -> addToDoList.add(buildObjectData(x, activityMessage)));
        stopWatch.lap("getAiResults");
        serviceFacade.bulkSaveObjectData(addToDoList, User.systemUser(tenantId));
        stopWatch.lap("bulkSaveTodo");
        stopWatch.logSlow(10000);
    }

    /**
     * 查询待办数据
     * @param tenantId EI
     * @param objectId 销售记录id
     */
    private QueryResult<IObjectData> selectTodoData(String tenantId,String objectId){
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "0");
        SearchUtil.fillFilterEq(filters, "active_record_id", objectId);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setOffset(0);
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ACTIVITY_TODO_API_NAME, searchTemplateQuery);
    }

    /**
     * 判断是否忽略任务
     *
     * @param activityMessage mq
     * @return true-忽略 false-不忽略
     */
    private boolean checkTaskStatusIgnore(ActivityMessage activityMessage) {
        log.info("activityTodo checkTaskStatusIgnore. activityMessage={}", activityMessage);
        String tenantId = activityMessage.getTenantId();
        String objectId = activityMessage.getObjectId();
        RLock lock = redissonService.tryLock(1, 1, TimeUnit.MINUTES, "ActivityTodo_task_" + objectId);
        if (null == lock) {
            redissonClient.getBucket("ActivityTodo_task_tem_" + activityMessage.getObjectId()).delete();
            log.warn("activityTodo lock get error");
            return true;
        }
        try {
            IObjectData objectData = activityTaskStateService.getObjectData(tenantId, ACTIVITY_TODO_API_NAME, objectId, null);
            log.info("activityTodo checkTask objectData={}", objectData);
            if (objectData == null) {
                activityTaskStateService.insOrUpdate(tenantId, ACTIVITY_TODO_API_NAME, objectId, TaskStatusEnum.ONGOING, null);
                return false;
            }
            TaskStatusEnum status = TaskStatusEnum.fromValue(objectData.get(ActivityTaskStateService.TASK_STATUS, String.class));
            if (ObjectUtils.isEmpty(status) || TaskStatusEnum.FAILED.equals(status)) {
                activityTaskStateService.insOrUpdate(tenantId, ACTIVITY_TODO_API_NAME, objectId, TaskStatusEnum.ONGOING, null);
                return false;
            }
            if (TaskStatusEnum.COMPLETED.equals(status) && "Update".equals(activityMessage.getActionCode())) {
                activityTaskStateService.insOrUpdate(tenantId, ACTIVITY_TODO_API_NAME, objectId, TaskStatusEnum.ONGOING, null);
                return false;
            }
            // 如果待办为空，再生成一次，兼容无附件新增的情况下，待办无法生成的情况
            QueryResult<IObjectData> queryResult = selectTodoData(tenantId, objectId);
            if (ObjectUtils.isEmpty(queryResult) || ObjectUtils.isEmpty(queryResult.getData())) {
                log.info("activityTodo queryResult is empty! continue!");
                return false;
            }
            return true;
        } finally {
            redissonService.unlock(lock);
        }
    }

    // 生成待办-录音内容
    public void generateTodo(ActivityMessage activityMessage, List<InteractiveDocument> interactiveDocuments) {
        log.info("activityTodo ai task start.");
        // 提取发言人集合
        Map<String, Map<String, String>> userMap = new HashMap<>();
        interactiveDocuments.forEach(o -> {
            String userName = o.getUserName();
            String userId = o.getUserId();
            String userApiName = o.getUserApiName();
            if (ObjectUtils.isEmpty(userName) || ObjectUtils.isEmpty(userId) || ObjectUtils.isEmpty(userApiName)) {
                return;
            }
            userMap.put(userName, Maps.of("id", userId, "apiName", userApiName));
        });
        log.info("activityTodo userMap={}", userMap);
        StringBuilder activeRecordText = new StringBuilder();
        interactiveDocuments.forEach(o -> {
            if (ObjectUtils.isEmpty(o.getContent())) {
                return;
            }
            activeRecordText.append("(");
            activeRecordText.append(o.getSeq());
            activeRecordText.append(")");
            activeRecordText.append(o.getUserName());
            activeRecordText.append("[");
            activeRecordText.append(o.getStartTime());
            activeRecordText.append(o.getEndTime());
            activeRecordText.append("]: ");
            activeRecordText.append(o.getContent());
            activeRecordText.append(";\n");
        });
        String tenantId = activityMessage.getTenantId();
        // 拼接参数提示词
        List<IObjectData> addToDoList = Lists.newArrayList();
        getAiResults(activityMessage, activeRecordText.toString()).forEach(o -> {
            IObjectData objectData = buildObjectData(o, activityMessage);
            JSONArray refSource = o.getJSONArray("sourceNum");
            if (!ObjectUtils.isEmpty(refSource)) {
                objectData.set("ref_source", refSource.toJSONString());
            }
//            try {
//                fillingPeople(o, objectData, userMap);
//            }catch (Exception e){
//                log.warn("activityTodo fillingPeople error o={}", o, e);
//            }
            addToDoList.add(objectData);
        });
        serviceFacade.bulkSaveObjectData(addToDoList, User.systemUser(tenantId));
    }

    public List<JSONObject> getAiResults(ActivityMessage activityMessage, String text) {
        List<JSONObject> aiResults = Lists.newArrayList();
        String time = simpleDateFormat.format(new Date());
        List<String> smallText = splitStringEveryNChars(text);
        log.info("activityTodo ai text length{}.request count:{}", text.length(), 1);
        smallText.forEach(s -> {
            AiRestProxyModel.Arg aiArg = AiRestProxyModel.Arg.builder()
                    .apiName("prompt_sfa_activity_todo")
                    .sceneVariables(Maps.of(
                            "body", s,
                            "language", activityMessage.getLanguage(),
                            "time", time))
                    .build();
            User user = new User(activityMessage.getTenantId(), ObjectUtils.isEmpty(activityMessage.getOpId()) ? User.SUPPER_ADMIN_USER_ID : activityMessage.getOpId());
            log.info("activityTodo ai user:{}", user);
            String aiResult = completions.requestCompletion(user, aiArg);
            if (ObjectUtils.isEmpty(aiResult) ) {
                return;
            }
            JSONObject jsonObject = aiResultTextDispose(aiResult);
            JSONObject suggestionsJsonObj = jsonObject.getJSONObject("actionSuggestions");
            JSONObject todoJsonObj = jsonObject.getJSONObject("todo");
            // 获取结果
            Integer suggestionsNum = suggestionsJsonObj.getInteger("count");
            Integer toDoNum = todoJsonObj.getInteger("count");
            Integer zero = 0;
            if (zero.equals(suggestionsNum) && zero.equals(toDoNum)) {
                return;
            }
            todoJsonObj.getJSONArray("dataList").forEach(x -> aiResults.add((JSONObject) x));
            suggestionsJsonObj.getJSONArray("dataList").forEach(x -> {
                JSONObject o = (JSONObject) x;
                o.put("todoType", "suggest");
                aiResults.add(o);
            });
        });
        return aiResults;
    }

    public IObjectData buildObjectData(JSONObject sourceData, ActivityMessage activityMessage) {
        // 执行时间 yyyy-mm-dd hh:mm
        String executionTime = sourceData.getString("executionTime");
        // 待办事项
        String toDoItem = sourceData.getString("item");
        // 待办事项描述
        String toDoItemDescription = sourceData.getString("itemDescription");
        try {
            if (toDoItemDescription != null && toDoItemDescription.contains("user_")) {
                String language = activityMessage.getLanguage();
                if ("zh_CN".equals(language) || ObjectUtils.isEmpty(language)) {
                    language = I18N.ZH_CN;
                }
                I18N.setContext(activityMessage.getTenantId(), language);
                toDoItemDescription = toDoItemDescription.replaceAll("user_", I18N.text("paas.udobj.user"));
            }
        } catch (Exception e) {
            log.error("tem replace user_ error:", e);
        }
        IObjectData objectData = new ObjectData();
        objectData.setName(toDoItem);
        objectData.set("task_status", "unconfirmed");
        objectData.set("ai_summary", toDoItemDescription);
        objectData.set("active_record_id", activityMessage.getObjectId());
        objectData.set("todo_type", sourceData.get("todoType"));
//        try {
//            objectData.set("execution_time", simpleDateFormat.parse(executionTime).getTime());
//        } catch (ParseException e) {
//            log.warn("activityTodo parse execution_time error", e);
//        }
        objectData.setTenantId(activityMessage.getTenantId());
        objectData.setDescribeApiName(ACTIVITY_TODO_API_NAME);
        return objectData;
    }

    // 给待办填充执行人和发起人
    private void fillingPeople(JSONObject o, IObjectData objectData, Map<String, Map<String, String>> userMap) {
        // 执行人，只有是内部员工或是部门，才会映射进去
        List<String> executorList = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(o.getJSONArray("Executor"))) {
            o.getJSONArray("Executor").forEach(s -> {
                if (userMap.containsKey(s.toString())) {
                    executorList.add(s.toString());
                }
            });
        }
        if (!ObjectUtils.isEmpty(executorList)) {
            List<String> executorIds = Lists.newArrayList();
            executorList.forEach(executor -> {
                Map<String, String> u = userMap.get(executor);
                String apiName = u.get("apiName");
                if ("PersonnelObj".equals(apiName) || "DepartmentObj".equals(apiName)) {
                    executorIds.add(u.get("id"));
                }
            });
            objectData.set("executor", executorIds);
        }
        // 提出人
        List<String> proposerList = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(o.getJSONArray("Proposer"))) {
            o.getJSONArray("Proposer").forEach(s -> {
                if (userMap.containsKey(s.toString())) {
                    proposerList.add(s.toString());
                }
            });
        }
        if (!ObjectUtils.isEmpty(proposerList)) {
            Set<String> apiNames = new HashSet<>();
            List<Map<String, String>> relatedObjectData = Lists.newArrayList();
            proposerList.forEach(s -> {
                Map<String, String> u = userMap.get(s);
                String apiName = u.get("apiName");
                String id = u.get("id");
                apiNames.add(apiName);
                relatedObjectData.add(Maps.of("describe_api_name", apiName, "id", id));
            });
            objectData.set("related_api_names", Lists.newArrayList(apiNames));
            objectData.set("related_object_data", relatedObjectData);
        }
    }

    /**
     * 分割长文本，每段大约TEXT_MAX_LENGTH个字符。<br/>
     * 分割的基本准则：在换行处分割，保证句子完整，可以超出TEXT_MAX_LENGTH个字符。<br/>
     * 如果是第一段，参照分割的基本准则进行分割。<br/>
     * 如果是最后一段，文本长度小于TEXT_MIN_LENGTH，就合并到上一段。<br/>
     * 如果是中间段，在基本准则分割出来的大约TEXT_MAX_LENGTH个字符的基础上，
     * 文本前面加上前一段的末尾大约TEXT_CONTEXT_OVERLAP_LENGTH个字符（保证句子完成可以多截取）.<br/>
     *
     * 最终返回的List中，每段长度大约TEXT_MAX_LENGTH个字符，大概率会超出这个长度。
     *
     * @param str 需要被分割的字符串
     */
    private List<String> splitStringEveryNChars(String str) {
        List<String> parts = Lists.newArrayList();
        int maxCount = str.length() / TEXT_MAX_LENGTH + 1;
        // 截断字符串
        for (int i = 0; i < maxCount; i++) {
            if (str.length() < TEXT_MAX_LENGTH + TEXT_CONTEXT_OVERLAP_LENGTH) {
                parts.add(str);
                break;
            }
            int index = TEXT_MAX_LENGTH + TEXT_CONTEXT_OVERLAP_LENGTH - 1;
            for (int j = TEXT_MAX_LENGTH; j < TEXT_MAX_LENGTH + TEXT_CONTEXT_OVERLAP_LENGTH; j++) {
                char c = str.charAt(j);
                if (c == '\n') {
                    index = j+1;
                    break;
                }
            }
            parts.add(str.substring(0, index));
            str = str.substring(index);
        }
        if (parts.size() <= 1){
            return parts;
        }
        // 判断最后一个字符串是否需要合并
        if (parts.get(parts.size() - 1).length() < TEXT_MIN_LENGTH){
            String last = parts.remove(parts.size() - 1);
            String previous = parts.get(parts.size() - 1);
            parts.set(parts.size() - 1, previous + last);
        }
        // 上下文重叠
        for (int i = 1; i < parts.size(); i++) {
            String last = parts.get(i - 1);
            int index = TEXT_CONTEXT_OVERLAP_LENGTH;
            for (int j = last.length()-1; j >= 0; j--) {
                char c = last.charAt(j);
                if (c == '\n') {
                    index = j;
                    break;
                }
            }
            String newLast = last.substring(index);
            String s = parts.get(i) + newLast;
            parts.set(i, s);
        }
        return parts;
    }

    /**
     * 过滤ai返回结果，仅截取json内容吗
     * @param aiResult ai返回的结果
     */
    private JSONObject aiResultTextDispose(String aiResult) {
        aiResult = removeSpecialCharacters(aiResult,"'''");
        aiResult = removeSpecialCharacters(aiResult,"```");
        aiResult = aiResult.replace("\\n","");
        try {
            return JSON.parseObject(aiResult);
        }catch (Exception e){
            log.warn("activityTodo aiResultTextDispose error, massage:{}", aiResult, e);
            JSONObject defaultJsonObject = new JSONObject();
            defaultJsonObject.put("ToDoNum", 0);
            return defaultJsonObject;
        }
    }

    public static String removeSpecialCharacters(String aiResult, String str) {
        String rem = str + "json";
        int jsonStartIndex = aiResult.indexOf(rem);
        if (jsonStartIndex != -1) {
            // 去掉 "'''json" 之前的所有字符和 "'''json" 本身
            String jsonContent = aiResult.substring(jsonStartIndex + rem.length());
            // 去掉结尾的 "'''"
            int jsonEndIndex = jsonContent.lastIndexOf(str);
            if (jsonEndIndex != -1) {
                jsonContent = jsonContent.substring(0, jsonEndIndex).trim();
            }
            aiResult = jsonContent;
        }
        return aiResult.replaceAll(str, "");
    }

}
