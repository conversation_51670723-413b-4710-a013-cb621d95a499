package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.constant.ActiveRecordConstants;
import com.facishare.crm.task.sfa.activitysummary.constant.EmotionAnalysisConstants;
import com.facishare.crm.task.sfa.activitysummary.enums.EmotionalTendencyEnum;
import com.facishare.crm.task.sfa.activitysummary.enums.LanguageEnum;
import com.facishare.crm.task.sfa.activitysummary.model.EmotionAnalysisModel.EmotionAnalysisDetail;
import com.facishare.crm.task.sfa.activitysummary.model.EmotionAnalysisModel.EmotionAnalysisSummary;
import com.facishare.crm.task.sfa.activitysummary.model.auditlog.ActivityMessageConverter;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.util.SFAConcurrentLock;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.functions.utils.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.activitysummary.constant.EmotionAnalysisConstants.EmotionAnalysisDetailObj;
import static com.facishare.crm.task.sfa.activitysummary.constant.EmotionAnalysisConstants.EmotionAnalysisObj;

@Slf4j
@Service
public class EmotionAnalysisService {

    @Autowired
    private CompletionsService completionsService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private SFAConcurrentLock sfaConcurrentLock;

    private static final String PROMPT_SFA_ACTIVITY_EMOTION_ANALYSIS = "prompt_sfa_activity_emotion_analysis";
    private static final String PROMPT_SFA_ACTIVITY_EMOTION_ANALYSIS_DETAIL = "prompt_sfa_activity_emotion_analysis_detail";

    private static final String CONCURRENT_LOCK_PREFIX = "EMOTION_ANALYSIS_";

    private static final List<String> SUPPORT_STAGE_LIST = Lists.newArrayList("file2text", "realtime2textDone");


    @SFAAuditLog(bizName = "emotion_analysis", entityClass = ActivityMessage.class, convertClass = ActivityMessageConverter.class)
    public void generate(ActivityMessage activityMessage) {
        log.warn("generate emotion analysis, activityMessage: {}", activityMessage);
        String tenantId = activityMessage.getTenantId();

        if (needSkipGenerate(activityMessage)) {
            log.warn("skip generate emotion analysis, activityMessage: {}", activityMessage);
            return;
        }

        String objectId = activityMessage.getObjectId();
        sfaConcurrentLock.runIfGetLock(CONCURRENT_LOCK_PREFIX + objectId, 5 * 60 * 1000,
                () -> doGenerate(activityMessage, tenantId));
    }

    private void doGenerate(ActivityMessage activityMessage, String tenantId) {
        StopWatch stopWatch = new StopWatch("emotion_analysis");
        CompletionResult completionResult = new CompletionResult();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> completionResult.setSummary(completionsSummary(activityMessage)));
        parallelTask.submit(() -> completionResult.setDetails(completionsDetail(activityMessage)));
        try {
            parallelTask.await(5 , TimeUnit.MINUTES);
        } catch (TimeoutException e) {
            log.error("completionsSummary or completionsDetail timeout, activityMessage: {}", activityMessage);
            return;
        }
        stopWatch.lap("completions");
        EmotionAnalysisSummary summary = completionResult.getSummary();
        List<EmotionAnalysisDetail> details = completionResult.getDetails();

        if (isInvalid(summary)) {
            log.warn("invalid emotion analysis summary, activityMessage: {}, summary: {}", activityMessage, summary);
            return;
        }

        String summaryId = serviceFacade.generateId();
        List<IObjectData> detailList = buildDetailList(activityMessage, details, tenantId, summaryId);

        Map<String, List<IObjectData>> tendencyGroup = detailList.stream().collect(Collectors.groupingBy(d -> d.get(EmotionAnalysisConstants.EMOTIONAL_TENDENCY, String.class)));

        IObjectData summaryData = buildSummaryData(activityMessage, summaryId, summary, detailList, tendencyGroup);

        User user = User.systemUser(tenantId);
        serviceFacade.saveObjectData(user, summaryData);
        serviceFacade.bulkSaveObjectData(detailList, user);
        stopWatch.lap("saveData");
        stopWatch.logSlow(10, TimeUnit.SECONDS);
    }


    private boolean needSkipGenerate(ActivityMessage activityMessage) {
        String tenantId = activityMessage.getTenantId();
        if (!Utils.ACTIVE_RECORD_API_NAME.equals(activityMessage.getObjectApiName())
                || Safes.isEmpty(activityMessage.getObjectId())
                || Safes.isEmpty(activityMessage.getStage())) {
            return true;
        }

        if ("AddNoAttachment".equals(activityMessage.getStage())) {
            IObjectData activeRecord = serviceFacade.findObjectData(User.systemUser(activityMessage.getTenantId()), activityMessage.getObjectId(), CommonConstant.ACTIVE_RECORD_API_NAME);
            if (activeRecord == null) {
                log.warn("EmotionAnalysisService activeRecordData is null");
                return true;
            }
            String interactiveContent = activeRecord.get(ActiveRecordConstants.INTERACTIVE_CONTENT_O, String.class);
            if (StringUtils.isBlank(interactiveContent) || interactiveContent.length() <= 150) {
                log.warn("EmotionAnalysisService INTERACTIVE_CONTENT_O is blank or length <= 150, activityMessage={}", activityMessage);
                return true;
            }
        } else {
            if (!SUPPORT_STAGE_LIST.contains(activityMessage.getStage())) {
                return true;
            }
        }


        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), EmotionAnalysisConstants.ACTIVE_RECORD_ID, Lists.newArrayList(activityMessage.getObjectId()));
        query.setLimit(1);
        List<IObjectData> emotionAnalysisList = serviceFacade.findBySearchQuery(User.systemUser(tenantId), EmotionAnalysisObj, query).getData();
        return Safes.isNotEmpty(emotionAnalysisList);
    }

    @SuppressWarnings("unchecked")
    private IObjectData buildSummaryData(ActivityMessage activityMessage, String summaryId, EmotionAnalysisSummary summary, List<IObjectData> detailList, Map<String, List<IObjectData>> tendencyGroup) {
        IObjectData summaryData = new ObjectData();
        summaryData.setId(summaryId);
        summaryData.setTenantId(activityMessage.getTenantId());
        summaryData.setDescribeApiName(EmotionAnalysisObj);
        summaryData.setOwner(Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
        summaryData.set(EmotionAnalysisConstants.ACTIVE_RECORD_ID, activityMessage.getObjectId());
        summaryData.set(EmotionAnalysisConstants.EMOTIONAL_TENDENCY, summary.getEmotionalTendency());
        summaryData.set(EmotionAnalysisConstants.EMOTION_SUMMARY, summary.getSummary());
//        for (EmotionAnalysisModel.UserAnalysis userAnalysis : Safes.of(summary.getUserAnalysisList())) {
//            userAnalysis.setEmotionalTendencyValue(EmotionalTendencyEnum.ofType(userAnalysis.getEmotionalTendency()));
//        }
        summaryData.set(EmotionAnalysisConstants.EMOTION_ANALYZE, JSON.toJSONString(summary.getUserAnalysisList()));
        summaryData.set(EmotionAnalysisConstants.DETAIL_COUNT, detailList.size());
        summaryData.set(EmotionAnalysisConstants.SATISFACTION_COUNT, tendencyGroup.getOrDefault(EmotionalTendencyEnum.SATISFACTION.getType(), Collections.EMPTY_LIST).size());
        summaryData.set(EmotionAnalysisConstants.NEUTRALITY_COUNT, tendencyGroup.getOrDefault(EmotionalTendencyEnum.NEUTRALITY.getType(), Collections.EMPTY_LIST).size());
        summaryData.set(EmotionAnalysisConstants.DISSATISFIED_COUNT, tendencyGroup.getOrDefault(EmotionalTendencyEnum.DISSATISFIED.getType(), Collections.EMPTY_LIST).size());
        return summaryData;
    }

    private boolean isInvalid(EmotionAnalysisSummary summary) {
        if (summary == null || Safes.isEmpty(summary.getSummary()) || Safes.isEmpty(summary.getUserAnalysisList()) || Safes.isEmpty(summary.getEmotionalTendency())) {
            return true;
        }
        return false;
    }

    private List<IObjectData> buildDetailList(ActivityMessage activityMessage, List<EmotionAnalysisDetail> details, String tenantId, String summaryId) {
        List<IObjectData> detailList = new ArrayList<>();
        for (EmotionAnalysisDetail detail : details) {
            IObjectData detailData = new ObjectData();
            detailData.setTenantId(tenantId);
            detailData.setOwner(Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
            detailData.setDescribeApiName(EmotionAnalysisDetailObj);
            detailData.set(EmotionAnalysisConstants.ACTIVE_RECORD_ID, activityMessage.getObjectId());
            detailData.set(EmotionAnalysisConstants.EMOTION_ANALYSIS_ID, summaryId);
            detailData.set(EmotionAnalysisConstants.EMOTION_POINT, detail.getEmotionPoint());
            detailData.set(EmotionAnalysisConstants.QUOTE_TEXT, detail.getQuoteText());
            detailData.set(EmotionAnalysisConstants.QUOTE_CONTEXT, replaceRepeatNewline(detail.getQuoteContext()));
            detailData.set(EmotionAnalysisConstants.EMOTION_TYPE, detail.getEmotionType());
            detailData.set(EmotionAnalysisConstants.EMOTIONAL_TENDENCY, detail.getEmotionalTendency());
            detailList.add(detailData);
        }
        return detailList;
    }

    private String replaceRepeatNewline(String text) {
        if (Safes.isEmpty(text)) {
            return text;
        }
        return text.replaceAll("\n{2,}", "\n");
    }


    private EmotionAnalysisSummary completionsSummary(ActivityMessage activityMessage) {
        String tenantId = activityMessage.getTenantId();
        AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
                .apiName(PROMPT_SFA_ACTIVITY_EMOTION_ANALYSIS)
                .bingObjectDataId(activityMessage.getObjectId())
                .sceneVariables(Maps.of("langEnv", LanguageEnum.getByCode(Optional.ofNullable(activityMessage.getLanguage()).orElse("zh_CN"))))
                .build();
        return completionsService.requestCompletionData(new User(tenantId, activityMessage.getOpId()), arg, StringUtils.EMPTY, EmotionAnalysisSummary.class);
    }

    private List<EmotionAnalysisDetail> completionsDetail(ActivityMessage activityMessage) {
        String tenantId = activityMessage.getTenantId();
        AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
                .apiName(PROMPT_SFA_ACTIVITY_EMOTION_ANALYSIS_DETAIL)
                .bingObjectDataId(activityMessage.getObjectId())
                .sceneVariables(Maps.of("langEnv", LanguageEnum.getByCode(Optional.ofNullable(activityMessage.getLanguage()).orElse("zh_CN"))))
                .build();
        return completionsService.requestCompletionListRetainNewline(new User(tenantId, activityMessage.getOpId()), arg, StringUtils.EMPTY, EmotionAnalysisDetail.class);
    }

    @Data
    static class CompletionResult {
        private EmotionAnalysisSummary summary;
        private List<EmotionAnalysisDetail> details;
    }
}
