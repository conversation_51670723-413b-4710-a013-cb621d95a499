package com.facishare.crm.task.sfa.activitysummary.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/1/25 15:26
 * @description:
 */
public interface ViewDataFilterConfig {

    @Data
    class ViewDataFilter{

        @JSONField(name = "tenant_id")
        @JsonProperty("tenant_id")
        private String tenantId;

        @JSONField(name = "date_filter_id")
        @JsonProperty("date_filter_id")
        private String dateFilterId;

        @JSONField(name = "object_filter_id")
        @JsonProperty("object_filter_id")
        private String objectFilterId;

        @JSONField(name = "view_id")
        @JsonProperty("view_id")
        private String viewId;

    }
}
