package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.Data;

import java.util.List;

public interface ActivityContactInsightModel {
    @Data
    class Arg{
        private String tenantId;
        private String activeRecordId;
    }

    @Data
    class Concern{
        private String bant_b;
        private List<String> bant_b_source;
        private String bant_a;
        private List<String> bant_a_source;
        private String bant_n;
        private List<String> bant_n_source;
        private String bant_t;
        private List<String> bant_t_source;
    }
    @Data
    class Meeting{
        private String topics;
        private List<String> topics_source;
    }
}
