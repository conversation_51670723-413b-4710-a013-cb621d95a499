package com.facishare.crm.task.sfa.bizfeature.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * MQ触发特征计算参数
 * 
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface FeatureMqModel {
    /**
     * 特征计算消息生产者名称
     */
    String MQ_PRODUCER_NAME = "sfa-ai-feature-value-producer";

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Message {
        /**
         * 段落id
         */
        private List<String> paragraphIds;
        /**
         * 企业id
         */
        private String tenantId;
        /**
         * userId
         */
        private String userId;
        /**
         * 类选 参考MessageType：tag-标注，requirement-需求洞察，participant-参会人洞察，
         */
        private String type;

        private String objectId;

        private String objectApiName;
        /**
         * 请求唯一id 方便后续排查问题串联数据
         */
        private String requestId;
    }

    enum MessageType {
        /**
         * 参会人洞察
         */
        PARTICIPANT("participant"),
        /**
         * 需求洞察
         */
        REQUIREMENT("requirement"),
        /**
         * 原文标注
         */
        TAG("tag");

        private final String messageType;

        public String getMessageType() {
            return messageType;
        }

        MessageType(String messageType) {
            this.messageType = messageType;
        }
    }
}
