package com.facishare.crm.task.sfa.bizfeature.enums;

public enum ProfileScoreTypeEnum {

    DIMENSION("dimension", "维度"),
    NODE("node", "节点");

    private String type;
    private String desc;

    ProfileScoreTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
