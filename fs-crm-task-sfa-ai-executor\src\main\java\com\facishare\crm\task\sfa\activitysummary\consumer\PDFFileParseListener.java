
package com.facishare.crm.task.sfa.activitysummary.consumer;


import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.activity.enums.TaskStatusEnum;
import com.facishare.crm.sfa.lto.activity.service.ActivityTaskStateService;
import com.facishare.crm.task.sfa.activitysummary.service.Rec2TextService;
import com.facishare.crm.task.sfa.rest.FsFileToContentResource;
import com.facishare.crm.task.sfa.rest.dto.FileToContentJobSubmit;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.support.GDSHandler;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 接收平台的消息， 消费 pdf 文件解析消息
 * https://wiki.firstshare.cn/pages/viewpage.action?pageId=562891364
 */
@Slf4j
@Component
public class PDFFileParseListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Resource
    private FsFileToContentResource fsFileToContentResource;

    @Resource
    private ActivityTaskStateService activityTaskStateService;

    @Resource
    protected Rec2TextService rec2TextService;

    @Resource
    protected GDSHandler gdsHandler;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "activity-file-parse", (MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                try {
                    // 消费消息
                    processMessage(msg);
                } catch (Exception e) {
                    log.error("Error processing message: {}", msg, e);
                    throw new RuntimeException(e);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    /**
     * 处理消息
     */
    private void processMessage(MessageExt msg) {
        try {
            String messageBody = new String(msg.getBody());
            log.info("Processing PDF file parse message: {},msgId:{}", messageBody, msg.getMsgId());
            try {
                FileToContentJobSubmit.DocParseCompletedMsg parseCompletedMsg = JSON.parseObject(messageBody, FileToContentJobSubmit.DocParseCompletedMsg.class);
                if (!parseCompletedMsg.isSuccess()) {
                    log.warn("File path rule mismatch error detected - ea: {}, jobId: {}, requestId: {}, failCode: {}, failMsg: {}",
                            parseCompletedMsg.getEa(), parseCompletedMsg.getJobId(), parseCompletedMsg.getRequestId(), parseCompletedMsg.getJobFailCode(), parseCompletedMsg.getJobFailMsg());
                    return; // 直接返回，不抛出异常
                }
                // 处理其他类型的消息
                if (parseCompletedMsg.getJobId() != null) {
                    String fullText = getDocumentContent(parseCompletedMsg);
                    String tenantId = gdsHandler.getEAByEI(parseCompletedMsg.getEa());
                    IObjectData fileParseData = activityTaskStateService.getObjectData(parseCompletedMsg.getEa(), "file_parse", parseCompletedMsg.getJobId());
                    String activeRecordId = fileParseData.get("target_data_id", String.class);
                    TaskStatusEnum status = TaskStatusEnum.fromValue(fileParseData.get(ActivityTaskStateService.TASK_STATUS, String.class));
                    if (!ObjectUtils.isEmpty(status) && TaskStatusEnum.ONGOING.equals(status)) {
                        activityTaskStateService.insOrUpdate(tenantId, "file_parse", activeRecordId, TaskStatusEnum.COMPLETED, parseCompletedMsg.getJobId());
                    }
                    rec2TextService.updateActivityText(tenantId, activeRecordId, "ActiveRecordObj", fullText);
                }

            } catch (Exception parseException) {
                log.error("Failed to parse message as DocParseCompletedMsg, treating as raw message: {}", parseException.getMessage());
            }

        } catch (Exception e) {
            log.error("Error processing PDF file parse message", e);
            throw e;
        }
    }

    /**
     * 调用GetContent API获取文档内容
     */
    public String getDocumentContent(FileToContentJobSubmit.DocParseCompletedMsg docParseCompletedMsg) {
        try {
            // 构建请求参数
            FileToContentJobSubmit.JobGetContentArg request = new FileToContentJobSubmit.JobGetContentArg();
            request.setJobId(docParseCompletedMsg.getJobId());

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("x-fs-ei", docParseCompletedMsg.getEa());
            headers.put("x-fs-userInfo", User.SUPPER_ADMIN_USER_ID);

            // 调用API - 直接返回文档内容字符串
            String content = fsFileToContentResource.getContent(headers, request);
            if (content != null && !content.trim().isEmpty()) {
                return content;
            } else {
                log.warn("Failed to get document content for jobId: {}, content is empty", docParseCompletedMsg.getJobId());
                return "";
            }
        } catch (Exception e) {
            log.error("Error calling getContent API for jobId: {}", docParseCompletedMsg.getJobId(), e);
            return "";
        }
    }
}


