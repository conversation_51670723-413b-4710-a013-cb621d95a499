package com.facishare.crm.task.sfa.activitysummary.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ParagraphResultModel {
    private List<Chunk> chunks;
    // 根据自我评分计算得出
    @JSONField(name = "self_score")
    private Double selfScore;
    // 根据 self_score 判断
    @JSONField(name = "needs_high_level_model")
    private Boolean needsHighLevelModel;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Chunk {
        private List<String> content;
        private String summary;
    }
}
