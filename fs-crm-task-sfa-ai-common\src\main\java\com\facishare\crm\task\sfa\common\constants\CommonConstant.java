package com.facishare.crm.task.sfa.common.constants;

import com.google.common.collect.Lists;

import java.util.List;

public class CommonConstant {
    public static final String SUPER_USER = "-10000";
    public static final String LEADS_API_NAME = "LeadsObj";
    public static final String LEADS_POOL_API_NAME = "LeadsPoolObj";
    public static final String LEADS_OWNER_HISTORY_API_NAME = "LeadsOwnerHistoryObj";
    public static final String LEADS_POOL_ALLOCATE_RULE_MEMBER_RECORD_API_NAME = "LeadsPoolAllocateRuleMemberRecordObj";
    public static final String ACCOUNT_API_NAME = "AccountObj";
    public static final String PRODUCT_API_NAME = "ProductObj";
    public static final String ACTIVE_RECORD_API_NAME = "ActiveRecordObj";
    public static final String ACCOUNT_ADDR_API_NAME = "AccountAddrObj";
    public static final String ACTIVITY_QUESTION_API_NAME = "ActivityQuestionObj";
    public static final String ACCOUNT_DEPARTMENT_API_NAME = "AccountDepartmentObj";
    public static final String ACTIVITY_BUSINESS_API_NAME = "ActivityBusinessObj";
    public static final String SALES_TOPIC_LIBRARY_API_NAME = "SalesTopicLibraryObj";
    public static final String ACTIVITY_ACCOUNT_SUMMARY_OBJ = "ActivityAccountSummaryObj";
    public static final String ACTIVITY_OPPORTUNITY_SUMMARY_OBJ = "ActivityOpportunitySummaryObj";
    public static final String ACTIVITY_CONTACT_SUMMARY_OBJ = "ActivityContactSummaryObj";
    public static final String ACTIVITY_DEPARTMENT_SUMMARY_OBJ = "ActivityDepartmentSummaryObj";
    public static final String REQUIREMENT_OBJ = "RequirementObj";
    public static final String ActivityUserObj = "ActivityUserObj";
    public static final String AttendeeInsightRecordObj = "AttendeeInsightRecordObj";
    public static final String ContactObj = "ContactObj";
    public static final String ActivityContactInsightObj = "ActivityContactInsightObj";
    public static final String OUT_OWNER = "out_owner";
    public static final String OUT_TENANT_ID = "out_tenant_id";
    public static final String OWNER = "owner";

    public static final String LEADS_POOL_FIELD_API_NAME = "leads_pool_id";
    public static final String LEADS_ID = "leads_id";
    public static final String CONTACT_ID = "contact_id";
    public static final String ACTIVE_RECORD_ID = "active_record_id";
    public static final String ACTIVITY_USER_ID = "activity_user_id";
    public static final String OPPORTUNITY_CONTACTS_ID = "opportunity_contacts_id";

    public static final String ACCOUNT_FIELD_PARENT_ACCOUNT_ID = "parent_account_id";
    public static final String ACCOUNT_FIELD_ACCOUNT_PATH = "account_path";
    public static final String ACCOUNT_ID = "account_id";
    public static final String NEW_OPPORTUNITY_ID= "new_opportunity_id";
    public static final String INTERACTIVE_CONTENT= "interactive_content";
    public static final String ACTIVE_RECORD_CONTENT= "active_record_content";
    public static final String ACTIVE_RECORD_CONTENT__E= "active_record_content__e";

    public static final String PARTNER_ID= "partner_id";
    public static final String OPPORTUNITY_ID= "opportunity_id";
    public static final String INITIATOR= "initiator";
    public static final String INSIGHT_TYPE= "insight_type";
    public static final String INSIGHT_RESULT__O= "insight_result__o";

    public static final Long MIN_TIME_STAMP = 946656000000L;

    public static final String CRM_APP_ID = "CRM";
    public static final String ALL_COMPANY_ID = "999999";

    public static final String PROJECT_API_NAME = "ProjectObj";
    public static final String PROJECT_STAGE_API_NAME = "ProjectStageObj";
    public static final String PROJECT_TASK_API_NAME = "ProjectTaskObj";
    public static final String PROJECT_DOCUMENT_API_NAME = "ProjectDocumentObj";
    public static final String TIME_SHEET_API_NAME = "TimeSheetObj";
    public static final String DEPENDENCIES_API_NAME = "DependenciesObj";

    /**竞争对手**/
    public static final String COMPETITIVE_LINES_API_NAME = "CompetitiveLinesObj";


    public static final String MARKETING_EVENT_API_NAME = "MarketingEventObj";
    public static final String CONTACT_API_NAME = "ContactObj";
    public static final String NEW_OPPORTUNITY_API_NAME = "NewOpportunityObj";
    public static final String NEW_OPPORTUNITY_LINES_API_NAME = "NewOpportunityLinesObj";
    public static final String NEW_OPPORTUNITY_CONTACTS_API_NAME = "NewOpportunityContactsObj";
    public static final String QUOTE_API_NAME = "QuoteObj";
    public static final String SALES_ORDER_API_NAME = "SalesOrderObj";
    public static final String CONTRACT_API_NAME = "ContractObj";

    public static final String SALES_ORDER_PRODUCT_API_NAME = "SalesOrderProductObj";
    public static final String PRICE_BOOK_PRODUCT_API_NAME = "PriceBookProductObj";
    public static final String AVAILABLE_PRODUCT_API_NAME = "AvailableProductObj";
    public static final String PRICE_POLICY_PRODUCT_API_NAME = "PricePolicyProductObj";
    /** supplier_product_list */
    public static final String TARGET_RELATED_LIST_NAME = "%s_%s_list";

    public static final List<String> INIT_REFERENCE_FIELD_OBJECT_LIST = Lists.newArrayList(MARKETING_EVENT_API_NAME,
            LEADS_API_NAME, ACCOUNT_API_NAME, CONTACT_API_NAME, NEW_OPPORTUNITY_API_NAME, NEW_OPPORTUNITY_LINES_API_NAME,
            NEW_OPPORTUNITY_CONTACTS_API_NAME, SALES_ORDER_API_NAME, QUOTE_API_NAME, CONTRACT_API_NAME, PRODUCT_API_NAME);
    public static final List<String> INIT_QUOTE_FIELD_OBJECT_LIST = Lists.newArrayList(SALES_ORDER_PRODUCT_API_NAME,
            PRICE_BOOK_PRODUCT_API_NAME, AVAILABLE_PRODUCT_API_NAME, PRICE_POLICY_PRODUCT_API_NAME);


    public static final String ATTITUDE= "attitude";
    public static final String CURRENT_ATTITUDE= "current_attitude";
    public static final String COMPREHENSIVE_ATTITUDE= "comprehensive_attitude";
    public static final String CHARACTER_CONTENT= "character_content";
    public static final String ATTENDEEINSIGHT_RECORD_ID= "attendeeinsight_record_id";
    public static final String BANT_BUDGET= "bant_budget";
    public static final String BUDGET_ACTIVE_RECORD_ID= "budget_active_record_id";
    public static final String BANT_AUTHORITY= "bant_authority";
    public static final String AUTHORITY_ACTIVE_RECORD_ID= "authority_active_record_id";
    public static final String BANT_NEED= "bant_need";
    public static final String NEED_ACTIVE_RECORD_ID= "need_active_record_id";
    public static final String BANT_TIME= "bant_time";
    public static final String TIME_ACTIVE_RECORD_ID= "time_active_record_id";
    public static final String MEETING_CONTENT= "meeting_content";
    public static final String INVISIBLE_CONTENT= "invisible_content";
    public static final String ATTITUDE_UPD_FIELD= "attitude_upd_field";
    public static final String ATTITUDE_UPD_TAG= "attitude_upd_tag";
    public static final String CHARACTER_UPD_TAG= "character_upd_tag";
    public static final String COMPREHENSIVE_ATTITUDE_IS_AI_UPD= "comprehensive_attitude_is_ai_upd";



    public static final String POSITION= "position";
    public static final String CONTACT_OUR_STRENGTH= "contact_our_strength";
}
