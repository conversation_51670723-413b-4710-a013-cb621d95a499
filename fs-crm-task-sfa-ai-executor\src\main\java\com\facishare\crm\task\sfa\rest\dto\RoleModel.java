package com.facishare.crm.task.sfa.rest.dto;

import com.facishare.crm.task.sfa.model.PAASContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface RoleModel {
    @Data
    @Builder
    class AddRoleArg {
        private PAASContext authContext;
        private String roleCode;
        private String roleName;
        private String groupCode;
        private String licenseCode;
        private String description;
        private int roleType;
    }

    @Data
    class AddRoleResult {
        int errCode;
        String errMessage;
        String result;
        boolean success;
    }
}
