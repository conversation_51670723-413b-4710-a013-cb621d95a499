package com.facishare.crm.task.sfa.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface ActiveRecordRestModel {
    @Data
    class AddArg {
        private ObjectDataDocument object_data;
    }

    @Data
    class AddResult {
        private String errCode;
        private String errMessage;
        private BaseObjectSaveActionResult result;
    }

    @Data
    class BaseObjectSaveActionResult {
        private ObjectDataDocument objectData;
        private Map<String, List<ObjectDataDocument>> newObjectData;
        private BaseObjectSaveAction.ValidationMessage validationRuleMessage;
        private Boolean isDuplicate;
    }
}
