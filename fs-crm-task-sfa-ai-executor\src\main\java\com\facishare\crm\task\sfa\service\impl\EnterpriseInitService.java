package com.facishare.crm.task.sfa.service.impl;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.CustomButtonServiceImpl;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserDefinedButtonService;
import com.facishare.paas.appframework.privilege.dto.ObjectDataPermissionInfo;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ILayoutRuleService;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.ErrorCode;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.exception.MetadataValidateException;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

import static com.facishare.crm.privilege.util.Constant.SYSTEM_OPT_USER_ID;
import static com.facishare.paas.appframework.privilege.util.PrivilegeConstants.ADMIN_ROLE_CODE;

@Component
@Slf4j
public class EnterpriseInitService {
    @Resource
    private IObjectDescribeService objectDescribeService;
    private ObjectDescribe getDescribeFromLocalResource(String apiName) {
        ObjectDescribe describe = new ObjectDescribe();
        //获取到此对象的json数据
        String jsonStr = getDescribeJsonFromResourceByApiName(apiName);
        describe.fromJsonString(jsonStr);
        return describe;
    }
    /**
     * 从本地resource种获取对应对象的预制json格式。
     *
     * @param apiName 对象apiName
     */
    private String getDescribeJsonFromResourceByApiName(String apiName) {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonstr;
        try {
            jsonstr = IOUtils.toString(classLoader.getResource(
                    "bizfeaturedescribe/init_crm_" + apiName + "_describe.json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initCRMDescribeByApiName file parse error");
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initCRMDescribeByApiName file parse error", e);
        }
        return jsonstr;
    }
    public void initDescribeForTenant(String tenantId, String describeApiName) throws MetadataServiceException {
        ObjectDescribe describe = getDescribeFromLocalResource(describeApiName);
        describe.setTenantId(tenantId);
        describe.setCreatedBy(SYSTEM_OPT_USER_ID);
        describe.setLastModifiedBy(SYSTEM_OPT_USER_ID);
        objectDescribeService.create(describe, true, false);
    }
}
