package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 评分规则常量
 *
 * <AUTHOR>
 */
public interface ScoringRuleConstants {
	/**
	 * 评分类型
	 */
	String SCORING_TYPE = "scoring_type";

	enum ScoringType {
		/**
		 * 权重
		 */
		ENUM("enum"),
		/**
		 * 范围
		 */
		RANGE("range"),
		/**
		 * 大模型
		 */
		LLM("llm");

		private final String scoringType;

		public String getScoringType() {
			return scoringType;
		}

		ScoringType(String scoringType) {
			this.scoringType = scoringType;
		}
	}

	/**
	 * 评分规则
	 */
	String RULE_CONTENT = "rule_content";
	/**
	 * 计算方法
	 */
	String CALC_METHOD = "calc_method";

	enum CalcMethodType {
		/**
		 * 打分器
		 */
		SCORING("scoring"),
		/**
		 * LLM
		 */
		LLM("llm");

		private String calcMethod;

		public String getCalcMethodType() {
			return calcMethod;
		}

		CalcMethodType(String calcMethod) {
			this.calcMethod = calcMethod;
		}
	}

	/**
	 * 默认值
	 */
	String DEFAULT_VALUE = "default_value";

	/**
	 * 系统数据类型
	 */
	String SYSTEM_TYPE = "system_type";

	enum SystemType {
		/**
		 * 系统
		 */
		SYSTEM("system"),
		/**
		 * 自定义
		 */
		UDEF("udef");

		private final String systemType;

		public String getSystemType() {
			return systemType;
		}

		SystemType(String systemType) {
			this.systemType = systemType;
		}
	}
}