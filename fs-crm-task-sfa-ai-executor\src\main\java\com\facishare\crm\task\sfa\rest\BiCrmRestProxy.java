package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.task.sfa.model.proxy.bi.FilterQuery;
import com.facishare.crm.task.sfa.model.proxy.bi.LwtViewDataQuery;
import com.facishare.crm.task.sfa.model.proxy.bi.LwtViewDetailDataQuery;
import com.facishare.crm.task.sfa.model.proxy.bi.RptHeaderQuery;
import com.facishare.crm.task.sfa.model.proxy.bi.StatViewDataQuery;
import com.facishare.crm.task.sfa.model.proxy.bi.StatViewDetailDataQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.HashMap;
import java.util.Map;

@RestResource(value = "BI-CRM", desc = "BI-CRM内部调用", contentType = "application/json")
public interface BiCrmRestProxy {

    @POST(value = "/api/v1/view/data/query", desc = "获取报表/统计图数据集")
    StatViewDataQuery.Result queryViewData(@Body StatViewDataQuery.Arg arg, @HeaderMap Map<String, String> headerMap);

    @POST(value = "/api/v1/view/detail/data/queryStatViewDetailData", desc = "获取统计图明细")
    StatViewDetailDataQuery.Result queryStatViewDetailData(@Body StatViewDetailDataQuery.Arg arg, @HeaderMap Map<String, String> headerMap);

    @POST(value = "/api/v1/view/data/queryLwtViewData", desc = "获取拼表数据集")
    LwtViewDataQuery.Result queryLwtViewData(@Body LwtViewDataQuery.Arg arg, @HeaderMap Map<String, String> headerMap);

    @POST(value = "/api/v1/view/detail/data/queryLwtViewDetailData", desc = "获取拼表明细")
    LwtViewDetailDataQuery.Result queryLwtViewDetailData(@Body LwtViewDetailDataQuery.Arg arg, @HeaderMap Map<String, String> headerMap);

    @POST(value = "/api/v1/view/getRptHeaderResult", desc = "针对一个图表查询返回所有表头字段")
    RptHeaderQuery.Result getRptHeaderResult(@Body RptHeaderQuery.Arg arg, @HeaderMap Map<String, String> headerMap);

    @POST(value = "/api/v1/view/getFiltersResult", desc = "针对一个图表查询返回所有筛选器")
    FilterQuery.Result getFiltersResult(@Body FilterQuery.Arg arg, @HeaderMap Map<String, String> headerMap);

    static Map<String, String> getHeaders(String tenantId) {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-fs-Enterprise-Account", tenantId);
        headers.put("X-fs-Enterprise-Id", tenantId);
        headers.put("X-fs-Employee-Id", User.SUPPER_ADMIN_USER_ID);
        headers.put("x-fs-ei", tenantId);
        return headers;
    }
}
