package com.facishare.crm.task.sfa.activitysummary.service.tag;

import com.alibaba.fastjson2.JSONObject;
import com.facishare.crm.task.sfa.activitysummary.constant.ActivityTagMap;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTag;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphTagResult;
import com.facishare.crm.task.sfa.activitysummary.model.TagProcessingResult;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.User;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标签处理服务
 * 负责处理标签的打标流程
 * 
 * 配置项说明：
 * - tag_partition_size: 标签分区大小，用于控制每次处理的标签数量（默认值：1）
 * - enable_judge_model: 是否启用裁判模型进行结果评估和优化（默认值：false）
 *   当设置为true时，标签分类结果将通过裁判模型进行质量评估和循环优化
 *   当设置为false时，直接返回AI模型的标签分类结果
 */
@Service
@Slf4j
public class TagProcessingService extends BaseTagProcessingService {

    @Autowired
    private CompletionsService completionsService;

    @Autowired
    private TagJudgeProcessor tagJudgeProcessor;

    // 获取分段建议的API
    private static final String API_NAME_TAG_SUGGESTIONS = "sfa_paragraph_tag_suggestions_prompt";
    private static final String SUGGESTIONS_JSON_FORMAT = "{\"suggestions\":{\"overallAnalysis\":{\"dialogTopic\":\"\",\"keyPoints\":\"\"},\"paragraphRecommendations\":[{\"paragraphId\":\"\",\"contentSummary\":\"\",\"recommendedTag\":\"\",\"reason\":\"\",\"keyInfo\":\"\",\"notes\":\"\"}],\"classificationStrategy\":{\"priorityRules\":\"\",\"boundaryHandling\":\"\",\"qualityAssurance\":\"\"}}}";

    private static int TAG_PARTITION;
    private static boolean ENABLE_JUDGE_MODEL;

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            TAG_PARTITION = config.getInt("tag_partition_size", 1);
            ENABLE_JUDGE_MODEL = config.getBool("tag_enable_judge_model", false);
        });
    }

    /**
     * 处理标签分类（使用配置的裁判模型设置）
     *
     * @param user                用户信息
     * @param processingResult 段落内容映射
     * @return 标签处理结果
     */
    public void processTags(User user, TagProcessingResult processingResult) {
        processTags(user, processingResult, ENABLE_JUDGE_MODEL);
    }

    /**
     * 处理标签分类（可指定是否启用裁判模型）
     *
     * @param user                用户信息
     * @param processingResult 段落内容映射
     * @param enableJudgeModel    是否启用裁判模型
     * @return 标签处理结果
     */
    public void processTags(User user,TagProcessingResult processingResult, boolean enableJudgeModel) {
        Map<String, String> paragraphContentsMap = processingResult.getContentChunks();
        if (paragraphContentsMap.isEmpty()) {
            return;
        }
        Map<String, List<ParagraphTagResult.TagResult>> tagResults = Maps.newHashMap();
        List<ActivityTag> allChildTags = ActivityTagMap.TAG_MAP.values()
                .stream()
                .map(ActivityTag::getActivityTags)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        // 核心思路：为每个标签，通过轻量的语义搜索，快速筛选出可能相关的段落子集，
        // 然后再将这个较小的"段落-标签"组合送给大模型进行精细判断，以减少API调用负载。
        List<List<ActivityTag>> partition = Lists.partition(allChildTags, TAG_PARTITION);
        for (List<ActivityTag> activityTags : partition) {
            try {
                // 1. 获取该标签分区的分段建议
                //String suggestions = getTagSuggestions(user, paragraphContentsMap, activityTags);
                
                // 2. 仅将相关段落和标签送入大模型进行精细判断，并传递建议
                Map<String, ParagraphTagResult.TagResult> singleTagResult = processTag(user, paragraphContentsMap, activityTags, "", enableJudgeModel);
                // 合并结果
                singleTagResult.forEach((paragraphId, tagResultItem) -> {
                    tagResults.computeIfAbsent(paragraphId, k -> Lists.newArrayList()).add(tagResultItem);
                });
            } catch (Exception e) {
                log.error("Error processing tag: {}", activityTags.stream().map(ActivityTag::getId).collect(Collectors.joining(",")), e);
            }
        }
        processingResult.setTagResults(tagResults);
    }

    private Map<String, ParagraphTagResult.TagResult> processTag(User user, Map<String, String> paragraphContentsChunk, List<ActivityTag> tagList, String suggestions, boolean enableJudgeModel) {
        if (paragraphContentsChunk.isEmpty()) {
            return Maps.newHashMap();
        }

        try {
            // 请求大模型
            Map<String, ParagraphTagResult.TagResult> tagResult = tagJudgeProcessor.executeTagClassification(user, paragraphContentsChunk, tagList, suggestions);

            // 如果启用裁判模型，则使用TagJudgeProcessor对已有结果进行评判和优化
            if (enableJudgeModel) {
                return tagJudgeProcessor.processWithJudge(user, paragraphContentsChunk, tagList, tagResult);
            }

            return tagResult;
        } catch (Exception e) {
            log.error("Error processing single tag chunk for tag: {}", tagList.stream().map(x -> x.getId() + ":" + x.getName()).collect(Collectors.joining(",")), e);
            return Maps.newHashMap();
        }
    }

    /**
     * 获取标签分类的建议
     *
     * @param user 用户信息
     * @param paragraphContentsMap 段落内容映射
     * @param tagList 当前标签列表
     * @return 建议内容
     */
    private String getTagSuggestions(User user, Map<String, String> paragraphContentsMap, List<ActivityTag> tagList) {
        try {
            // 构建内容列表
            List<Map<String, Object>> contentList = buildContentList(paragraphContentsMap);
            // 构建单个标签的列表
            List<Map<String, Object>> tagsList = buildTagsList(tagList);

            // 构建请求参数
            AiRestProxyModel.Arg arg = buildBaseTagArgument(API_NAME_TAG_SUGGESTIONS, contentList, tagsList);

            // 请求大模型获取建议
            log.info("Requesting suggestions for tags: {}", tagList.stream().map(x -> x.getId() + ":" + x.getName()).collect(Collectors.joining(",")));
            
            // 获取结构化的建议响应
            JSONObject response = completionsService.requestCompletionData(user, arg, SUGGESTIONS_JSON_FORMAT, JSONObject.class);
            
            JSONObject suggestions = response.getJSONObject("suggestions");
            log.info("Got structured suggestions: {}", suggestions);
            
            // 将结构化建议转换为字符串，供后续处理使用
            return suggestions != null ? suggestions.toJSONString() : "";
        } catch (Exception e) {
            log.error("Error getting tag suggestions for tags: {}", tagList.stream().map(x -> x.getId() + ":" + x.getName()).collect(Collectors.joining(",")), e);
            return "";
        }
    }

    /**
     * 使用向量搜索或语义搜索，为给定的标签筛选出相关的段落。
     * <p>
     * <b>注意：这是一个待实现的占位符方法。</b> 实际实现需要调用文本嵌入（Embedding）模型和向量检索引擎。
     * <ul>
     *     <li><b>实现步骤：</b></li>
     *     <li>1. 将标签的名称或描述（{@code tag.getName()}）向量化，得到一个查询向量 (Query Vector)。</li>
     *     <li>2. 批量将 {@code allParagraphs} 中的所有段落内容向量化，得到段落向量集合。</li>
     *     <li>3. 在段落向量集合中，计算每个段落向量与查询向量的语义相似度（如余弦相似度）。</li>
     *     <li>4. 筛选出相似度得分高于预设阈值的段落，或者返回Top-K个最相似的段落。</li>
     * </ul>
     *
     * @param tagList           当前要处理的标签
     * @param allParagraphs 所有的段落Map
     * @return 经过筛选的、与标签语义相关的段落Map。如果未实现，则返回原始的全量段落。
     */
    private Map<String, String> filterRelevantParagraphsForTag(List<ActivityTag> tagList, Map<String, String> allParagraphs) {
        // TODO: 此处应替换为真实的向量搜索/语义匹配逻辑。
        // 当前作为占位符，我们暂时返回所有段落，以确保流程完整。
        // 在生产环境中，若无相关段落，应返回空Map。
        return allParagraphs;
    }
}
