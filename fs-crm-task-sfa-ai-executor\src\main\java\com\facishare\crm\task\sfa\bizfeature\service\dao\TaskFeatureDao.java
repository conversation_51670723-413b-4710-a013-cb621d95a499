package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.facishare.crm.task.sfa.bizfeature.constant.*;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TaskFeatureDao {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private FeatureDao featureDao;
    @Autowired
    private TaskInstanceDao taskInstanceDao;
    @Autowired
    private InstanceFeatureDao instanceFeatureDao;
    /**
     * 查询特征
     */
    public List<IObjectData> fetchTaskFeaturesByMethodologyId(User user, String methodologyId) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, TaskFeatureConstants.METHODOLOGY_ID, methodologyId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, TaskFeatureConstants.STATUS, TaskFeatureConstants.StatusType.ENABLE.getStatusType());
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.TASK_FEATURE, query).getData();
    }

    public List<IObjectData> fetchTaskFeaturesByFeatureIds(User user, List<String> featureIds) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, TaskFeatureConstants.FEATURE_ID, featureIds);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, TaskFeatureConstants.STATUS, TaskFeatureConstants.StatusType.ENABLE.getStatusType());
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.TASK_FEATURE, query).getData();
    }

    /**
     * 根据任务ID列表查询任务特征关联
     */
    public List<IObjectData> fetchTaskFeaturesByTaskIds(User user, List<String> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Lists.newArrayList();
        }
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, TaskFeatureConstants.TASK_ID, taskIds);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, TaskFeatureConstants.STATUS, TaskFeatureConstants.StatusType.ENABLE.getStatusType());
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.TASK_FEATURE, query).getData();
    }

    /**
     * 根据类型、节点ID和维度ID过滤特征
     */

    public List<IObjectData> getTaskFeaturesByMethodologyInstanceId(User user, String methodologyInstanceId) {
        List<IObjectData> instanceFeatureList = instanceFeatureDao.fetchInstanceFeaturesByMethodologyInstanceId(user, methodologyInstanceId);

        List<String> featureIds = instanceFeatureList.stream()
                .map(x -> x.get(InstanceFeatureConstants.FEATURE_ID, String.class)).collect(Collectors.toList());
        return fetchTaskFeaturesByFeatureIds(user, featureIds);
    }
}
