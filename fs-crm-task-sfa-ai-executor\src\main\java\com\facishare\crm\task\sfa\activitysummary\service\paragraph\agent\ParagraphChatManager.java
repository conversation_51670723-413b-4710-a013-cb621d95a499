package com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent;

import com.facishare.crm.task.sfa.activitysummary.model.ChatHistory;
import com.facishare.crm.task.sfa.activitysummary.service.ChatBot;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 段落分割业务的对话管理器
 * 基于通用ChatBot，专门为段落分割业务提供对话管理功能
 */
@Component
public class ParagraphChatManager {

    @Autowired
    private ChatBot chatBot;

    /**
     * 多个对话历史的容器
     * 专门用于段落分割业务的三个AI Agent
     */
    public static class ChatHistories {
        private ChatHistory plannerHistory;
        private ChatHistory executorHistory;
        private ChatHistory evaluatorHistory;

        public ChatHistories(ChatBot chatBot) {
            this.plannerHistory = chatBot.createChatHistory();
            this.executorHistory = chatBot.createChatHistory();
            this.evaluatorHistory = chatBot.createChatHistory();
        }

        public ChatHistory getPlannerHistory() {
            return plannerHistory;
        }

        public ChatHistory getExecutorHistory() {
            return executorHistory;
        }

        public ChatHistory getEvaluatorHistory() {
            return evaluatorHistory;
        }
    }

    /**
     * 创建新的段落分割对话历史集合
     */
    public ChatHistories createChatHistories() {
        return new ChatHistories(chatBot);
    }

    /**
     * 获取ChatBot实例
     */
    public ChatBot getChatBot() {
        return chatBot;
    }
} 