package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.task.sfa.rest.dto.StoneAuthModels;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2025/1/2 17:50
 * @description:
 */
@RestResource(value = "FS_BIG_FILE", desc = "fs big file manager", contentType = "application/json")
public interface FsBigFileManagerProxy {

    @GET(value = "/fs-big-file-manager-biz/api/generateDownloadSignatureUrlByAuth?employeeAccount={employeeAccount}&employeeId={employeeId}&path={path}&expireTime={expireTime}", desc = "获取文件下载签名URL")
    StoneAuthModels.GenerateDownloadUrlResponse generateDownloadUrl(@HeaderMap Map<String, String> headers, @PathParams Map<String, String> pathParams);


    /**
     * https://apifox.com/apidoc/shared-5115d6a8-41a0-48cf-9b4c-a1b864e9c2c2?pwd=TGGulVvr
     *
     * {
     *     "employeeAccount": "7",
     *     "employeeId": 2257,
     *     "fileName": "testxmind-windows-3.5.2.************.exe",
     *     "business": "sfa",
     *     "fileExpireDay": 0,
     *     "objectKey": "7/1000/bc63447cca494739945e59691839c613.exe"
     * }
     *
     * @param headers
     * @param pathParams
     * @return
     */
    @POST(value = "/fs-big-file-manager-biz/api/generationPathByObjectKey", desc = "通过objectKey等对象存储信息生成大附件path")
    StoneAuthModels.GenerationPathByObjectKeyResponse generationPathByObjectKey(@HeaderMap Map<String, String> headers, @Body Map<String, Object> pathParams);

    /**
     * 获取音频大附件信息
     *
     * @param ea ea!
     * @param userId   1000
     * @param filePath ALIOSS_95da659a5dab46769562180d3fad9632
     * @return AudioData
     */
    @GET(value = "/fs-big-file-manager-biz/api/getAudioInfo", desc = "获取音频大附件信息")
    StoneAuthModels.AudioInfo getAudioInfo(@QueryParam("employeeAccount") String ea, @QueryParam("employeeId") String userId, @QueryParam("filePath") String filePath);
}
