package com.facishare.crm.task.sfa.services.rebate.rest;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 调用规则引擎业务逻辑接口类
 *
 * <AUTHOR>
 */
public interface RuleEngineLogicService {


    /**
     * 匹配规则条件
     *
     * @param user
     * @param pricePolicyIds
     * @param masterApiName
     * @param masterData
     *
     * @return
     */
    Set<String> matchRuleCondition(User user,
            List<String> pricePolicyIds,
            String masterApiName,
            IObjectData masterData);

    /**
     * 批量获取聚合值
     *
     * @param user
     * @param aggregateRuleIds
     * @param masterApiName
     * @param masterData
     * @param detailDataList
     * @return
     */
    Map<String, String> computeAggregateValues(User user,
            Set<String> aggregateRuleIds,
            String masterApiName,
            IObjectData masterData,
            List<IObjectData> detailDataList);
}
