package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.util.FeatureBaseDataUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BaseDataMergeManager {
    @Autowired
    private ServiceFacade serviceFacade;
    public IObjectData findObjectData(User user, String objectId, String objectDescribeApiName) {
        IObjectData objectData = serviceFacade.findObjectData(user, objectId, objectDescribeApiName);
        /*if (FeatureBaseDataUtils.vaiableApiNames.contains(objectDescribeApiName)) {
            FeatureBaseDataUtils.handFeatureBaseData(objectDescribeApiName, Lists.newArrayList(objectData));
        }*/
        return objectData;
    }

    public IObjectData findObjectDataIgnoreAll(User user, String objectId, String objectDescribeApiName) {
        IObjectData objectData = serviceFacade.findObjectDataIgnoreAll(user, objectId, objectDescribeApiName);
        /*if (FeatureBaseDataUtils.vaiableApiNames.contains(objectDescribeApiName)) {
            FeatureBaseDataUtils.handFeatureBaseData(objectDescribeApiName, Lists.newArrayList(objectData));
        }*/
        return objectData;
    }

    public List<IObjectData> findBySearchQueryIgnoreAll(User user, String objectDescribeApiName, SearchTemplateQuery searchTemplateQuery) {
        List<IObjectData> objectFeatureDataList = serviceFacade
                .findBySearchQueryIgnoreAll(user, objectDescribeApiName, searchTemplateQuery).getData();
        /*if (FeatureBaseDataUtils.vaiableApiNames.contains(objectDescribeApiName)) {
            FeatureBaseDataUtils.handFeatureBaseData(objectDescribeApiName, objectFeatureDataList);
        }*/
        return objectFeatureDataList;
    }

    public List<IObjectData> findObjectDataByIdsIgnoreAll(User user, List<String> objectIds, String objectDescribeApiName) {
        List<IObjectData> objectFeatureDataList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), objectIds, objectDescribeApiName);
        /*if (FeatureBaseDataUtils.vaiableApiNames.contains(objectDescribeApiName)) {
            FeatureBaseDataUtils.handFeatureBaseData(objectDescribeApiName, objectFeatureDataList);
        }*/
        return objectFeatureDataList;
    }
}
