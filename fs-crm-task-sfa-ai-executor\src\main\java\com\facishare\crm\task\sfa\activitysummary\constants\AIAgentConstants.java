package com.facishare.crm.task.sfa.activitysummary.constants;

/**
 * AI Agent常量类
 * 存储AI Agent相关的常量
 * @IgnoreI18nFile
 */
public class AIAgentConstants {
    // API名称常量
    public static final String API_NAME_PLANNER = "prompt_meeting_paragraph_planner"; // Planner API
    public static final String API_NAME_EXECUTOR = "prompt_meeting_paragraph_executor"; // Executor API
    public static final String API_NAME_EVALUATOR = "prompt_meeting_paragraph_evaluator"; // Evaluator API
    
    // 场景变量键名常量
    public static final String MEET_CONTENT_KEY = "custom_sence.meetingContent";
    public static final String PREVIOUS_SUMMARY_KEY = "custom_sence.lastSummary";
    public static final String PLAN_STRATEGY_KEY = "custom_sence.plan_strategy";
    public static final String PLAN_EXPECTED_SEGMENTS_KEY = "custom_sence.plan_expected_segments";
    public static final String PLAN_FOCUS_POINTS_KEY = "custom_sence.plan_focus_points";
    public static final String PLAN_SPECIAL_HANDLING_KEY = "custom_sence.plan_special_handling";
    public static final String SEGMENTS_KEY = "custom_sence.segments";
    public static final String EVALUATION_KEY = "custom_sence.evaluation";
    
    // JSON格式示例常量
    public static final String JSON_FORMAT_RESULT = "{\"chunks\":[{\"content\":[\"id1\",\"id2\"],\"summary\":\"本段总结\",\"self_score\":0.95,\"needs_high_level_model\":false}]}";
    public static final String JSON_FORMAT_PLAN = "{\"plan\":{\"strategy\":\"基于主题分段\",\"expected_segments\":5,\"focus_points\":[\"关键词密度\",\"主题变化\",\"语义连贯性\"],\"special_handling\":[]}}";
    public static final String JSON_FORMAT_SEGMENTS = "{\"segments\":[{\"segment_id\":\"1\",\"content_ids\":[\"id1\",\"id2\"],\"summary\":\"段落摘要\",\"confidence\":0.95}]}";
    public static final String JSON_FORMAT_EVALUATION = "{\"evaluation\":{\"overall_score\":0.92,\"issues\":[],\"suggestions\":[],\"needs_revision\":false}}";
    
    // 配置常量
    public static final int MAX_RETRY_COUNT = 3; // 整体重试次数
    public static final int MAX_ADJUSTMENT_ROUNDS = 2; // 最大调整轮数
    public static final double QUALITY_THRESHOLD = 0.9; // 质量评分阈值
    public static final double IMPROVEMENT_THRESHOLD = 0.05; // 改进阈值，低于此值认为没有显著改进
}
