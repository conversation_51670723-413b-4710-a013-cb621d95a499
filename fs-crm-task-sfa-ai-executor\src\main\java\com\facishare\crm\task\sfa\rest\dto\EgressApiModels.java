package com.facishare.crm.task.sfa.rest.dto;

import lombok.Data;

/**
 * Egress API 相关的请求响应模型
 */
public interface EgressApiModels {

    enum TaskStatus {
        INIT("init"),
        WAITING("waiting"), 
        DOING("doing"),
        SUCCESS("success"),
        FAILED("failed");

        private final String value;

        TaskStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 语音识别任务相关模型
     */
    interface AsrRecTask {
        @Data
        class Request {
            private String engineModelType;
            private Integer channelNum;
            private Integer speakerDiarization;
            private Integer speakerNumber;
            private String url;
            private String data;
        }

        @Data
        class Response {
            private Integer code;
            private String message;
            private TaskData data;
        }

        @Data
        class TaskData {
            private String taskId;
        }

        @Data
        class TaskResult {
            private Integer code;
            private String message;
            private TaskResultData data;
        }

        @Data
        class TaskResultData {
            /**
             * 任务ID
             */
            private String taskId;

            /**
             * 当前状态，只有状态是 success 时才有转换文字结果
             * <p>
             * init：提交到运营商成功，首次初始化
             * waiting：等待转换
             * doing：正在转换
             * success：转换成功
             * failed：转换失败
             */
            private String status;

            /**
             * 转换后的文本信息，只有转换成功后才有
             */
            private String text;

            /**
             * 提示信息，如错误原因，与status对应
             */
            private String message;

            /**
             * 音频时长(毫秒)，此字段可能返回 null，表示取不到有效值。
             */
            private Long audioDuration;
        }
    }
} 