package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 段落响应模型
 * 用于AI Agent的Executor组件，包装分段结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SegmentsResponse {
    /**
     * 段落列表
     * Executor组件生成的段落列表
     */
    private List<SegmentModel> segments;
}
