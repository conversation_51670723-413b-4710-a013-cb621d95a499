package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ParseRuleConstants;

import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.crm.task.sfa.services.rebate.rest.RuleEngineLogicService;
import com.facishare.paas.appframework.metadata.exception.FieldNotExistException;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.expression.*;
import com.facishare.paas.metadata.api.IObjectData;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ParseFormulaRule extends AbsParseRuleService {
    @Resource
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Resource
    private RuleEngineLogicService engineLogicService;
    @Resource
    private ParseServiceManager serviceManager;

    @Override
    public String getRuleType() {
        return ParseRuleConstants.CalcMethodType.FORMULA.getCalcMethodType();
    }

    @Override
    protected FeatureModel.ParseValueData getValue(User user, IObjectData feature, IObjectData rule, IObjectData data, IObjectDescribe dataDescribe) {
        StopWatch stopWatch = StopWatch.createStarted(getRuleType());
        String[] fields = this.getFields(feature);
        String ruleString = rule.get(ParseRuleConstants.RULE_CONTENT, String.class);
        FeatureModel.ParseBaseRule parseLLMRule = JSONObject.parseObject(ruleString, FeatureModel.ParseBaseRule.class);
        String content = parseLLMRule.getRule();

        String id = data.getId();
        stopWatch.lap("getDescribe");
        // 获取高级计算公式
        SimpleExpression simpleExpression = SimpleExpression.builder().expression(content).id(id).build();
        for (int i = 0; i < fields.length; i++) {
            String field = fields[i];
            simpleExpression.setExpression(
                    simpleExpression.getExpression().replaceAll("\\$field" + i + "\\$", "\\$" + field + "\\$"));
        }
        String returnDataType = rule.get(ParseRuleConstants.RETURN_DATA_TYPE, String.class);
        simpleExpression.setReturnType(ParseRuleConstants.ReturnDataType.changeToFieldType(returnDataType));

        Map<String, Object> extData = new HashMap<>();

        if (content.contains("EXT#AGGR#")) {
            extData = getAggregateIdToValue(user, data, dataDescribe, simpleExpression);
            stopWatch.lap("getAggregateIdToValue");
        }
        if (content.contains("EXT#COMMONAGG#")) {
            extData = getCommonAggIdToValue(user, dataDescribe, simpleExpression, feature, data, dataDescribe);
            stopWatch.lap("getCommonAggIdToValue");
        }
        Object value;
        try {
            // 调接口执行计算，calculateResult的key是"test_exp__c"，value是计算结果
            Map<String, Object> calculateResult = expressionCalculateLogicService.calculateWithExpression(dataDescribe,
                    data, extData, Lists.newArrayList(simpleExpression));
            value = calculateResult.get(simpleExpression.getId());
            stopWatch.lap("calculateWithExpression" + content);
        } catch (FieldNotExistException e) {
            log.warn("公式解析错误,字段不存在", e);
            return null;
        }
        stopWatch.logSlow(100);

        FeatureModel.ParseValueData ret = new FeatureModel.ParseValueData();
        ret.setObjectApiName(data.getDescribeApiName());
        ret.setObjectId(data.getId());
        Map<String, Object> srcValue = getSrcObjectMap(data, fields);
        ret.setValue(value);
        ret.setTriggerValue(srcValue);
        // 取出计算结果
        return ret;
    }

    private Map<String, Object> getAggregateIdToValue(User user, IObjectData data, IObjectDescribe objectDescribe,
            SimpleExpression simpleExpression) {
        Map<String, Object> extData = new HashMap<>();
        // 构造Expression并解析自定义变量
        Expression expression = ExpressionFactory.createExpression(objectDescribe,
                simpleExpression);
        List<ExpressionVariableFactory.ExtVariable> extVariables = expression.getExtVariablesByType("AGGR");
        List<SimpleExpression.VariableInfo> extVariableInfos = extVariables.stream()
                .map(x -> SimpleExpression.VariableInfo.of(x.getName(), "number"))
                .collect(Collectors.toList());
        simpleExpression.setExtVariables(extVariableInfos);
        //
        // 查询并绑定自定义变量的值，此时aggrIds是从"EXE#AGGR#rule1"里解析出来的rule1，extData的key是"EXE#AGGR#rule1"，value是rule1的值。
        List<String> aggregateIds = extVariables.stream().map(ExpressionVariableFactory.ExtVariable::getVariableBizId)
                .collect(Collectors.toList());
        Map<String, String> aggregateIdToValue = engineLogicService.computeAggregateValues(user,
                Sets.newHashSet(aggregateIds),
                data.getDescribeApiName(),
                data,
                null);
        aggregateIdToValue.forEach((aggregateId, value) -> {
            extData.put("EXT#AGGR#".concat(aggregateId), value);
        });

        return extData;
    }

    private Map<String, Object> getCommonAggIdToValue(User user, IObjectDescribe objectDescribe,
            SimpleExpression simpleExpression, IObjectData feature, IObjectData data, IObjectDescribe dataDescribe) {
        // 构造Expression并解析自定义变量
        Expression expression = ExpressionFactory.createExpression(objectDescribe,
                simpleExpression);
        List<ExpressionVariableFactory.ExtVariable> extVariables = expression.getExtVariablesByType("COMMONAGG");
        List<SimpleExpression.VariableInfo> extVariableInfos = extVariables.stream()
                .map(x -> SimpleExpression.VariableInfo.of(x.getName(), "number"))
                .collect(Collectors.toList());
        simpleExpression.setExtVariables(extVariableInfos);
        Map<String, Object> extData = new HashMap<>();

        // 查询并绑定自定义变量的值，此时aggrIds是从"EXE#AGGR#rule1"里解析出来的rule1，extData的key是"EXE#AGGR#rule1"，value是rule1的值。
        List<String> aggregateIds = extVariables.stream().map(ExpressionVariableFactory.ExtVariable::getVariableBizId)
                .collect(Collectors.toList());
        String aggregateId = aggregateIds.get(0);

        IObjectData rule = serviceFacade
                .findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(aggregateId),
                        FeatureConstants.PARSE_RULE)
                .get(0);

        FeatureModel.FeatureData value = serviceManager.getActionService(ParseRuleConstants.CalcMethodType.COMMON_AGG.getCalcMethodType()).parse(user, feature, rule, data, dataDescribe);

        if (value == null) {
            extData.put("EXT#COMMONAGG#".concat(aggregateId), BigDecimal.ZERO);
        }else {
            extData.put("EXT#COMMONAGG#".concat(aggregateId), value.getValueNumber());
        }

        return extData;
    }

}
