package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileItemScoreConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProfileItemScoreDao {

    @Autowired
    private ServiceFacade serviceFacade;
    /**
     * 查询画像分项得分
     */
    public List<IObjectData> fetchProfileItemScoresByProfileId(User user, String profileId) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileItemScoreConstants.PROFILE_ID, profileId);
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.PROFILE_ITEM_SCORE, query).getData();
    }
    public IObjectData fetchProfileItemScoreByNodeId(User user, String profileId, String currentNodeId) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileItemScoreConstants.PROFILE_ID, profileId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileItemScoreConstants.NODE_ID, currentNodeId);

        List<IObjectData> profileItemScoreList = serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.PROFILE_ITEM_SCORE, query).getData();
        return CollectionUtils.isEmpty(profileItemScoreList) ? null : profileItemScoreList.get(0);
    }
}
