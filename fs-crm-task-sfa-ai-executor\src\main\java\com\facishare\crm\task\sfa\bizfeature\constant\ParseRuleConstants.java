package com.facishare.crm.task.sfa.bizfeature.constant;

import com.facishare.paas.metadata.api.describe.IFieldType;

/**
 * 解析规则常量
 *
 * <AUTHOR>
 */
public interface ParseRuleConstants {
	/**
	 * 规则类型
	 */
	String RULE_TYPE = "rule_type";

	enum RuleType {
		/**
		 * 字段
		 */
		FIELD("field"),
		/**
		 * 时序
		 */
		TIME_SERIES("time_series"),
		/**
		 * 统计
		 */
		STATISTICS("statistics"),
		/**
		 * 文本
		 */
		TEXT("text");

		private final String ruleType;

		public String getRuleType() {
			return ruleType;
		}

		RuleType(String ruleType) {
			this.ruleType = ruleType;
		}
	}

	/**
	 * 数据源类型
	 */
	String DATA_SOURCE_TYPE = "data_source_type";

	enum DataSourceType {
		/**
		 * 内部
		 */
		INTERNAL("internal"),
		/**
		 * 外部
		 */
		EXTERNAL("external"),
		/**
		 * 第三方
		 */
		THIRD_PARTY("third-party");

		private final String dataSourceType;

		public String getDataSourceType() {
			return dataSourceType;
		}

		DataSourceType(String dataSourceType) {
			this.dataSourceType = dataSourceType;
		}
	}

	/**
	 * 返回数据类型
	 */
	String RETURN_DATA_TYPE = "return_data_type";

	enum ReturnDataType {
		/**
		 * 布尔值
		 */
		BOOL("bool"),
		/**
		 * 文本
		 */
		TEXT("text"),
		/**
		 * 数值
		 */
		NUMERIC("numeric");

		private final String returnDataType;

		public String getReturnDataType() {
			return returnDataType;
		}

		ReturnDataType(String returnDataType) {
			this.returnDataType = returnDataType;
		}

		public static String changeToFieldType(String returnDataType) {
			if (returnDataType.equals(BOOL.returnDataType)) {
				return IFieldType.TRUE_OR_FALSE;
			} else if (returnDataType.equals(TEXT.returnDataType)) {
				return IFieldType.TEXT;
			} else if (returnDataType.equals(NUMERIC.returnDataType)) {
				return IFieldType.NUMBER;
			}
			return IFieldType.TEXT;
		}
	}

	/**
	 * 计算方法
	 */
	String CALC_METHOD = "calc_method";

	enum CalcMethodType {
		/**
		 * 字段
		 */
		FIELD("field"),
		/**
		 * 公式
		 */
		FORMULA("formula"),
		/**
		 * 聚合
		 */
		AGGREGATION("aggregation"),
		/**
		 * 自定义查询
		 */
		CUSTOM_QUERY("custom_query"),
		/**
		 * 语义
		 */
		SEMANTIC("semantic"),

		/**
		 * 标注
		 */
		TAG("tag"),
		/**
		 * Activity聚合
		 */
		ACTIVITY_AGG("activity_agg"),
		/**
		 * 通用聚合
		 */
		COMMON_AGG("common_agg"),

		/**
		 * 仅是才处理的公式
		 */
		TRUE_FORMULA("true_formula"),
		/**
		 * 时序
		 */
		TIME_SERIES("time_series");

		private final String calcMethod;

		public String getCalcMethodType() {
			return calcMethod;
		}

		CalcMethodType(String calcMethod) {
			this.calcMethod = calcMethod;
		}
	}

	/**
	 * 规则内容
	 */
	String RULE_CONTENT = "rule_content";

	/**
	 * 系统数据类型
	 */
	String SYSTEM_TYPE = "system_type";

	enum SystemType {
		/**
		 * 系统
		 */
		SYSTEM("system"),
		/**
		 * 自定义
		 */
		UDEF("udef");

		private final String systemType;

		public String getSystemType() {
			return systemType;
		}

		SystemType(String systemType) {
			this.systemType = systemType;
		}
	}
}