package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.ActivityParagraphService;
import com.facishare.crm.task.sfa.util.SFAConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Component
@Slf4j
public class ActivityParagraphListener extends AbstractActivityCommonListener {

    @Resource
    private ActivityParagraphService activityParagraphService;

    @Override
    String getSection() {
        return "sfa-ai-activity-paragraph";
    }

    @Override
    void consume(ActivityMessage activityMessage) {
        String stage = activityMessage.getStage();
        if (ObjectUtils.isEmpty(stage)) {
            return;
        }
        if(!SFAConfigUtil.isOpenCustomerProfileAgent(activityMessage.getTenantId())){
            log.info("no customer profile agent, tenantId:{}", activityMessage.getTenantId());
            return;
        }

        log.info("ActivityParagraphListener activityMessage:{}", JSON.toJSONString(activityMessage));
        switch (stage) {
            // 附件转文本
            case "file2text":
                activityParagraphService.consumerFileToText(activityMessage);
                break;
            // 实时音频转文字(全部结束时)
            case "realtime2textDone":
                activityParagraphService.consumerRealtimeToTextDone(activityMessage);
                break;
            default:
                break;
        }
    }
}
