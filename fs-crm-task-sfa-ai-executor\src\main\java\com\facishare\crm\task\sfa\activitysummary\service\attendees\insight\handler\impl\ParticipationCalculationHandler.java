package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl;

import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ParticipationCalculationHandler extends AbstractInsightHandler<AttendeesInsightModel.InsightResult> {

    @Override
    public String getInsightType() {
        return "participation_calculation";
    }

    @Override
    public void insight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        doInsight(attendeesInsightMessage);
    }

    @Override
    public void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {

        AttendeesInsightModel.AttendeesInsightExtendData extendData = attendeesInsightMessage.getExtendData();
        List<InteractiveDocument> documents = extendData.getDocuments();
        List<IObjectData> activityUserList = extendData.getActivityUserList();
//        List<IObjectData> ourSideUsers = activityUserList.stream()
//                .filter(u -> "our_side".equals(u.get("participant_types")))
//                .collect(Collectors.toList());
//
//        List<IObjectData>  theirSideUsers = activityUserList.stream()
//                .filter(u -> "their_side".equals(u.get("participant_types")))
//                .collect(Collectors.toList());

        calculationParticipation(attendeesInsightMessage, activityUserList, documents);
//        calculationParticipation(attendeesInsightMessage, theirSideUsers, documents);
    }

    private void calculationParticipation(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, List<IObjectData> activityUserList, List<InteractiveDocument> documents) {
        Map<String, BigDecimal> userMap = Safes.of(activityUserList).stream()
                .collect(Collectors.toMap(IObjectData::getId, a -> BigDecimal.ZERO));

        if (userMap.size() == 1) {
            // 只有一个员工，100%参与度
            for (IObjectData user : activityUserList) {
                user.set("participation_proportion", BigDecimal.valueOf(100));
            }
            serviceFacade.batchUpdateByFields(User.systemUser(attendeesInsightMessage.getTenantId()), activityUserList, Lists.newArrayList("participation_proportion"));
            return;
        }


        BigDecimal total = BigDecimal.ZERO;
        documents = documents.stream().filter(d -> userMap.containsKey(d.getActivityUserId())).collect(Collectors.toList());
        for (InteractiveDocument document : documents) {
            String content = document.getContent();
            int wordCount = countWords(content);
            String userId = document.getActivityUserId();
            if (userMap.containsKey(userId)) {
                userMap.put(userId, userMap.get(userId).add(BigDecimal.valueOf(wordCount)));
                total = total.add(BigDecimal.valueOf(wordCount));
            }
        }

        for (IObjectData user : activityUserList) {
            BigDecimal userWords = userMap.get(user.getId());
            BigDecimal proportion = userWords.divide(total, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            user.set("participation_proportion", proportion);
        }

        serviceFacade.batchUpdateByFields(User.systemUser(attendeesInsightMessage.getTenantId()), activityUserList, Lists.newArrayList("participation_proportion"));
    }


    /**
     * 计算字符串字数：中文单字+1，英文连续字母词+1（忽略数字/符号）
     * @param text 输入字符串（支持中英文混合、标点、空格）
     * @return 总字数
     */
    public static int countWords(String text) {
        int count = 0;
        boolean inEnglishWord = false; // 标记是否处于英文单词中

        for (char c : text.toCharArray()) {
            // 1. 中文字符（基本汉字区）
            if (isChinese(c)) {
                count++;
                inEnglishWord = false; // 中文后重置英文状态
            }
            // 2. 英文字母（区分大小写，仅字母视为单词部分）
            else if (Character.isLetter(c)) {
                if (!inEnglishWord) { // 新英文单词开始
                    count++;
                    inEnglishWord = true;
                }
            }
            // 3. 其他字符（数字、标点、空格等）：结束英文单词
            else {
                inEnglishWord = false;
            }
        }
        return count;
    }

    /** 判断是否为中文字符（基本汉字区） */
    private static boolean isChinese(char c) {
        return c >= '\u4E00' && c <= '\u9FFF';
    }
}
