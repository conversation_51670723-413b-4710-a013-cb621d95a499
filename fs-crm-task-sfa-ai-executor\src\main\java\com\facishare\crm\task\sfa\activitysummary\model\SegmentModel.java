package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 段落模型
 * 用于AI Agent的Executor组件，表示一个分段的内容和属性
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SegmentModel {
    /**
     * 段落ID
     * 用于唯一标识一个段落，通常是数字或字符串
     */
    private String segmentId;
    
    /**
     * 内容ID列表
     * 该段落包含的原始内容ID列表，用于追踪原始内容
     */
    private List<String> contentIds;
    
    /**
     * 段落摘要
     * 对该段落内容的简要总结
     */
    private String summary;
    
    /**
     * 置信度
     * 表示模型对该分段结果的置信程度，范围0-1
     */
    private double confidence;
}
