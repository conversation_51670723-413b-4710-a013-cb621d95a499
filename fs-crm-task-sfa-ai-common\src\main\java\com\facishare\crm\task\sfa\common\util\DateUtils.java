package com.facishare.crm.task.sfa.common.util;

import org.jetbrains.annotations.NotNull;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

public class DateUtils {
    /**
     * 获取指定日期的开始时间（00:00:00）
     *
     * @param date 指定日期
     * @return 该日期的开始时间
     */
    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取一周前的日期
     *
     * @return 一周前的Date对象
     */
    public static Date getOneWeekAgo() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.WEEK_OF_YEAR, -1);
        return calendar.getTime();
    }

    /**
     * 获取两周前的日期
     *
     * @return 两周前的Date对象
     */
    public static Date getTwoWeekAgo() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.WEEK_OF_YEAR, -2);
        return calendar.getTime();
    }

    /**
     * 获取一个月前的日期
     *
     * @return 一个月前的Date对象
     */
    public static Date getOneMonthAgo() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        return calendar.getTime();
    }

    /**
     * 获取一个季度前的日期
     *
     * @return 一个季度前的Date对象
     */
    public static Date getOneQuarterAgo() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -3);
        return calendar.getTime();
    }

    /**
     * 获取一年前的日期
     *
     * @return 一年前的Date对象
     */
    public static Date getOneYearAgo() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        return calendar.getTime();
    }

    private static final String yyyyMMddHHmmss = "yyyy-MM-dd HH:mm:ss";
    /**
     * 比较两个日期日期是否是同一天
     *
     * @param date      第一个日期
     * @param otherDate 另一个日期
     * @return boolean
     */
    public static boolean isSameDate(Date date, Date otherDate) {
        SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
        return fmt.format(date).equals(fmt.format(otherDate));
    }

    /**
     * 获取今天
     *
     * @return Date
     */
    public static Date getToday() {
        return new Date();
    }

    /**
     * 当天0点的时间戳
     * @return
     */
    public static Long getCurrentDateTimeStamp() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime().getTime();
    }

    public static Long getCurrentDateSecondStamp() {
        return getCurrentDateTimeStamp() / 1000;
    }

    public static long toSeconds(long timestamp) {
        return timestamp / 1000;
    }

    public static Long getYesterdayTimeStamp() {
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        // 将日期转换为时间戳（毫秒）
        return yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取昨天
     *
     * @return Date
     */
    public static Date getYesterday() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        return cal.getTime();
    }


    /**
     * 获取明天
     *
     * @return Date
     */
    public static Date getTomorrow() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 1);
        return cal.getTime();
    }


    /**
     * 获取后天
     *
     * @return Date
     */
    public static Date getDayAfterTomorrow() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        return cal.getTime();
    }


    /**
     * 获取月开始日期
     *
     * @return Date
     **/
    public static Date getMonthStart(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, 0);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    /**
     * 获取月最后一天
     *
     * @return Date
     **/
    public static Date getMonthEnd(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return cal.getTime();
    }

    /**
     * 获取本月开始日期
     *
     * @return Date
     **/
    public static Date getMonthStart() {
        return getMonthStart(getToday());
    }

    /**
     * 获取本月最后一天
     *
     * @return Date
     **/
    public static Date getMonthEnd() {
        return getMonthEnd(getToday());
    }

    /**
     * 获取下月开始日期
     *
     * @return Date
     **/
    public static Date getNextMonthStart() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, 1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    /**
     * 获取下月最后一天
     *
     * @return Date
     **/
    public static Date getNextMonthEnd() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, 1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return cal.getTime();
    }

    /**
     * 获取本月第几天
     *
     * @return Date
     **/
    public static Date getMonthOfDates(int ofDates) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getMonthStart());
        int maxDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        int realDates = maxDay < ofDates ? maxDay : ofDates;
        cal.add(Calendar.DAY_OF_MONTH, realDates - 1);
        return cal.getTime();
    }

    /**
     * 获取本周的第一天
     *
     * @return Date
     **/
    public static Date getWeekStart() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 0);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        return cal.getTime();
    }

    /**
     * 获取本周的最后一天
     *
     * @return Date
     **/
    public static Date getWeekEnd() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
        cal.add(Calendar.DAY_OF_WEEK, 1);
        return cal.getTime();
    }

    /**
     * 获取下周的第一天
     *
     * @return Date
     **/
    public static Date getNextWeekStart() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 1);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        return cal.getTime();
    }

    /**
     * 获取下周的最后一天
     *
     * @return Date
     **/
    public static Date getNextWeekEnd() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 1);
        cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
        cal.add(Calendar.DAY_OF_WEEK, 1);
        return cal.getTime();
    }

    /**
     * 获取本周的第几天
     *
     * @return Date
     **/
    public static Date getWeekOfDates(int ofDates) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getWeekStart());
        int maxDay = cal.getActualMaximum(Calendar.DAY_OF_WEEK);
        int realDates = maxDay < ofDates ? maxDay : ofDates;
        cal.add(Calendar.DAY_OF_WEEK, realDates - 1);
        return cal.getTime();
    }

    /**
     * 获取本年的第一天
     *
     * @return Date
     **/
    public static Date getYearStart() {
        try {
            return new SimpleDateFormat("yyyy-MM-dd").parse(new SimpleDateFormat("yyyy").format(new Date()) + "-01-01");
        } catch (ParseException e) {
            throw new IllegalArgumentException(e.getMessage(), e);
        }

    }

    /**
     * 获取本年的最后一天
     *
     * @return Date
     **/
    public static Date getYearEnd() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, cal.getActualMaximum(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return cal.getTime();
    }

    /**
     * 获取下年的第一天
     *
     * @return Date
     **/
    public static Date getNextYearStart() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getYearStart());
        cal.add(Calendar.YEAR, 1);
        return cal.getTime();
    }

    /**
     * 获取下年的最后一天
     *
     * @return Date
     **/
    public static Date getNextYearEnd() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getYearEnd());
        cal.add(Calendar.YEAR, 1);
        return cal.getTime();
    }

    /**
     * 获取本季度第一天
     *
     * @return
     */
    public static Date getQuarterStart() {
        Calendar cal = Calendar.getInstance();
        int currentMonth = cal.get(Calendar.MONTH) + 1;
        SimpleDateFormat longSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
        Date now = null;
        try {
            if (currentMonth >= 1 && currentMonth <= 3)
                cal.set(Calendar.MONTH, 0);
            else if (currentMonth >= 4 && currentMonth <= 6)
                cal.set(Calendar.MONTH, 3);
            else if (currentMonth >= 7 && currentMonth <= 9)
                cal.set(Calendar.MONTH, 6);
            else if (currentMonth >= 10 && currentMonth <= 12)
                cal.set(Calendar.MONTH, 9);
            cal.set(Calendar.DATE, 1);
            now = longSdf.parse(shortSdf.format(cal.getTime()) + " 00:00:00");
        } catch (Exception e) {

        }
        return now;
    }

    /**
     * 获取本季度最后一天
     *
     * @return
     */
    public static Date getQuarterEnd() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getQuarterStart());
        cal.add(Calendar.MONTH, 2);

        return getMonthEnd(cal.getTime());
    }

    /**
     * 获取下季度第一天
     *
     * @return
     */
    public static Date getNextQuarterStart() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getQuarterStart());
        cal.add(Calendar.MONTH, 3);
        return cal.getTime();
    }

    /**
     * 获取下季度最后一天
     *
     * @return
     */
    public static Date getNextQuarterEnd() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getQuarterStart());
        cal.add(Calendar.MONTH, 5);

        return getMonthEnd(cal.getTime());
    }

    /**
     * 获取本季度第几月第几天
     *
     * @return Date
     **/
    public static Date getQuarterOfDates(int month, int ofDates) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getQuarterStart());
        return addMonthAndDay(month, ofDates, cal);
    }

    /**
     * 获取年第几月第几天
     *
     * @return Date
     **/
    public static Date getYearOfDates(int month, int ofDates) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getYearStart());
        return addMonthAndDay(month, ofDates, cal);
    }

    /**
     * 获取动态日期区间
     * @param typeValue
     * @return
     */
    public static Date getDynamicDate(String typeValue) {
        Date date = null;
        switch (typeValue) {
            case "DAY_AFTER_TOMORROW":
                date = getDayAfterTomorrow();
                break;
            case "TODAY":
                date = getToday();
                break;
            case "TOMORROW":
                date = getTomorrow();
                break;
            case "NEXT_MONTH_END":
                date = getNextMonthEnd();
                break;
            case "NEXT_MONTH_START":
                date = getNextMonthStart();
                break;
            case "NEXT_QUARTER_END":
                date = getNextQuarterEnd();
                break;
            case "NEXT_QUARTER_START":
                date = getNextQuarterStart();
                break;
            case "NEXT_WEEK_END":
                date = getNextWeekEnd();
                break;
            case "NEXT_WEEK_START":
                date = getNextWeekStart();
                break;
            case "NEXT_YEAR_END":
                date = getNextYearEnd();
                break;
            case "NEXT_YEAR_START":
                date = getNextYearStart();
                break;
            case "THIS_MONTH_END":
                date = getMonthEnd();
                break;
            case "THIS_MONTH_START":
                date = getMonthStart();
                break;
            case "THIS_QUARTER_END":
                date = getQuarterEnd();
                break;
            case "THIS_QUARTER_START":
                date = getQuarterStart();
                break;
            case "THIS_WEEK_END":
                date = getWeekEnd();
                break;
            case "THIS_WEEK_START":
                date = getWeekStart();
                break;
            case "THIS_YEAR_END":
                date = getYearEnd();
                break;
            case "THIS_YEAR_START":
                date = getYearStart();
                break;
        }
        return date;
    }


    @NotNull
    private static Date addMonthAndDay(int month, int ofDates, Calendar cal) {
        cal.add(Calendar.MONTH, month - 1);
        int maxDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        int realDates = maxDay < ofDates ? maxDay : ofDates;
        cal.add(Calendar.DAY_OF_MONTH, realDates - 1);
        return cal.getTime();
    }

    public static String getCurrentTimeToStr(){
        Date d = new Date();
        SimpleDateFormat sbf = new SimpleDateFormat(yyyyMMddHHmmss);
        return sbf.format(d);
    }
    public static String getTimeByTimeStamp(long millisecond){
        Date d = new Date();
        long time = d.getTime()-(millisecond*1000);
        d.setTime(time);
        SimpleDateFormat sbf = new SimpleDateFormat(yyyyMMddHHmmss);
        return sbf.format(d);
    }
}
