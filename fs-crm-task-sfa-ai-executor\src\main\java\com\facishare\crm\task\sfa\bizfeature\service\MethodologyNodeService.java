package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyInstanceConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.NodeInstanceConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ObjectMethodologyConstants;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.common.constants.SystemConstants;
import com.facishare.crm.task.sfa.model.ObjectData;
import com.facishare.crm.task.sfa.services.SFALicenseService;
import com.facishare.crm.task.sfa.util.SFAConfigUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MethodologyNodeService {

    @Autowired
    private ServiceFacade serviceFacade;

    @Resource
    private SFALicenseService sfaLicenseService;

    /**
     * 启动方法论节点
     *
     * @param message 对象变更消息
     */
    public void startMethodologyNode(ObjectData.ObjectChange message) {
        try {
            String tenantId = message.getContext().getTenantId();
            String objectApiName = message.getEntityId();
            String objectId = message.getObjectId();

            log.info("开始处理方法论节点启动，tenantId: {}, objectApiName: {}, objectId: {}",
                    tenantId, objectApiName, objectId);

            User user = new User(tenantId, CommonConstant.SUPER_USER);
            String aiLicense = "ai_interactive_assistant_app";
            if (!sfaLicenseService.checkModuleLicenseExist(user.getTenantId(), aiLicense)) {
                log.info("No aiLicense found");
                return;
            }

            if(!SFAConfigUtil.isOpenCustomerProfileAgent(user.getTenantId())){
                return;
            }

            // 1. 根据objectApiName和Id查询数据
            IObjectData objectData = queryObjectData(user, objectApiName, objectId);
            if (ObjectUtils.isEmpty(objectData)) {
                log.warn("未找到对象数据，objectApiName: {}, objectId: {}", objectApiName, objectId);
                return;
            }

            // 2. 根据objectApiName和id查询方法论实例
            List<IObjectData> methodologyInstances = queryMethodologyInstances(user, objectApiName, objectId);
            if (CollectionUtil.isEmpty(methodologyInstances)) {
                log.warn("未找到方法论实例，objectApiName: {}, objectId: {}", objectApiName, objectId);
                return;
            }

            // 批量处理方法论实例
            batchProcessMethodologyInstances(user, objectData, methodologyInstances, objectApiName);

        } catch (Exception e) {
            log.error("处理方法论节点启动异常", e);
        }
    }

    /**
     * 查询对象数据
     */
    private IObjectData queryObjectData(User user, String objectApiName, String objectId) {
        try {
            return serviceFacade.findObjectData(user, objectId, objectApiName);
        } catch (Exception e) {
            log.error("查询对象数据异常，objectApiName: {}, objectId: {}", objectApiName, objectId, e);
            return null;
        }
    }

    /**
     * 查询方法论实例
     */
    private List<IObjectData> queryMethodologyInstances(User user, String objectApiName, String objectId) {
        try {
            SearchTemplateQueryPlus query = new SearchTemplateQueryPlus()
                    .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                    .addFilter(MethodologyInstanceConstants.STATUS, Operator.EQ,
                            MethodologyInstanceConstants.StatusType.ENABLE.getStatusType());

            // 根据不同的对象类型使用不同的字段进行查询
            if (FeatureConstants.LEADS_OBJ.equals(objectApiName)) {
                query.addFilter(MethodologyInstanceConstants.LEAD_ID, Operator.EQ, objectId);
            } else if (FeatureConstants.ACCOUNT_OBJ.equals(objectApiName)) {
                query.addFilter(MethodologyInstanceConstants.ACCOUNT_ID, Operator.EQ, objectId);
            } else if (FeatureConstants.NEW_OPPORTUNITY_OBJ.equals(objectApiName)) {
                query.addFilter(MethodologyInstanceConstants.OPPORTUNITY_ID, Operator.EQ, objectId);
            } else {
                log.warn("不支持的对象类型: {}", objectApiName);
                return Lists.newArrayList();
            }

            query.setLimit(100);
            query.setFindExplicitTotalNum(false);
            query.setNeedReturnCountNum(false);
            query.setPermissionType(0);

            QueryResult<IObjectData> result = serviceFacade.findBySearchQueryIgnoreAll(user,
                    MethodologyInstanceConstants.API_NAME, query);
            return result != null ? result.getData() : Lists.newArrayList();
        } catch (Exception e) {
            log.error("查询方法论实例异常，objectApiName: {}, objectId: {}", objectApiName, objectId, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 批量处理方法论实例
     */
    private void batchProcessMethodologyInstances(User user, IObjectData objectData,
                                                  List<IObjectData> methodologyInstances, String objectApiName) {
        try {
            // 批量获取方法论ID
            List<String> methodologyIds = methodologyInstances.stream()
                    .map(instance -> getStringValue(instance, MethodologyInstanceConstants.METHODOLOGY_ID))
                    .filter(ObjectUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(methodologyIds)) {
                log.warn("未找到有效的方法论ID");
                return;
            }

            // 批量查询对象流程字典
            List<IObjectData> allObjectWorkflows = batchQueryObjectWorkflows(user, objectApiName, methodologyIds);
            if (CollectionUtil.isEmpty(allObjectWorkflows)) {
                log.warn("未找到对象流程字典，objectApiName: {}, methodologyIds: {}", objectApiName, methodologyIds);
                return;
            }

            // 按方法论ID分组
            Map<String, List<IObjectData>> workflowsByMethodologyId = allObjectWorkflows.stream()
                    .collect(Collectors
                            .groupingBy(workflow -> getStringValue(workflow, ObjectMethodologyConstants.METHODOLOGY_ID)));

            // 批量获取匹配的节点ID和方法论实例ID的映射
            Map<String, String> instanceIdToNodeIdMap = new HashMap<>();

            for (IObjectData methodologyInstance : methodologyInstances) {
                String methodologyId = getStringValue(methodologyInstance, MethodologyInstanceConstants.METHODOLOGY_ID);
                if (ObjectUtils.isEmpty(methodologyId)) {
                    log.warn("方法论实例缺少methodology_id，实例ID: {}", methodologyInstance.getId());
                    continue;
                }

                List<IObjectData> objectWorkflows = workflowsByMethodologyId.get(methodologyId);
                if (CollectionUtil.isEmpty(objectWorkflows)) {
                    log.warn("未找到对象流程字典，methodologyId: {}", methodologyId);
                    continue;
                }

                String matchedNodeId = findMatchedNodeId(objectData, objectWorkflows);
                if (ObjectUtils.isNotEmpty(matchedNodeId)) {
                    instanceIdToNodeIdMap.put(methodologyInstance.getId(), matchedNodeId);
                }
            }

            if (instanceIdToNodeIdMap.isEmpty()) {
                log.warn("未找到任何匹配的节点");
                return;
            }

            // 批量更新节点实例状态
            batchUpdateNodeInstanceStatus(user, instanceIdToNodeIdMap);

        } catch (Exception e) {
            log.error("批量处理方法论实例异常", e);
        }
    }

    /**
     * 批量查询对象流程字典
     */
    public List<IObjectData> batchQueryObjectWorkflows(User user, String objectApiName, List<String> methodologyIds) {
        try {
            SearchTemplateQueryPlus query = new SearchTemplateQueryPlus()
                    .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                    .addFilter(ObjectMethodologyConstants.OBJECT_API_NAME, Operator.EQ, objectApiName)
                    .addFilter(ObjectMethodologyConstants.METHODOLOGY_ID, Operator.IN, methodologyIds);

            query.setLimit(2000);
            query.setFindExplicitTotalNum(false);
            query.setNeedReturnCountNum(false);
            query.setPermissionType(0);

            QueryResult<IObjectData> result = serviceFacade.findBySearchQueryIgnoreAll(user,
                    ObjectMethodologyConstants.API_NAME, query);
            return result != null ? result.getData() : Lists.newArrayList();
        } catch (Exception e) {
            log.error("批量查询对象流程字典异常，objectApiName: {}, methodologyIds: {}", objectApiName, methodologyIds, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 批量更新节点实例状态
     */
    private void batchUpdateNodeInstanceStatus(User user, Map<String, String> instanceIdToNodeIdMap) {
        try {
            List<String> methodologyInstanceIds = new ArrayList<>(instanceIdToNodeIdMap.keySet());
            List<String> nodeIds = new ArrayList<>(instanceIdToNodeIdMap.values()).stream().distinct()
                    .collect(Collectors.toList());

            // 批量查询节点实例
            SearchTemplateQueryPlus query = new SearchTemplateQueryPlus()
                    .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                    .addFilter(NodeInstanceConstants.METHODOLOGY_INSTANCE_ID, Operator.IN, methodologyInstanceIds)
                    .addFilter(NodeInstanceConstants.NODE_ID, Operator.IN, nodeIds);

            query.setLimit(1000);
            query.setFindExplicitTotalNum(false);
            query.setNeedReturnCountNum(false);
            query.setPermissionType(0);

            QueryResult<IObjectData> result = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user,
                    NodeInstanceConstants.API_NAME, query,
                    Lists.newArrayList(DBRecord.ID, SystemConstants.Field.TennantID.apiName, NodeInstanceConstants.METHODOLOGY_INSTANCE_ID,
                            NodeInstanceConstants.NODE_ID, NodeInstanceConstants.STATUS, IObjectData.DESCRIBE_API_NAME));
            if (result == null || CollectionUtil.isEmpty(result.getData())) {
                log.warn("未找到节点实例，methodologyInstanceIds: {}, nodeIds: {}", methodologyInstanceIds, nodeIds);
                return;
            }

            // 过滤需要更新的节点实例
            List<IObjectData> updateList = result.getData().stream()
                    .filter(nodeInstance -> {
                        String instanceId = getStringValue(nodeInstance, NodeInstanceConstants.METHODOLOGY_INSTANCE_ID);
                        String nodeId = getStringValue(nodeInstance, NodeInstanceConstants.NODE_ID);
                        String expectedNodeId = instanceIdToNodeIdMap.get(instanceId);

                        // 检查是否匹配且状态不是进行中
                        return ObjectUtils.isNotEmpty(expectedNodeId)
                                && expectedNodeId.equals(nodeId)
                                && !NodeInstanceConstants.StatusType.PROGRESS.getStatusType().equals(
                                getStringValue(nodeInstance, NodeInstanceConstants.STATUS));
                    })
                    .peek(nodeInstance -> {
                        nodeInstance.set(NodeInstanceConstants.STATUS,
                                NodeInstanceConstants.StatusType.PROGRESS.getStatusType());
                        log.info("更新节点实例状态为进行中，节点实例ID: {}, nodeId: {}",
                                nodeInstance.getId(), getStringValue(nodeInstance, NodeInstanceConstants.NODE_ID));
                    })
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(updateList)) {
                serviceFacade.batchUpdateByFields(user, updateList, Lists.newArrayList(NodeInstanceConstants.STATUS));
                log.info("成功批量更新{}个节点实例状态", updateList.size());
            } else {
                log.info("没有需要更新的节点实例");
            }

        } catch (Exception e) {
            log.error("批量更新节点实例状态异常", e);
        }
    }

    /**
     * 根据字段数据匹配节点ID
     */
    public String findMatchedNodeId(IObjectData objectData, List<IObjectData> objectWorkflows) {
        try {
            for (IObjectData workflow : objectWorkflows) {
                String objectFieldName = getStringValue(workflow, ObjectMethodologyConstants.OBJECT_FIELD_NAME);
                String objectFieldValue = getStringValue(workflow, ObjectMethodologyConstants.OBJECT_FIELD_VALUE);
                String nodeId = getStringValue(workflow, ObjectMethodologyConstants.NODE_ID);

                if (ObjectUtils.isEmpty(objectFieldName) || ObjectUtils.isEmpty(objectFieldValue)
                        || ObjectUtils.isEmpty(nodeId)) {
                    continue;
                }

                // 获取对象数据中对应字段的值
                Object fieldValue = objectData.get(objectFieldName);
                if (Objects.equal(objectFieldValue, fieldValue)) {
                    log.info("找到匹配的节点，字段: {}, 值: {}, 节点ID: {}", objectFieldName, objectFieldValue, nodeId);
                    return nodeId;
                }
            }
        } catch (Exception e) {
            log.error("匹配节点ID异常", e);
        }
        return null;
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(IObjectData data, String fieldName) {
        return data.get(fieldName, String.class);
    }
}