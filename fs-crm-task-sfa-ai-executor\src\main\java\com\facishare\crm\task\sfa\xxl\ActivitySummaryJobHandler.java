package com.facishare.crm.task.sfa.xxl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.task.sfa.activitysummary.service.ActivitySummaryService;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/6 17:08
 * @description:
 */
@Slf4j
@Component
@JobHander(value = "ActivitySummaryJobHandler")
public class ActivitySummaryJobHandler extends IJobHandler {

    @Autowired
    private ActivitySummaryService activitySummaryService;

    @Autowired
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;

    @Autowired
    private SpecialTableMapper specialTableMapper;

    private static List<String> tenantIds = Lists.newArrayList("1");
    private static List<String> test_account_names = Lists.newArrayList();
    private static Splitter CONFIG_SPLITTER = Splitter.on("|").omitEmptyStrings();

    private static Map<String, String> ACTIVITY_EI_ACCOUNT_NAMES = Maps.newHashMap();


    static {
        ConfigFactory.getConfig("fs-gray-sfa-follow", config -> {
            tenantIds = Lists.newArrayList(CONFIG_SPLITTER.split(config.get("activity_summary_tenant_id", "1")));
            test_account_names = Lists.newArrayList(CONFIG_SPLITTER.split(config.get("test_account_names")));
            if (config.get("activity_ei_account_names") != null){
                List<Map> activityEiAccountNames = JSONObject.parseArray(config.get("activity_ei_account_names"), Map.class);
                for (Map activityEiAccountName : activityEiAccountNames) {
                    ACTIVITY_EI_ACCOUNT_NAMES.put((String) activityEiAccountName.get("tenant_id"), (String) activityEiAccountName.get("account_names"));
                }
            }
        });
    }

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        log.info("ActivitySummaryJobHandler#execute {}", triggerParam);
        exexute();
        log.info("ActivitySummaryJobHandler#execute end {}", triggerParam);
        return ReturnT.SUCCESS;
    }

    public void exexute() {
        // 1 轮询企业
        for (String tenantId : tenantIds) {
            if (!ACTIVITY_EI_ACCOUNT_NAMES.isEmpty()) {
                if (ACTIVITY_EI_ACCOUNT_NAMES.containsKey(tenantId)){
                    String value = ACTIVITY_EI_ACCOUNT_NAMES.get(tenantId);
                    if ("ALL".equals(value)) {
                        getAllObjectIds(tenantId);
                    } else {
                        ArrayList<String> strings = Lists.newArrayList(CONFIG_SPLITTER.split(value));
                        if (CollectionUtils.isNotEmpty(strings)) {
                            String names = strings.stream().map(name -> String.format("'%s'", name)).collect(Collectors.joining(","));
                            getObjectIdsByName(tenantId, names);
                        }
                    }
                }else {
                    getAllObjectIds(tenantId);
                }
                continue;
            }

            if (CollectionUtils.isNotEmpty(test_account_names)) {
                String names = test_account_names.stream().map(name -> String.format("'%s'", name)).collect(Collectors.joining(","));
                getObjectIdsByName(tenantId,names);
            } else {
                getAllObjectIds(tenantId);
            }
        }
    }

    public void getAllObjectIds(String tenantId) {
        String sqlBegin = "select id from biz_account where tenant_id = '%s' and is_deleted = 0 order by id limit 500";
        String sqlOrder = "select id from biz_account where tenant_id = '%s' and is_deleted = 0 and id > '%s' order by id limit 500";
        String lastId = "";
        String sql = "";
        while (true) {
            if (StringUtils.isNotBlank(lastId)) {
                sql = String.format(sqlOrder, tenantId, lastId);
            } else {
                sql = String.format(sqlBegin, tenantId);
            }

            List<Map> dataList = specialTableMapper.setTenantId(tenantId).findBySql(sql);
            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }
            List<String> ids = dataList.stream().map(data -> (String) data.get("id")).collect(Collectors.toList());
            lastId = ids.get(ids.size() - 1);
            List<Map<String, String>> sessions = activitySummaryService.getSession(tenantId, ids);
            for (Map<String, String> session : sessions) {
                sfaTaskRateLimiterService.getActivitySummaryLimiter().acquire();
                for (Map.Entry<String, String> entry : session.entrySet()) {
                    try {
                        activitySummaryService.processAllSummary(tenantId, entry.getKey(), entry.getValue());
                    } catch (Exception e) {
                        log.warn("动态简报处理异常,tenantId:{},objectId:{},sessionId:{}", tenantId, entry.getKey(), entry.getValue(), e);
                    } finally {
                        SFALogContext.clearContext();
                    }
                }
            }
            if (dataList.size() < 500) {
                break;
            }
        }
    }

    public void getObjectIdsByName(String tenantId,String names) {
        // test_account_names
        String sql = String.format("select id from biz_account where tenant_id = '%s' and is_deleted = 0 and name = any(array[%s])", tenantId, names);
        List<Map> dataList = specialTableMapper.setTenantId(tenantId).findBySql(sql);
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("没有找到对应的企业,tenantId:{},names:{}", tenantId, names);
            return;
        }
        List<String> ids = dataList.stream().map(data -> (String) data.get("id")).collect(Collectors.toList());
        List<Map<String, String>> sessions = activitySummaryService.getSession(tenantId, ids);
        for (Map<String, String> session : sessions) {
            sfaTaskRateLimiterService.getActivitySummaryLimiter().acquire();
            for (Map.Entry<String, String> entry : session.entrySet()) {
                try {
                    activitySummaryService.processAllSummary(tenantId, entry.getKey(), entry.getValue());
                } catch (Exception e) {
                    log.warn("动态简报处理异常,tenantId:{},objectId:{},sessionId:{}", tenantId, entry.getKey(), entry.getValue(), e);
                } finally {
                    SFALogContext.clearContext();
                }
            }
        }
    }
}
