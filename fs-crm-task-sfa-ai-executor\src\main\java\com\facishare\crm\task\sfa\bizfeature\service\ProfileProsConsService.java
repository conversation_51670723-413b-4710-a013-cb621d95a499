package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.facishare.change.set.util.JsonUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.activitysummary.service.FixJSONFormatService;
import com.facishare.crm.task.sfa.bizfeature.constant.*;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureCrmNoteContext;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileAdviceModel;
import com.facishare.crm.task.sfa.bizfeature.service.dao.*;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.util.FeatureBaseDataUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ProfileProsConsService {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ProfileScoreService profileScoreService;
    @Resource
    FeatureDao featureDao;
    @Resource
    FeatureValueDao featureValueDao;
    @Resource
    TaskFeatureDao taskFeatureDao;
    @Resource
    FeatureWeightDao featureWeightDao;
    @Resource
    ProfileItemScoreDao profileItemScoreDao;
    @Resource
    ProfileProsConsDao profileProsConsDao;
    @Resource
    InstanceFeatureDao instanceFeatureDao;
    @Resource
    CompletionsService completionsService;
    @Resource
    NodeInstanceDao nodeInstanceDao;
    @Resource
    MethodologyInstanceDao methodologyInstanceDao;
    @Resource
    MethodologyNodeDao methodologyNodeDao;
    @Resource
    FixJSONFormatService fixJSONFormatService;

    private static final Map<String, String> NODE_TAG = new LinkedHashMap<String, String>() {
        {
            put("683063c99c6dd800060bf08f", "W");
            put("68306a219c6dd800060bf75a", "F");
            put("68306a3a9c6dd800060bfa4c", "C");
        }
    };
    private static final List<String> WIN_KEY_LIST = Arrays.asList(
            "1W1F7C", "1W1F8C", "1W1F9C",
            "1W2F6C", "1W2F7C", "1W2F8C", "1W2F9C",
            "1W3F6C", "1W3F7C", "1W3F8C", "1W3F9C", "1W3F6C", "1W3F7C", "1W3F8C", "1W3F9C");
    private static final List<String> SHAKE_KEY_LIST = Arrays.asList(
            "0W2F7C", "0W2F8C", "0W2F9C", "0W3F7C", "0W3F8C",
            "1W0F6C", "1W0F7C", "1W0F8C", "1W0F9C", "1W1F6C");
    private static final List<String> LOST_KEY_LIST = Arrays.asList(
            "0W0F0C", "0W0F1C", "0W0F2C", "0W0F3C", "0W0F4C", "0W0F5C", "0W0F6C", "0W0F7C", "0W0F8C",
            "0W1F0C", "0W1F1C", "0W1F2C", "0W1F3C", "0W1F4C", "0W1F5C", "0W1F6C", "0W1F7C", "0W1F8C",
            "0W2F0C", "0W2F1C", "0W2F2C", "0W2F3C", "0W2F4C", "0W2F5C", "0W2F6C",
            "0W3F0C", "0W3F1C", "0W3F2C", "0W3F3C", "0W3F4C", "0W3F5C", "0W3F6C",
            "1W0F0C", "1W0F1C", "1W0F2C", "1W0F3C", "1W0F4C", "1W0F5C",
            "1W1F0C", "1W1F1C", "1W1F2C", "1W1F3C", "1W1F4C", "1W1F5C");

    private static List<String> SUPPORT_OBJECT_API_NAME
            = Arrays.asList("LeadsObj", "AccountObj", "NewOpportunityObj", "NewOpportunityContactsObj"
            , "ActiveRecordObj", "CompetitiveLinesObj", "NewOpportunityContactRelationshipObj");

    /**
     * 生成画像优劣势分析
     *
     * @param param tenant_id+画像ID
     */
    public void generateProfileProsCons(ProfileAdviceModel param, FeatureCrmNoteContext featureCrmNoteContext) {
        StopWatch stopWatch = StopWatch.createStarted("generateProfileProsCons" + param.getProfileId());
        String profileId = param.getProfileId();
        // 1. 查询画像及所有分项得分
        User user = User.systemUser(param.getTenantId());
        IObjectData profile = param.getProfile();
        String methodologyId = profile.get(ProfileConstants.METHODOLOGY_ID, String.class);
        String methodologyInstanceId = profile.get(ProfileConstants.METHODOLOGY_INSTANCE_ID, String.class);
        String type = profile.get(ProfileConstants.TYPE, String.class);
        String objectId = param.getObjectId();
        String objectApiName = param.getObjectDescribeApiName();
        if (Strings.isNullOrEmpty(methodologyId) || Strings.isNullOrEmpty(methodologyInstanceId) || Strings.isNullOrEmpty(type) || Strings.isNullOrEmpty(objectId)) {
            log.error("methodologyId or methodologyInstanceId or type or objectId is null or empty.");
            featureCrmNoteContext.setSucess(false);
            return;
        }
        String accountId = StringUtil.EMPTY;
        if (Utils.NEW_OPPORTUNITY_API_NAME.equals(objectApiName)) {
            IObjectData opportunity = serviceFacade.findObjectData(user, objectId, Utils.NEW_OPPORTUNITY_API_NAME);
            if (opportunity != null) {
                accountId = opportunity.get("account_id", String.class);
            }
        }

        IObjectData methodology = serviceFacade.findObjectData(user, methodologyId, FeatureConstants.METHODOLOGY);

        if (methodology == null) {
            log.error("Methodology not found for ID: {}", methodologyId);
            featureCrmNoteContext.setSucess(false);
            return;
        }

        String methodologyType = methodology.get(MethodologyConstants.TYPE, String.class);
        List<IObjectData> methodologyInstanceList = new ArrayList<>();
        IObjectData methodologyInstance = serviceFacade.findObjectData(user, methodologyInstanceId, FeatureConstants.METHODOLOGY_INSTANCE);
        if (methodologyInstance == null) {
            log.error("Methodology instance not found for ID: {}", methodologyInstanceId);
            featureCrmNoteContext.setSucess(false);
            return;
        }
        methodologyInstanceList.add(methodologyInstance);

        String currentNodeName = StringUtil.EMPTY;
        if (methodology.get(MethodologyConstants.TYPE, String.class).equals(MethodologyConstants.Type.FLOW.getType())) {
            // 3.1 获取阶段实例
            IObjectData instance = nodeInstanceDao.queryLastMethodologyAndObjectIdWithOrder(user, methodology, methodologyInstanceId, objectId);
            if (ObjectUtils.isEmpty(instance)) {
                log.error("No node instance found");
                featureCrmNoteContext.setSucess(false);
                return;
            }
            IObjectData currentNode = serviceFacade.findObjectData(user, instance.get(NodeInstanceConstants.NODE_ID, String.class), FeatureConstants.METHODOLOGY_NODE);
            currentNodeName = currentNode.getName();
        }


        List<IObjectData> profileItemScoreList = profileItemScoreDao.fetchProfileItemScoresByProfileId(user, profileId);

        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(user.getTenantId(), SUPPORT_OBJECT_API_NAME);
        stopWatch.lap("prepareBeforeData");

        Map<String, Object> totalSummaryMap = new HashMap<>();
        List<Map<String, Object>> totalSummaryList = new ArrayList<>();
        List<Map<String, Object>> prosFeatureList = new ArrayList<>();
        List<Map<String, Object>> consFeatureList = new ArrayList<>();
        Map<String, IObjectData> dimensionMap = new HashMap<>();
        Map<String, List<IObjectData>> featuresWithDimensionMap = new HashMap<>();
        List<String> dimensionIds=profileItemScoreList.stream()
                .filter(itemScore -> itemScore.get(ProfileItemScoreConstants.TYPE, String.class).equals(ProfileItemScoreConstants.Type.DIMENSION.getValue()))
                .map(itemScore -> itemScore.get(ProfileItemScoreConstants.FEATURE_DIMENSION_ID, String.class)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dimensionIds)) {
            //维度
            List<IObjectData> dimensions = serviceFacade.findObjectDataByIds(user.getTenantId(), dimensionIds, FeatureConstants.FEATURE_DIMENSION);
            dimensionMap = dimensions.stream().collect(Collectors.toMap(IObjectData::getId, dimension -> dimension));
            // 获取所有特征
            List<IObjectData> featureDimensionList = featureDao.fetchFeaturesByDimensionIds(user, dimensionIds);
            featuresWithDimensionMap = featureDimensionList.stream().collect(Collectors.groupingBy(x -> x.get(FeatureConstants.FEATURE_DIMENSION_1, String.class)));
        }
        // 获取所有 实例 特征关系
        List<IObjectData> instanceFeatureList = instanceFeatureDao.fetchInstanceFeaturesByMethodologyInstanceId(user, methodologyInstanceId);
        if (CollectionUtils.isEmpty(instanceFeatureList)) {
            log.error("instanceFeatureList is null or empty.");
            featureCrmNoteContext.setSucess(false);
            return;
        }
        //判断是否关联了流程
        IObjectData relatedMethodologyInstance = null;
        List<IObjectData> relatedInstanceFeatureList = new ArrayList<>();

        List<IObjectData> nodeList = methodologyNodeDao.fetchNodeByStatus(user);
        if (methodology.get(MethodologyConstants.TYPE, String.class).equals(MethodologyConstants.Type.PROFILE.getType())) {
            relatedMethodologyInstance = getRelatedMethodology(user, methodology.getId(), objectId, objectApiName);
            if (relatedMethodologyInstance != null) {
                relatedInstanceFeatureList = instanceFeatureDao.fetchInstanceFeaturesByMethodologyInstanceId(user, relatedMethodologyInstance.getId());
            }
        }

        List<String> allFeatureIds = instanceFeatureList.stream().map(x -> x.get(InstanceFeatureConstants.FEATURE_ID, String.class)).collect(Collectors.toList());
        List<IObjectData> allFeatures = serviceFacade.findObjectDataByIds(user.getTenantId(), allFeatureIds, FeatureConstants.FEATURE);
        List<IObjectData> allFeatureValues = featureValueDao.fetchFeatureValuesByFeatureIds(user, allFeatureIds);
        List<IObjectData> allFeatureWeights = featureWeightDao.fetchFeaturesWeightsByFeatureIds(user, methodologyId, allFeatureIds);


        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        // 循环所有分项得分
        for (IObjectData profileItemScore : profileItemScoreList) {
            String itemType = profileItemScore.get(ProfileItemScoreConstants.TYPE, String.class);
            String featureTriggerValues = profileItemScore.get(ProfileItemScoreConstants.FEATURE_TRIGGER_VALUES, String.class);
            if (itemType.equals(ProfileItemScoreConstants.Type.NODE.getValue()) && methodologyType.equals(MethodologyConstants.Type.FLOW.getType())) {
                //  跳过流程+节点，不生成优劣势信息
                continue;
            }
                stopWatch.lap("loopStart");
                String nodeId = profileItemScore.get(ProfileItemScoreConstants.NODE_ID, String.class);//一级节点
                String featureDimensionId = profileItemScore.get(ProfileItemScoreConstants.FEATURE_DIMENSION_ID, String.class);
                String targetId = Strings.isNullOrEmpty(featureDimensionId) ? nodeId : featureDimensionId;
                String itemTypeMapName = "";

                // 根据维度ID过滤特征
                List<IObjectData> filteredInstanceFeatureList = new ArrayList<>();
                Map<String, List<String>> featureIdTaskIdsMap = new HashMap<>();
                if (itemType.equals(ProfileItemScoreConstants.Type.DIMENSION.getValue())) {
                    IObjectData featureDimension = dimensionMap.get(featureDimensionId);
                    itemTypeMapName= featureDimension.getName();
                    List<IObjectData> features = featuresWithDimensionMap.get(featureDimensionId);
                    List<String> featureIds = features.stream()
                            .map(DBRecord::getId)
                            .collect(Collectors.toList());
                    filteredInstanceFeatureList = instanceFeatureList.stream()
                            .filter(taskFeature -> featureIds.contains(taskFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class)))
                            .collect(Collectors.toList());
                    for (IObjectData filteredInstanceFeature : filteredInstanceFeatureList) {
                        if (!featureIdTaskIdsMap.containsKey(filteredInstanceFeature.get(TaskFeatureConstants.FEATURE_ID, String.class))) {
                            featureIdTaskIdsMap.put(filteredInstanceFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class), Lists.newArrayList(filteredInstanceFeature.get(InstanceFeatureConstants.TASK_ID, String.class)));
                        } else {
                            featureIdTaskIdsMap.get(filteredInstanceFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class)).add(filteredInstanceFeature.get(InstanceFeatureConstants.TASK_ID, String.class));
                        }
                    }
                } else {
                    Optional<IObjectData> node = nodeList.stream().filter(x -> nodeId.equals(x.getId())).findFirst();
                    if (node.isPresent()) {
                        itemTypeMapName= node.get().getName();
                    }
                    List<String> childNodeIds= nodeList.stream()
                            .filter(x -> nodeId.equals(x.get(MethodologyNodeConstants.PARENT_ID, String.class)))
                            .map(DBRecord::getId)
                            .collect(Collectors.toList());
                    filteredInstanceFeatureList = instanceFeatureList.stream()
                            .filter(x -> childNodeIds.contains(x.get(InstanceFeatureConstants.NODE_ID, String.class)))
                            .collect(Collectors.toList());
                    if (relatedMethodologyInstance != null) {
                        List<String> filterdFeatureIds = filteredInstanceFeatureList.stream()
                                .map(x -> x.get(InstanceFeatureConstants.FEATURE_ID, String.class))
                                .collect(Collectors.toList());
                        List<IObjectData> relatedItemInstanceFeatureList = relatedInstanceFeatureList.stream()
                                .filter(x -> filterdFeatureIds.contains(x.get(InstanceFeatureConstants.FEATURE_ID, String.class)))
                                .collect(Collectors.toList());
                        for (IObjectData filteredInstanceFeature : relatedItemInstanceFeatureList) {
                            if (!featureIdTaskIdsMap.containsKey(filteredInstanceFeature.get(TaskFeatureConstants.FEATURE_ID, String.class))) {
                                featureIdTaskIdsMap.put(filteredInstanceFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class), Lists.newArrayList(filteredInstanceFeature.get(InstanceFeatureConstants.TASK_ID, String.class)));
                            } else {
                                featureIdTaskIdsMap.get(filteredInstanceFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class)).add(filteredInstanceFeature.get(InstanceFeatureConstants.TASK_ID, String.class));
                            }
                        }
                    } else {
                        List<String> featureIds = filteredInstanceFeatureList.stream()
                                .map(x-> x.get(InstanceFeatureConstants.FEATURE_ID, String.class))
                                .collect(Collectors.toList());
                        List<IObjectData> taskFeatures = taskFeatureDao.fetchTaskFeaturesByFeatureIds(user, featureIds);
                        for (IObjectData taskFeature : taskFeatures) {
                            if (!featureIdTaskIdsMap.containsKey(taskFeature.get(TaskFeatureConstants.FEATURE_ID, String.class))) {
                                featureIdTaskIdsMap.put(taskFeature.get(TaskFeatureConstants.FEATURE_ID, String.class), Lists.newArrayList(taskFeature.get(TaskFeatureConstants.TASK_ID, String.class)));
                            } else {
                                featureIdTaskIdsMap.get(taskFeature.get(TaskFeatureConstants.FEATURE_ID, String.class)).add(taskFeature.get(TaskFeatureConstants.TASK_ID, String.class));
                            }
                        }
                    }
                }

                if (CollectionUtils.isEmpty(filteredInstanceFeatureList)) {
                    log.warn("profile_pros_info_targetId:{},filteredInstanceFeatureList is null", targetId);
                    continue;
                }
                log.info("profile_pros_info_targetId:{},filteredTaskFeatureIds:{}"
                        , targetId, filteredInstanceFeatureList.stream().map(x -> x.get(InstanceFeatureConstants.FEATURE_ID, String.class)).collect(Collectors.toList()));
                Map<String, List<IObjectData>> instanceFeatureMap = processInstanceFeatureWithMethodologyInstanceMap(filteredInstanceFeatureList);

                // 查询特征分数
                if (instanceFeatureMap.isEmpty()) {
                    continue;
                }
                stopWatch.lap("loopGetFilteredTaskFeatureList");
                // 获取特征分数
                // key：methodologyInstanceId, objectApiName, objectId, featureId
                // value：分数

                Map<String, BigDecimal> keyScoreMap = profileScoreService.fetchFeatureScores(user, methodologyInstanceList, instanceFeatureMap);
                log.info("profile_pros_info_targetId:{},keyScoreMap:{}", targetId, keyScoreMap);
                stopWatch.lap("loopGetKeyScoreMap");
                List<String> objectIds = new ArrayList<>();
                Map<String, BigDecimal> featureIdScoreMap = Maps.newHashMap();

                for (Map.Entry<String, BigDecimal> entry : keyScoreMap.entrySet()) {
                    String key = entry.getKey();
                    String[] keys = key.split("_");
                    if (!objectApiName.equals(Utils.NEW_OPPORTUNITY_API_NAME) && !objectApiName.equals(keys[1])) {
                        continue;
                    }
                    objectIds.add(keys[2]);
                    if (!featureIdScoreMap.containsKey(keys[3])) {
                        featureIdScoreMap.put(keys[3], entry.getValue());
                    }
                }
                for (Map.Entry<String, List<String>> entry : featureIdTaskIdsMap.entrySet()) {
                    if (!featureIdScoreMap.containsKey(entry.getKey())) {
                        IObjectData feature = allFeatures.stream().filter(x -> entry.getKey().equals(x.getId())).findFirst().get();
                        BigDecimal score = feature.get(FeatureConstants.DEFAULT_SCORE, BigDecimal.class);
                        if (Objects.nonNull(score)) {
                            featureIdScoreMap.put(entry.getKey(), score);
                        }
                    }
                }
                if (objectApiName.equals(Utils.NEW_OPPORTUNITY_API_NAME)) {
                    objectIds.add(accountId);
                }
                List<String> featureIds = new ArrayList<>(featureIdScoreMap.keySet());
                if (CollectionUtils.isEmpty(featureIds)) {
                    continue;
                }
                log.info("profile_pros_info_targetId:{},featureIds:{}", targetId, featureIds);
                List<IObjectData> featureList = allFeatures.stream().filter(feature -> featureIds.contains(feature.getId())).collect(Collectors.toList());
                List<IObjectData> featureValues = allFeatureValues.stream()
                        .filter(featureValue -> featureIds.contains(featureValue.get(FeatureValueConstants.FEATURE_ID, String.class)))
                        .filter(featureValue -> objectIds.contains(featureValue.get(FeatureValueConstants.OBJECT_ID, String.class)))
                        .collect(Collectors.toList());

                log.info("profile_pros_info_targetId:{},featureValuesIds:{}", targetId, featureValues.stream().map(DBRecord::getId).collect(Collectors.toList()));
                Map<String, IObjectData> featureIdMap = processFeatureMap(featureList);
                Map<String, BigDecimal> featureIdWeightMap = featureWeightDao.fetchFeaturesWeightsByMethodologyIdAndType(allFeatureWeights, itemType, nodeId);

            String finalItemTypeMapName = itemTypeMapName;
            parallelTask.submit(() -> {
                List<IObjectData> filterFeatureList = fillProsOrConsExtraDataWithOrder(featureIdMap, featureIdScoreMap, featureIdWeightMap);
                stopWatch.lap("loopGetProsOrConsPreData-"+finalItemTypeMapName);
                fillProsOrConsInfomation(user, profileId, filterFeatureList, featureValues, methodology
                        , finalItemTypeMapName, featureIdWeightMap, prosFeatureList, consFeatureList, describeMap);
                stopWatch.lap("loopGetProsOrConsInfomation-"+finalItemTypeMapName);
                saveProsCons(user, profileId, itemType, nodeId, featureDimensionId, filterFeatureList);
                stopWatch.lap("loopSaveProsCons-"+finalItemTypeMapName);
                // 处理得分项总结
                Map<String, Object> methodologySummeryMap = processDimensionSummary(user, profileId, featureIdMap, featureValues, featureIdWeightMap
                        , featureIdTaskIdsMap, finalItemTypeMapName, methodology.getName(), profileItemScore, featureIdScoreMap, featureTriggerValues, describeMap);
                totalSummaryList.add(methodologySummeryMap);
            });
            stopWatch.lap("loopEnd");
        }
        try {
            parallelTask.await(600, TimeUnit.SECONDS);
        }catch (Exception e){
            log.error("Timeout generateProfileProsCons for", e);
            return;
        }

        saveTotalProsConsSummary(user, profileId, prosFeatureList, consFeatureList);
        stopWatch.lap("saveTotalProsConsSummary");
        if (CollectionUtils.isEmpty(totalSummaryList)) {
            log.warn("totalSummaryList is empty, profileId:{}", profileId);
            return;
        }
        if (!Strings.isNullOrEmpty(currentNodeName)) {
            totalSummaryMap.put("当前阶段", currentNodeName);
        }
        totalSummaryMap.put(methodology.getName(), totalSummaryList);
        String areaTip = StringUtil.EMPTY;

        if (methodology.get(MethodologyConstants.TYPE, String.class).equals(MethodologyConstants.Type.PROFILE.getType())
                && objectApiName.equals(Utils.NEW_OPPORTUNITY_API_NAME)) {
            if (relatedMethodologyInstance != null) {
                List<IObjectData> nodeInstanceList = nodeInstanceDao.fetchNodeInstancesByMethodologyInstanceId(user, methodologyInstanceId);
                Map<String, List<IObjectData>> parentIdNodeInstanceList = nodeInstanceList.stream()
                        .filter(x -> !Strings.isNullOrEmpty(x.get(NodeInstanceConstants.PARENT_ID, String.class)))
                        .filter(x -> x.get(NodeInstanceConstants.LEVEL, String.class).equals("2"))
                        .filter(x -> !x.get(NodeInstanceConstants.STATUS, String.class).equals(NodeInstanceConstants.StatusType.COMPLETED.getStatusType()))
                        .collect(Collectors.groupingBy(x -> x.get(NodeInstanceConstants.PARENT_ID, String.class)));
                StringBuilder sb = new StringBuilder();

                for (Map.Entry<String, String> entry : NODE_TAG.entrySet()) {
                    for (Map.Entry<String, List<IObjectData>> entryNode : parentIdNodeInstanceList.entrySet()) {
                        if (entry.getKey().equals(entryNode.getKey())) {
                            int count = entryNode.getValue().size();
                            sb.append(count + NODE_TAG.get(entry.getKey()));
                        }
                    }
                }

                if (WIN_KEY_LIST.contains(sb.toString())) {
                    areaTip = String.format("%s-当前商机处于赢单区内。", sb);// ignoreI18n
                }
                if (LOST_KEY_LIST.contains(sb.toString())) {
                    areaTip = String.format("%s-当前商机处于输单区内。", sb);// ignoreI18n
                }
                if (SHAKE_KEY_LIST.contains(sb.toString())) {
                    areaTip = String.format("%s-当前商机处于抖动区内。", sb);// ignoreI18n
                }
            }
        }
        updateTotalSummary(user, profile, totalSummaryMap, methodology.getName(), objectApiName, areaTip);
        stopWatch.lap("updateTotalSummary");
        stopWatch.logSlow(500);
    }

    private void saveTotalProsConsSummary(User user,String profileId, List<Map<String, Object>> prosFeatureList,List<Map<String, Object>> consFeatureList) {
        List<IObjectData> saveDataList = new ArrayList<>();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        if (!CollectionUtils.isEmpty(prosFeatureList)) {
            parallelTask.submit(() -> {
                String prosSummary = getProsConsSummary(user, profileId, prosFeatureList);
                IObjectData prosData = BuildProsCons(user, profileId, null, ProfileProsConsConstants.RangeType.PROFILE.getValue(),ProfileProsConsConstants.Type.PROS.getValue()
                        , "", "", "", prosSummary);
                saveDataList.add(prosData);
            });
        }
        if (!CollectionUtils.isEmpty(consFeatureList)) {

            parallelTask.submit(() -> {
                String consSummary = getProsConsSummary(user, profileId, consFeatureList);
                IObjectData consData = BuildProsCons(user, profileId, null, ProfileProsConsConstants.RangeType.PROFILE.getValue(),ProfileProsConsConstants.Type.CONS.getValue()
                        , "", "", "", consSummary);
                saveDataList.add(consData);
            });
        }
        try {
            parallelTask.await(300, TimeUnit.SECONDS);
        }catch (Exception e){
            log.error("Timeout saveTotalProsConsSummary parallelTask", e);
            return;
        }
        List<IObjectData> existProsConsList = profileProsConsDao.fetchDatasByProfileIdWithProfileType(user, profileId);
        if (!CollectionUtils.isEmpty(existProsConsList)) {
            serviceFacade.bulkDeleteDirect(existProsConsList, user);
        }
        if (!CollectionUtils.isEmpty(saveDataList)) {
            serviceFacade.bulkSaveObjectData(saveDataList, user);
        }
    }

    private String getProsConsSummary(User user,String profileId, List<Map<String, Object>> featureList) {
        String promptApiName = PrompotConstants.PROMPT_PROS_CONS_SUMMARY;
        String promptRequestInfo = JsonUtil.toJsonString(featureList);
        Map<String, Object> sceneVariables = new HashMap<>();
        sceneVariables.put(PrompotConstants.PROMPOT_INFO, promptRequestInfo);

        AiRestProxyModel.Arg argSummary = AiRestProxyModel.Arg.builder()
                .apiName(promptApiName)
                .sceneVariables(sceneVariables)
                .build();
        String resultSummary = completionsService.requestCompletion(user, argSummary);
        log.info("profile_pros_prompt_info_{}:apiName:{},requestInfo:{},result:{}"
                , profileId, argSummary.getApiName(), argSummary.getSceneVariables(), resultSummary);
        return resultSummary;
    }

    private void updateTotalSummary(User user, IObjectData profile, Map<String, Object> totalSummaryMap, String methodyologyName, String objectApiName,String areaTip) {
        String promptRequestInfo = JsonUtil.toJsonString(totalSummaryMap);
        String promptApiName = StringUtil.EMPTY;
        if (objectApiName.equals(Utils.ACCOUNT_API_NAME)) {
            promptApiName = PrompotConstants.PROMPT_PROFILE_ACCOUNT_SUMMARY;
        }
        if (objectApiName.equals(Utils.LEADS_API_NAME)) {
            promptApiName = PrompotConstants.PROMPT_PROFILE_LEADS_SUMMARY;
        }
        if (objectApiName.equals(Utils.NEW_OPPORTUNITY_API_NAME)) {
            promptApiName = PrompotConstants.PROMPT_PROFILE_OPPORTUNITY_SUMMARY;
        }
        if (methodyologyName.equals(MethodologyConstants.C139)) {
            //C139用不同提示词模板
            promptApiName = PrompotConstants.PROMPT_C139_PROFILE_SUMMARY;
        }
        Map<String, Object> sceneVariables = new HashMap<>();
        sceneVariables.put(PrompotConstants.PROMPOT_INFO, promptRequestInfo);

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();

        String finalPromptApiName = promptApiName;

        List<String> updateFields = new ArrayList<>();
        parallelTask.submit(() -> {
            AiRestProxyModel.Arg argSummary = AiRestProxyModel.Arg.builder()
                    .apiName(finalPromptApiName)
                    .sceneVariables(sceneVariables)
                    .build();
            String resultSummary = completionsService.requestCompletion(user, argSummary);
            resultSummary = areaTip + '\n' + resultSummary;
            log.info("profile_pros_prompt_info_{}:apiName:{},requestInfo:{},result:{}"
                    , profile.getId(), argSummary.getApiName(), argSummary.getSceneVariables(), resultSummary);
            if (!Strings.isNullOrEmpty(resultSummary)) {
                profile.set(ProfileConstants.SUMMARY, resultSummary);
                updateFields.add(ProfileConstants.SUMMARY);
            }
        });
        parallelTask.submit(() -> {
            AiRestProxyModel.Arg argTrendSummary = AiRestProxyModel.Arg.builder()
                    .apiName(PrompotConstants.PROMPT_PROFILE_TREND_SUMMARY)
                    .sceneVariables(sceneVariables)
                    .build();
            String resultTrendSummary = completionsService.requestCompletion(user, argTrendSummary);
            log.info("profile_pros_prompt_info_{}:apiName:{},requestInfo:{},result:{}"
                    , profile.getId(), argTrendSummary.getApiName(), argTrendSummary.getSceneVariables(), resultTrendSummary);
            if (!Strings.isNullOrEmpty(resultTrendSummary)) {
                profile.set(ProfileConstants.TREND_SUMMARY, resultTrendSummary);
                updateFields.add(ProfileConstants.TREND_SUMMARY);
            }
        });

        try {
            parallelTask.await(300, TimeUnit.SECONDS);
        }catch (Exception e){
            log.error("Timeout updateTotalSummary parallelTask", e);
            return;
        }
//        List<String> updateFields = new ArrayList<>();
//        if (!Strings.isNullOrEmpty(resultSummary)) {
//            profile.set(ProfileConstants.SUMMARY, resultSummary);
//            updateFields.add(ProfileConstants.SUMMARY);
//        }
//        if (!Strings.isNullOrEmpty(resultTrendSummary)) {
//            profile.set(ProfileConstants.TREND_SUMMARY, resultTrendSummary);
//            updateFields.add(ProfileConstants.TREND_SUMMARY);
//        }
        if (CollectionUtils.isNotEmpty(updateFields)) {
            serviceFacade.batchUpdateByFields(user, Lists.newArrayList(profile), updateFields);
        }
    }

    /**
     * 处理得分项总结，构建特征详情信息
     *
     * @param user                    当前用户信息
     * @param featureIdMap            特征ID到特征对象的映射
     * @param featureValues           特征值列表
     * @param featureIdWeightMap      特征ID到权重的映射
     */
    private Map<String, Object> processDimensionSummary(User user, String profileId, Map<String, IObjectData> featureIdMap,
                                                        List<IObjectData> featureValues,
                                                        Map<String, BigDecimal> featureIdWeightMap,
                                                        Map<String,List<String>> featureIdTaskIdsMap,
                                                        String itemTypeMapName, String methodologyName,
                                                        IObjectData profileItemScore, Map<String, BigDecimal> featureIdScoreMap,
                                                        String featureTriggerValues, Map<String, IObjectDescribe> describeMap) {
        // 构建特征与任务的映射关系
        List<String> allTaskIds = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : featureIdTaskIdsMap.entrySet()) {
            allTaskIds.addAll(entry.getValue());
        }
        // 查询任务数据
        List<IObjectData> taskList = serviceFacade.findObjectDataByIds(user.getTenantId(), allTaskIds, FeatureConstants.METHODOLOGY_TASK);
        Map<String, IObjectData> taskMap = processTaskMap(taskList);

        // 将 featureValues 转换为 Map 提升查询效率
        Map<String, IObjectData> featureValueMap = featureValues.stream()
                .collect(Collectors.toMap(
                        f -> f.get(FeatureValueConstants.FEATURE_ID, String.class),
                        f -> f,
                        (existing, replacement) -> existing // 或者 (existing, replacement) -> replacement
                ));

        Map<String, Object> methodologySummeryMap = new HashMap<>();
        Map<String, Object> dimensionSummeryMap = new HashMap<>();
        Map<String, Object> featureSummaryMap = new HashMap<>();
        for (Map.Entry<String, IObjectData> entry : featureIdMap.entrySet()) {
            String featureId = entry.getKey();
            List<String> taskIds = featureIdTaskIdsMap.get(featureId);
            List<String> taskNotes = new ArrayList<>();
            if (!CollectionUtils.isEmpty(taskIds)) {
                taskNotes = taskIds.stream()
                        .map(taskId -> taskMap.get(taskId))
                        .filter(Objects::nonNull)
                        .map(task -> task.get(MethodologyTaskConstants.NOTE, String.class, ""))
                        .collect(Collectors.toList());
            }
            IObjectData feature = featureIdMap.get(featureId);
            if (feature == null) {
                // 记录 feature 不存在的日志
                continue;
            }

            // 使用 Map 直接查询替代 Stream 过滤
            IObjectData featureValue = featureValueMap.get(featureId);

            Map<String, Object> featureDetail = new HashMap<>();
            featureDetail.put("任务特征", feature.getName());
            featureDetail.put("任务描述", taskNotes.stream().collect(Collectors.joining(";")));
            featureDetail.put("特征权重", Objects.isNull(featureIdWeightMap.get(feature.getId())) ? 1 : featureIdWeightMap.get(feature.getId()));
            if (featureValue != null) {
                featureDetail.put("特征值", featureValueDao.getFeatureValue(featureValue));
                Object featureValueTriggerValue = featureValueDao.getFeatureTriggerValue(user.getTenantId(), featureValue, describeMap);
                if (!Objects.isNull(featureValueTriggerValue)) {
                    featureDetail.put("特征原值", featureValueTriggerValue);
                }
            }
            featureDetail.put("特征评分(10分制)", featureIdScoreMap.get(feature.getId()));
            featureSummaryMap.put(feature.getName(), featureDetail);
        }
        if (featureSummaryMap.isEmpty()) {
            log.warn("profile_pros_prompt_info_{}:featureSummaryMap is empty", profileId);
            return new HashMap<>();
        }
        if (!Strings.isNullOrEmpty(itemTypeMapName)) {
            dimensionSummeryMap.put(itemTypeMapName, featureSummaryMap);
            methodologySummeryMap.put(methodologyName, dimensionSummeryMap);
        } else {
            methodologySummeryMap.put(methodologyName, featureSummaryMap);
        }

        String promptRequestInfo = JsonUtil.toJsonString(methodologySummeryMap);

        Map<String, Object> sceneVariables = new HashMap<>();
        sceneVariables.put(PrompotConstants.PROMPOT_INFO, promptRequestInfo);
        AiRestProxyModel.Arg argSummary = AiRestProxyModel.Arg.builder()
                .apiName(PrompotConstants.PROMPT_PROFILE_DIMENSION_SUMMARY)
                .sceneVariables(sceneVariables)
                .build();
        String resultSummary = completionsService.requestCompletion(user, argSummary);
        log.info("profile_pros_prompt_info_{}:apiName:{},requestInfo:{},result:{}"
                , profileId, argSummary.getApiName(), argSummary.getSceneVariables(), resultSummary);

        AiRestProxyModel.Arg argTrendSummary = AiRestProxyModel.Arg.builder()
                .apiName(PrompotConstants.PROMPT_PROFILE_TREND_SUMMARY)
                .sceneVariables(sceneVariables)
                .build();
        String resultTrendSummary = completionsService.requestCompletion(user, argTrendSummary);
        log.info("profile_pros_prompt_info_{}:apiName:{},requestInfo:{},result:{}"
                , profileId, argTrendSummary.getApiName(), argTrendSummary.getSceneVariables(), resultTrendSummary);

        List<String> updateFields = new ArrayList<>();
        if (!Strings.isNullOrEmpty(resultSummary)) {
            profileItemScore.set(ProfileItemScoreConstants.SUMMARY, resultSummary);
            updateFields.add(ProfileItemScoreConstants.SUMMARY);
        }
        if (!Strings.isNullOrEmpty(resultTrendSummary)) {
            profileItemScore.set(ProfileItemScoreConstants.TREND_SUMMARY, resultTrendSummary);
            updateFields.add(ProfileItemScoreConstants.TREND_SUMMARY);
        }
        if (!Strings.isNullOrEmpty(resultSummary) || !Strings.isNullOrEmpty(resultTrendSummary)) {
            serviceFacade.batchUpdateByFields(user, Lists.newArrayList(profileItemScore), updateFields);
        }
        return (Map<String, Object>) methodologySummeryMap.get(methodologyName);
    }

    private void fillProsOrConsInfomation(User user,String profileId, List<IObjectData> featureList, List<IObjectData> featureValues
            , IObjectData methodology, String itemTypeMapName, Map<String, BigDecimal> featureIdWeightMap
            ,List<Map<String, Object>> prosFeatureList,List<Map<String, Object>> consFeatureList,Map<String, IObjectDescribe> describeMap) {
        // 遍历featureValues，构建特征项
        Map<String, Map<String, Object>> featureIdRequestMap = new HashMap<>();
        for (IObjectData feature : featureList) {
            if (Strings.isNullOrEmpty(feature.get(ProfileProsConsConstants.VIRTUAL_TYPE, String.class))) {
                continue;
            }
            IObjectData featureValue = featureValues.stream()
                    .filter(f -> f.get(FeatureValueConstants.FEATURE_ID, String.class).equals(feature.getId()))
                    .distinct()
                    .findFirst().orElse(null);
// 构建特征项
            Map<String, Object> featureDetail = new HashMap<>();
            featureDetail.put("方法论", methodology.getName());
            if (!Strings.isNullOrEmpty(itemTypeMapName)) {
                featureDetail.put("维度", itemTypeMapName);
            }
            featureDetail.put("任务特征", feature.getName());
            if (featureValue != null) {
                featureDetail.put("特征值", featureValueDao.getFeatureValue(featureValue));
                Object featureValueTriggerValue = featureValueDao.getFeatureTriggerValue(user.getTenantId(), featureValue, describeMap);
                if (!Objects.isNull(featureValueTriggerValue)) {
                    featureDetail.put("特征原值", featureValueTriggerValue);
                }
            }
            featureDetail.put("特征权重", Objects.isNull(featureIdWeightMap.get(feature.getId())) ? 1 : featureIdWeightMap.get(feature.getId()));
            featureDetail.put("优劣势类型", feature.get(ProfileProsConsConstants.VIRTUAL_TYPE, String.class).equals("pros") ? "优势" : "劣势");
            featureDetail.put("特征评分(10分制)", feature.get(ProfileProsConsConstants.VIRTUAL_SCORE, BigDecimal.class));
            if (!featureIdRequestMap.containsKey(feature.getId())) {
                featureIdRequestMap.put(feature.getId(), featureDetail);
            }
            if (feature.get(ProfileProsConsConstants.VIRTUAL_TYPE, String.class).equals("pros")) {
                prosFeatureList.add(featureDetail);
            } else {
                consFeatureList.add(featureDetail);
            }
        }
        if (featureIdRequestMap.isEmpty()) {
            return;
        }
        String promptApiName = PrompotConstants.PROMPT_PROS_CONS;
        String promptRequestInfo = JsonUtil.toJsonString(featureIdRequestMap);
        Map<String, Object> sceneVariables = new HashMap<>();
        sceneVariables.put(PrompotConstants.PROMPOT_INFO, promptRequestInfo);
        AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
                .apiName(promptApiName)
                .sceneVariables(sceneVariables)
                .build();
        String result = completionsService.requestCompletion(user, arg);
        log.info("profile_pros_prompt_info_{}:apiName:{},requestInfo:{},result:{}"
                , profileId, arg.getApiName(), arg.getSceneVariables(), result);
        JSONObject targetNodeAdviceJson;
        try {
            String  fixJSON = extractJson(result);
            targetNodeAdviceJson = JsonUtil.parseObject(fixJSON);
        } catch (Exception e) {
            log.error("profile_pros_prompt_info_{}:Failed to parse JSON result: {}", profileId, result, e);
            targetNodeAdviceJson = new JSONObject();
        }
        for (IObjectData feature : featureList) {
            String prosConsAdvice = targetNodeAdviceJson.getString(feature.getId());
            if (prosConsAdvice != null && prosConsAdvice.contains("proscons")) {
                JSONObject prosConsObj = JsonUtil.parseObject(prosConsAdvice);
                prosConsAdvice = prosConsObj.getString("proscons");
            }
            feature.set(ProfileProsConsConstants.VIRTUAL_INFORMATION, prosConsAdvice);
        }
    }
    private String extractJson(String json) {
        if (json.contains("{") && json.contains("}")) {
            int start = json.indexOf("{");
            int end = json.lastIndexOf("}") + 1;
            return json.substring(start, end);
        }
        return json;
    }

    private void saveProsCons(User user, String profileId, String itemType, String nodeId, String featureDimensionId, List<IObjectData> featureList) {
        List<IObjectData> prosConsList = new ArrayList<>();
        for (IObjectData feature : featureList) {
            if (Strings.isNullOrEmpty(feature.get(ProfileProsConsConstants.VIRTUAL_INFORMATION, String.class))) {
                continue;
            }
            IObjectData prosCons = BuildProsCons(user, profileId, feature, ProfileProsConsConstants.RangeType.DIMENSION.getValue(), null
                    , itemType, nodeId, featureDimensionId, null);
            prosConsList.add(prosCons);
        }
        List<IObjectData> existProsConsList = profileProsConsDao.fetchProfileProsConsByProfileIdAndDimensionId(user, profileId,
                itemType.equals(ProfileItemScoreConstants.Type.DIMENSION.getValue()) ? featureDimensionId : nodeId);
        if (!CollectionUtils.isEmpty(existProsConsList)) {
            serviceFacade.bulkDeleteDirect(existProsConsList, user);
        }
        serviceFacade.bulkSaveObjectData(prosConsList, user);
    }

    private IObjectData BuildProsCons(User user, String profileId, IObjectData feature,String rangeType,String type, String itemType, String nodeId, String featureDimensionId,String infomation) {
        IObjectData prosCons = new ObjectData();
        prosCons.setId(IdGenerator.get());
        prosCons.setName(FeatureBaseDataUtils.generateName());
        prosCons.setTenantId(user.getTenantId());
        prosCons.setDescribeApiName(FeatureConstants.PROFILE_PROS_CONS);
        prosCons.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        prosCons.setOwner(Lists.newArrayList(user.getUserId()));

        prosCons.set(ProfileProsConsConstants.PROFILE_ID, profileId);
        prosCons.set(ProfileProsConsConstants.RANGE_TYPE, rangeType);
        if (!Strings.isNullOrEmpty(itemType)) {
            prosCons.set(ProfileProsConsConstants.FEATURE_DIMENSION_ID
                    , itemType.equals(ProfileItemScoreConstants.Type.DIMENSION.getValue()) ? featureDimensionId : nodeId);
        }

        prosCons.set(ProfileProsConsConstants.IS_ACCEPT, false);
        prosCons.set(ProfileProsConsConstants.IS_IGNORE, false);
        if (Objects.nonNull(feature)) {
            prosCons.set(ProfileProsConsConstants.FEATURE_IDS, Lists.newArrayList(feature.getId()));
            prosCons.set(ProfileProsConsConstants.SEQ, feature.get(ProfileProsConsConstants.VIRTUAL_SEQ, Integer.class));
            prosCons.set(ProfileProsConsConstants.TYPE, feature.get(ProfileProsConsConstants.VIRTUAL_TYPE, String.class));
            prosCons.set(ProfileProsConsConstants.INFORMATION, feature.get(ProfileProsConsConstants.VIRTUAL_INFORMATION, String.class));
        }
        if (!Strings.isNullOrEmpty(type)) {
            prosCons.set(ProfileProsConsConstants.TYPE, type);
        }
        if (!Strings.isNullOrEmpty(infomation)) {
            prosCons.set(ProfileProsConsConstants.INFORMATION, infomation);
        }

        return prosCons;
    }

    public List<IObjectData> fillProsOrConsExtraDataWithOrder(Map<String, IObjectData> featureIdMap, Map<String, BigDecimal> featureIdScoreMap, Map<String, BigDecimal> featureIdWeightMap) {
        List<IObjectData> prosConsList = Lists.newArrayList();
        // 分离优势和劣势特征（以6分为界）
        Map<String, BigDecimal> pros = featureIdScoreMap.entrySet().stream()
                .filter(entry -> Objects.nonNull(entry.getValue()))
                .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(6)) >= 0)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        Map<String, BigDecimal> cons = featureIdScoreMap.entrySet().stream()
                .filter(entry -> Objects.nonNull(entry.getValue()))
                .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(6)) < 0)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 对优势特征按 isSwichOn + 权重排序
        List<String> sortedPros = pros.entrySet().stream()
                .sorted((e1, e2) -> {
                    IObjectData f1 = featureIdMap.get(e1.getKey());
                    IObjectData f2 = featureIdMap.get(e2.getKey());

                    // 获取 isSwichOn 值（默认 false）
                    boolean sw1 = Optional.ofNullable(f1.get(FeatureConstants.SWITCH_FEATURE, Boolean.class)).orElse(false);
                    boolean sw2 = Optional.ofNullable(f2.get(FeatureConstants.SWITCH_FEATURE, Boolean.class)).orElse(false);

                    // 1. 优先按 isSwichOn 降序（true 在前）
                    if (sw1 != sw2) {
                        return sw1 ? -1 : 1;
                    }
                    // 2. 再按权重降序
                    BigDecimal w1 = featureIdWeightMap.getOrDefault(e1.getKey(), BigDecimal.ZERO);
                    BigDecimal w2 = featureIdWeightMap.getOrDefault(e2.getKey(), BigDecimal.ZERO);
                    return w2.compareTo(w1);
                })
                .limit(3)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // 对劣势特征按 isSwichOn + 权重排序（同理）
        List<String> sortedCons = cons.entrySet().stream()
                .sorted((e1, e2) -> {
                    IObjectData f1 = featureIdMap.get(e1.getKey());
                    IObjectData f2 = featureIdMap.get(e2.getKey());

                    boolean sw1 = Optional.ofNullable(f1.get(FeatureConstants.SWITCH_FEATURE, Boolean.class)).orElse(false);
                    boolean sw2 = Optional.ofNullable(f2.get(FeatureConstants.SWITCH_FEATURE, Boolean.class)).orElse(false);

                    if (sw1 != sw2) {
                        return sw1 ? -1 : 1;
                    }
                    BigDecimal w1 = featureIdWeightMap.getOrDefault(e1.getKey(), BigDecimal.ZERO);
                    BigDecimal w2 = featureIdWeightMap.getOrDefault(e2.getKey(), BigDecimal.ZERO);
                    return w2.compareTo(w1);
                })
                .limit(3)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // 合并结果
        int seq = 1;
        for (String prosFeature : sortedPros) {
            IObjectData feature = featureIdMap.get(prosFeature);
            BigDecimal score = featureIdScoreMap.get(prosFeature);
            BigDecimal weight = featureIdWeightMap.get(prosFeature);
            feature.set(ProfileProsConsConstants.VIRTUAL_SEQ, seq);
            feature.set(ProfileProsConsConstants.VIRTUAL_TYPE, "pros");
            feature.set(ProfileProsConsConstants.VIRTUAL_SCORE, score);
            feature.set(ProfileProsConsConstants.VIRTUAL_WEIGHT, weight);
            prosConsList.add(feature);
            seq++;
        }
        seq = 1;
        for (String consFeature : sortedCons) {
            IObjectData feature = featureIdMap.get(consFeature);
            BigDecimal score = featureIdScoreMap.get(consFeature);
            BigDecimal weight = featureIdWeightMap.get(consFeature);
            feature.set(ProfileProsConsConstants.VIRTUAL_SEQ, seq);
            feature.set(ProfileProsConsConstants.VIRTUAL_TYPE, "cons");
            feature.set(ProfileProsConsConstants.VIRTUAL_SCORE, score);
            feature.set(ProfileProsConsConstants.VIRTUAL_WEIGHT, weight);
            seq++;
            prosConsList.add(feature);
        }
        return prosConsList;
    }

    /**
     * 处理任务特征数据，按方法论实例ID分组
     */
    private Map<String, List<IObjectData>> processInstanceFeatureWithMethodologyInstanceMap(List<IObjectData> instanceFeatureMap) {
        return instanceFeatureMap.stream()
                .collect(Collectors.groupingBy(taskFeature -> taskFeature.get(InstanceFeatureConstants.METHODOLOGY_INSTANCE_ID, String.class)));
    }

    private Map<String, IObjectData> processTaskMap(List<IObjectData> taskList) {
        return taskList.stream()
                .collect(Collectors.toMap(
                        task -> task.get(DBRecord.ID, String.class),
                        task -> task));
    }

    private Map<String, String> processFeatureAndTaskMap(List<IObjectData> filteredInstanceFeatureList) {
        Map<String, String> featureTaskMap = new HashMap<>();
        for (IObjectData instanceFeature : filteredInstanceFeatureList) {
            String featureId = instanceFeature.get(TaskFeatureConstants.FEATURE_ID, String.class);
            String taskId = instanceFeature.get(TaskFeatureConstants.TASK_ID, String.class);
            if (featureId != null && taskId != null) {
                featureTaskMap.put(featureId, taskId);
            }
        }

        return featureTaskMap;
    }

    /**
     * 处理任务特征数据，按方法论ID分组
     */
    private Map<String, IObjectData> processFeatureMap(List<IObjectData> featureList) {
        return featureList.stream()
                .collect(Collectors.toMap(
                        feature -> feature.get(DBRecord.ID, String.class),
                        feature -> feature));
    }

    private IObjectData getRelatedMethodology(User user, String methodologyId, String objectId,String objectApiName) {
        List<IObjectData> methodologyInstances = methodologyInstanceDao.fetchInstanceByMethodologyAndObject(user, Lists.newArrayList(methodologyId), objectId,objectApiName);
        if (CollectionUtils.isEmpty(methodologyInstances)) {
            return null;
        }
        IObjectData methodologyInstance = methodologyInstances.get(0);
        List<String> relatedMethodologyIds = methodologyInstance.get(MethodologyInstanceConstants.METHODOLOGY_IDS, List.class);
        if (CollectionUtils.isEmpty(relatedMethodologyIds)) {
            return null;
        }
        List<IObjectData> relatedMethodologyInstances = methodologyInstanceDao.fetchInstanceByMethodologyAndObject(user, relatedMethodologyIds, objectId, objectApiName);
        if (CollectionUtils.isEmpty(relatedMethodologyInstances)) {
            return null;
        }
        List<IObjectData> filteredRelatedMethodologyInstances = relatedMethodologyInstances.stream()
                .filter(x -> x.get(MethodologyInstanceConstants.STATUS, String.class).equals(MethodologyInstanceConstants.StatusType.ENABLE.getStatusType()))
                .filter(x -> x.get(MethodologyInstanceConstants.TYPE, String.class).equals(MethodologyInstanceConstants.Type.FLOW.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredRelatedMethodologyInstances)) {
            return null;
        }
        return filteredRelatedMethodologyInstances.get(0);
    }

    public List<IObjectData> filterFeaturesByType(User user, List<IObjectData> instanceFeatureList, String itemType
            , String nodeId, String featureDimensionId, Map<String, List<IObjectData>> featuresWithDimensionMap) {
        if (itemType.equals(ProfileItemScoreConstants.Type.DIMENSION.getValue())) {
            List<IObjectData> features = featuresWithDimensionMap.get(featureDimensionId);
            List<String> featureIds = features.stream()
                    .map(DBRecord::getId)
                    .collect(Collectors.toList());
            return instanceFeatureList.stream()
                    .filter(taskFeature -> featureIds.contains(taskFeature.get(InstanceFeatureConstants.FEATURE_ID, String.class)))
                    .collect(Collectors.toList());
        }
        /*if (itemType.equals(ProfileItemScoreConstants.Type.NODE.getValue())) {
            List<IObjectData> taskInstances = taskInstanceDao.fetchTaskInstancesByNodeInstance(user, nodeInstance);
            List<String> taskIds = taskInstances.stream()
                    .map(x -> x.get(TaskInstanceConstants.TASK_ID, String.class))
                    .collect(Collectors.toList());
            return instanceFeatureList.stream()
                    .filter(taskFeature -> taskIds.contains(taskFeature.get(InstanceFeatureConstants.TASK_ID, String.class)))
                    .collect(Collectors.toList());
        }*/
        return instanceFeatureList;
    }
}
