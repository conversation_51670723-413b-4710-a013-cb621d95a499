<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">


    <context:component-scan base-package="com.facishare.crm.task.sfa.xxl"/>
    <!-- 配置02、执行器 -->
    <bean id="myXxlJobExecutor" class="com.facishare.crm.task.sfa.xxl.MyXxlJobExecutor" init-method="start"
          destroy-method="destroy">
        <property name="configName" value="fs-crm-task-sfa-xxl-job"/>
    </bean>


    <bean id="SFAJedisCmd" class="com.github.jedis.support.JedisFactoryBean" p:configName="fs-crm-sfa-redis"/>

    <bean id="configPackageService" class="com.facishare.change.set.service.impl.ConfigPackageServiceImpl"/>

</beans>