package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.producer.ActivityRocketProducer;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTaskMessage;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityMeetingSummaryServiceImpl;
import com.facishare.crm.task.sfa.activitysummary.service.Rec2TextService;
import com.facishare.crm.task.sfa.activitysummary.service.RecordingTaskService;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.AttendeesInsightService;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/9 20:36
 * @description: 日晷的 activity 消息，这个是定时任务，消息会延迟发出（取决于日晷配置）。
 */
@Slf4j
@Component
public class ActivityTaskListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Resource
    private Rec2TextService rec2TextService;

    @Resource
    private ActivityRocketProducer activityRocketProducer;

    @Resource
    private RecordingTaskService recordingTaskService;


    @Resource
    private ActivityMeetingSummaryServiceImpl activityMeetingSummaryService;
    @Resource
    private AttendeesInsightService attendeesInsightService;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-ai-activity-nomon", (MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                try {
                    consumeResponse(msg);
                } catch (Exception e) {
                    log.error("活动任务消息消费失败:{}", msg, e);
                    throw new RuntimeException(e);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    private void consumeResponse(MessageExt message) {
        byte[] body = message.getBody();
        ActivityTaskMessage.Rec2TextTask rec2TextTask = JSON.parseObject(body, ActivityTaskMessage.Rec2TextTask.class);
        try {
            IObjectData byId = rec2TextService.findById(ActivityMessage.builder().tenantId(rec2TextTask.getTenantId()).objectId(rec2TextTask.getObjectId()).build());
            if (byId == null) {
                log.warn("object not found, msgId:{},{}", message.getMsgId(), rec2TextTask);
                return;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("ActivityTaskListener rec2TextTask:{}", rec2TextTask);
        try {
            // 轮询通义实时录音任务
            if ("recording".equals(rec2TextTask.getStage())){
                recordingTaskService.processRecordingTask(rec2TextTask);
                return;
            }else if ("changeSpeaker".equals(rec2TextTask.getStage())){
                ActivityMessage activityMessage = ActivityMessage.builder()
                    .tenantId(rec2TextTask.getTenantId())
                    .objectId(rec2TextTask.getObjectId())
                    .objectApiName(rec2TextTask.getObjectApiName())
                    .interactiveTypes(rec2TextTask.getInteractiveTypes())
                    .opId(rec2TextTask.getOpId())
                    .language(rec2TextTask.getLanguage())
                    .build();
                attendeesInsightService.sendInsightMessage(activityMessage.getTenantId(), activityMessage.getObjectId(), AttendeesInsightModel.FEATURE_INSIGHT_TYPE_LIST);
                activityMeetingSummaryService.executeByApiName(activityMessage, "prompt_sfa_speaker_summary");
                return;
            }
            // 轮询上传的语音文件识别结果
            rec2TextService.pollingAsrRecTaskResult(rec2TextTask);
        } catch (Exception e) {
            log.error("处理活动任务消息失败 rec2TextTask:{}", rec2TextTask, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
}
