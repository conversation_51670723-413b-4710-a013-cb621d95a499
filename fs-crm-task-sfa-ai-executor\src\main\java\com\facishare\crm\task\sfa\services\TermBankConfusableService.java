package com.facishare.crm.task.sfa.services;

import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class TermBankConfusableService {
    public static final String OBJECT_API_NAME = "TermBankConfusableObj";
    public static final String CONFUSABLE_WORD = "confusable_word";
    public static final String SEGMENTATION_LEVEL = "segmentation_level";
    private final ServiceFacade serviceFacade;

    public TermBankConfusableService(ServiceFacade serviceFacade) {
        this.serviceFacade = serviceFacade;
    }

    public void save(String tenantId, List<Entry> entryList) {
        List<IObjectData> list = entryList.stream().map(e -> e.toObjectData(tenantId)).collect(Collectors.toList());
        serviceFacade.bulkSaveObjectData(list, User.systemUser(tenantId));
    }

    public void deleteByIds(String tenantId, List<String> ids) {
        List<IObjectData> exist = serviceFacade.findObjectDataByIds(tenantId, ids, OBJECT_API_NAME);
        if (!exist.isEmpty()) {
            serviceFacade.bulkDeleteDirect(exist, User.systemUser(tenantId));
        }
    }

    @Data
    public static class Entry {
        String k;
        String v;
        String l;

        public IObjectData toObjectData(String tenantId) {
            IObjectData data = new ObjectData();
            data.setDescribeApiName(OBJECT_API_NAME);
            data.setTenantId(tenantId);
            data.set(CONFUSABLE_WORD, k);
            data.setName(v);
            data.set(SEGMENTATION_LEVEL, l);
            return data;
        }
    }
}
