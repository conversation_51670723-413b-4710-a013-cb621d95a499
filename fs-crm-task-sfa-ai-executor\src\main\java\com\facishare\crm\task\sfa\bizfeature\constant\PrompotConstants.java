package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 特征分数常量
 *
 * <AUTHOR>
 */
public interface PrompotConstants {
	/**
     * 优劣势润色提示词模板
     */
	String PROMPT_PROS_CONS = "prompt_pros_cons";
	/**
	 * 优劣势总结提示词模板
	 */
	String PROMPT_PROS_CONS_SUMMARY = "prompt_pros_cons_summary";
	/**
     * 画像总结提示词模板(线索)
     */
	String PROMPT_PROFILE_LEADS_SUMMARY = "prompt_profile_leads_summary";
	/**
	 * 画像总结提示词模板(客户)
	 */
	String PROMPT_PROFILE_ACCOUNT_SUMMARY = "prompt_profile_account_summary";
	/**
	 * 画像总结提示词模板(商机)
	 */
	String PROMPT_PROFILE_OPPORTUNITY_SUMMARY = "prompt_profile_opportunity_summary";
	/**
	 * 画像维度总结提示词模板
	 */
	String PROMPT_PROFILE_DIMENSION_SUMMARY = "prompt_profile_dimension_summary";
	/**
	 * 画像趋势总结提示词模板（通用于维度）
	 */
	String PROMPT_PROFILE_TREND_SUMMARY = "prompt_profile_trend_summary";
	/**
     * C139总结提示词模板
     */
	String PROMPT_C139_PROFILE_SUMMARY = "prompt_c139_profile_summary";
	/**
	 * 意见建议提示词模板
	 */
	String PROMPT_ADVICE_METHODOLOGY = "prompt_advice_methodology";
	/**
     * RAG知识库apiname
     */
	String RAG_KNOWLEDGE_BASE = "RAG_knowledge_base__c";
	/**
     * RAG知识库apiname
     */
	String RAG_KNOWLEDGE_USER = "RAG_user_knowledge_base__c";
	/**
	 * 提示变量
	 */
	String PROMPOT_INFO = "prompotInfo";
}