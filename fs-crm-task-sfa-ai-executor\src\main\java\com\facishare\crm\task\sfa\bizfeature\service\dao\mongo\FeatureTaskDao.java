package com.facishare.crm.task.sfa.bizfeature.service.dao.mongo;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureTaskConstants;
import com.facishare.paas.timezone.DateUtils;
import com.google.common.collect.Lists;
import lombok.Generated;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.mongodb.morphia.query.UpdateResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class FeatureTaskDao {

    @Generated
    private static final Logger log = LoggerFactory.getLogger(FeatureTaskDao.class);
    @Autowired
    @Qualifier("activityDataMongoDbStore")
    private Datastore customerDatastore;

    public FeatureTaskDocument saveOrUpdate(FeatureTaskDocument taskDocument) {
        if (taskDocument.getId() != null) {
            Query<FeatureTaskDocument> query = (Query)((Query)this.customerDatastore.createQuery(FeatureTaskDocument.class).field("id").equal(taskDocument.getId())).field("tenantId").equal(taskDocument.getTenantId());
            UpdateOperations<FeatureTaskDocument> ops = this.customerDatastore.createUpdateOperations(FeatureTaskDocument.class);
            if (taskDocument.getStatus() != null) {
                ops.set(FeatureTaskConstants.TaskFields.STATUS, taskDocument.getStatus());
            }

            if (taskDocument.getFailReason() != null) {
                ops.set("failReason", taskDocument.getFailReason());
            }

            if (taskDocument.getCreateBy() != null) {
                ops.set("createBy", taskDocument.getCreateBy());
            }

            ops.set("lastModifyTime", System.currentTimeMillis());

            try {
                UpdateResults results = this.customerDatastore.update(query, ops);
                if (results.getUpdatedCount() == 0) {
                    log.warn("更新特征计算任务失败，未找到记录, id:{}", taskDocument.getId());
                }

                return taskDocument;
            } catch (Exception e) {
                log.error("更新特征计算任务异常, id:{}", taskDocument.getId(), e);
                throw e;
            }
        } else {
            taskDocument.setCreateTime(System.currentTimeMillis());
            taskDocument.setLastModifyTime(System.currentTimeMillis());
            try {
                this.customerDatastore.save(taskDocument);
                log.info("新增特征计算任任务成功, id:{}", taskDocument.getId());
                return taskDocument;
            } catch (Exception e) {
                log.error("新增特征计算任任务异常", e);
                throw e;
            }
        }
    }

    public FeatureTaskDocument findById(ObjectId id) {
        return (FeatureTaskDocument)((Query)this.customerDatastore.createQuery(FeatureTaskDocument.class).field("id").equal(id)).get();
    }

    public void updateStatus(ObjectId id, String status, String failReason) {
        Query<FeatureTaskDocument> query = (Query)this.customerDatastore.createQuery(FeatureTaskDocument.class).field("id").equal(id);
        UpdateOperations<FeatureTaskDocument> ops = this.customerDatastore.createUpdateOperations(FeatureTaskDocument.class).set("status", status).set("failReason", failReason).set("lastModifyTime", System.currentTimeMillis());

        try {
            this.customerDatastore.update(query, ops);
            log.info("更新特征计算任务状态成功, id:{}, status:{}", id, status);
        } catch (Exception e) {
            log.error("更新特征计算任务状态异常, id:{}, status:{}", new Object[]{id, status, e});
            throw e;
        }
    }

    public void delete(ObjectId id) {
        try {
            customerDatastore.delete(FeatureTaskDocument.class, id);
            log.info("删除特征计算任务成功, id:{}", id);
        } catch (Exception e) {
            log.error("删除特征计算任务异常, id:{}", id, e);
            throw e;
        }
    }

    public List<FeatureTaskDocument> findExistJob(String tenantId) {
        List<String> statusList = Lists.newArrayList(FeatureTaskConstants.TaskStatus.FINISHED, FeatureTaskConstants.TaskStatus.RUNNING);
        Long startTime = DateUtils.getTodayStartTime();
        Long endTime = System.currentTimeMillis();
        try {
            Query<FeatureTaskDocument> query = ((Query)((Query)((Query)((Query)this.customerDatastore.createQuery(FeatureTaskDocument.class)
                    .field(FeatureTaskConstants.TaskFields.STATUS).in(statusList))
                    .field(FeatureTaskConstants.TaskFields.TENANT_ID).equal(tenantId))
                    .field("createTime").greaterThanOrEq(startTime))
                    .field("createTime").lessThanOrEq(endTime)).order("createTime");
            return query.asList();
        } catch (Exception e) {
            log.error("查询特征计算任务异常, startTime:{}, endTime:{}", startTime, endTime, e);
            return new ArrayList();
        }
    }

    public List<FeatureTaskDocument> findByStatusAndCreateTimeBetween(Long startTime, Long endTime, Integer offset, Integer limit) {
        List<String> statusList = Lists.newArrayList("init", "running");

        try {
            Query<FeatureTaskDocument> query = ((Query)((Query)((Query)this.customerDatastore.createQuery(FeatureTaskDocument.class)
                    .field("status").in(statusList))
                    .field("createTime").greaterThanOrEq(startTime))
                    .field("createTime").lessThanOrEq(endTime))
                    .order("createTime").offset(offset).limit(limit);
            return query.asList();
        } catch (Exception e) {
            log.error("查询特征计算任务异常, startTime:{}, endTime:{}, offset:{}, limit:{}", new Object[]{startTime, endTime, offset, limit, e});
            return new ArrayList();
        }
    }

}
