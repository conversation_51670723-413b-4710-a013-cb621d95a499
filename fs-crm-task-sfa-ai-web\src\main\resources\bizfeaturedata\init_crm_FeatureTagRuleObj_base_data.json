[{"id": "C_10", "tenant_id": "93747", "name": "项目是否提及采购流程", "active_status": "enable", "related_object_api_name": ["AccountObj", "NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户提及了采购相关的流程、程序或操作，包括：\n  1. **采购流程术语**：客户提及采购申请、询价、招标、合同签订等流程环节\n  2. **采购执行动作**：客户涉及供应商评估、比价、审批等采购操作\n  3. **隐含采购需求**：客户虽未直接说采购，但涉及资源协调、供应商管理等采购相关内容", "positive_sample": null, "negative_criteria": "客户完全未涉及任何采购相关内容", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": *************, "last_modified_by": "-10000", "last_modified_time": *************, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 3, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": ****************}, {"id": "C_11", "tenant_id": "93747", "name": "项目是否明确采购流程", "active_status": "enable", "related_object_api_name": ["AccountObj", "NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户明确说明了具体的采购流程步骤和要求，包括：\n  1. **具体步骤描述**：客户详细说明采购的各个环节和步骤\n  2. **采购方式确定**：明确了招标、比价、单一来源等采购方式\n  3. **决策标准清晰**：说明了供应商选择标准、评审要求等\n  4. **责任人与时间节点明确**：指定了各环节的负责人和时间安排", "positive_sample": null, "negative_criteria": "虽有提及采购但未明确具体流程细节，或完全未提及", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": *************, "last_modified_by": "-10000", "last_modified_time": *************, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 3, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": ****************}, {"id": "C_12", "tenant_id": "93747", "name": "项目周期是否提及", "active_status": "enable", "related_object_api_name": ["LeadsObj", "AccountObj", "NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户涉及了项目时间相关的内容，**无论是正面还是负面回答**，包括：\n  1. **直接提及时间范围**：客户涉及项目起止时间、实施周期等\n  2. **里程碑与阶段**：客户提及项目阶段划分、时间节点等\n  3. **时间相关回应**：客户回应关于项目时间安排的询问，即使回答\"还没定\"也算提及\n  4. **时间约束讨论**：客户涉及时间压力、延期风险等时间相关话题", "positive_sample": null, "negative_criteria": "客户完全未涉及任何项目时间相关内容", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": *************, "last_modified_by": "-10000", "last_modified_time": *************, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 4, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": ****************}, {"id": "C_14", "tenant_id": "93747", "name": "项目立项原因是否明确", "active_status": "enable", "related_object_api_name": ["AccountObj", "NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户说明了启动项目的背景、问题或机会，包括：\n  1. **市场需求与机会**：客户基于市场趋势、客户反馈等启动项目\n  2. **内部战略与目标**：因公司战略、内部问题等需要启动项目\n  3. **技术升级与创新**：基于技术发展、竞品对标等原因立项\n  4. **合规与风险要求**：因政策法规、风险控制等需求立项", "positive_sample": null, "negative_criteria": "客户未说明项目启动的具体原因或背景", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": *************, "last_modified_by": "-10000", "last_modified_time": *************, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 4, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753932583309572}, {"id": "C_22", "tenant_id": "93747", "name": "项目是否有明确预算金额", "active_status": "enable", "related_object_api_name": ["LeadsObj", "NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户明确提供了具体的预算金额或预算范围，包括：\n  1. **具体金额**：客户说明了确切的预算金额（如\"预算是50万\"、\"不超过100万\"）\n  2. **预算区间**：客户提供了预算的范围区间（如\"预算在30-50万之间\"）\n  3. **分项预算**：客户说明了不同项目阶段或模块的具体预算分配", "positive_sample": null, "negative_criteria": "以下情况不满足明确条件：\n  1. **预算未定**：客户表示预算还未确定、待定等\n  2. **模糊表述**：仅说\"有预算\"、\"预算不是问题\"等不具体的表述\n  3. **仅提及但未明确**：虽然讨论了预算但未给出具体金额", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932997844, "last_modified_by": "-10000", "last_modified_time": 1753932997997, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": ****************}, {"id": "C_1", "tenant_id": "93747", "name": "需求发起方是否明确", "active_status": "enable", "related_object_api_name": ["AccountObj", "NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户明确表达了需求的内部发起方，包括以下情况：\n  1. **明确提及具体推动者**：客户说明了具体的发起人（如\"王总要求\"、\"销售部门指示\"、\"CTO决定\"、\"采购部门主导\"）\n  2. **明确的部门驱动**：客户明确表达某个部门的具体需求（如\"客服部门要求改进\"、\"财务部门要求成本控制\"、\"IT部门建议升级\"）\n  3. **具体的驱动因素与发起方关联**：客户既说明了原因又指出了推动者（如\"因为客户投诉增多，客服部门要求改进系统\"）", "positive_sample": null, "negative_criteria": "以下情况均不满足明确条件：\n  1. **泛化表述**：仅说\"公司要升级\"、\"我们需要改进\"等没有具体推动者的表述\n  2. **被动需求**：仅表达\"听说有这个产品\"、\"了解一下\"等被动了解情况\n  3. **完全未提及**：对话中完全没有涉及谁在推动这个需求", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753870359551, "last_modified_by": "-10000", "last_modified_time": 1753931962102, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 4, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753931962110511}, {"id": "C_2", "tenant_id": "93747", "name": "是否向客户描述公司提供的服务", "active_status": "enable", "related_object_api_name": ["LeadsObj"], "follower": "our_side", "positive_criteria": "我方在对话中向客户介绍了服务内容，包括：\n  1. **服务范围与内容**：介绍我方提供的具体服务模块、功能或解决方案\n  2. **服务优势与差异化**：说明我方服务相比竞品的优势或特色\n  3. **服务流程与交付**：介绍服务实施的流程、团队配置或支持方式", "positive_sample": null, "negative_criteria": "我方未向客户介绍任何服务相关内容", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932009159, "last_modified_by": "-10000", "last_modified_time": 1753932009276, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753932009318108}, {"id": "C_3", "tenant_id": "93747", "name": "是否获取商务评判规则", "active_status": "enable", "related_object_api_name": ["NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户提供了评估供应商的具体标准，包括：\n  1. **评估维度明确**：客户说明了价格、技术能力、交付周期、合规性等评估维度及权重\n  2. **决策流程透明化**：客户介绍了评估、审批、决策的具体流程和标准\n  3. **细化评估细节**：客户提及违约金计算、响应时间要求、合同条款等具体要求", "positive_sample": null, "negative_criteria": "未获取客户的评估标准或决策规则", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932076367, "last_modified_by": "-10000", "last_modified_time": 1753932076495, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753932076547491}, {"id": "C_4", "tenant_id": "93747", "name": "是否获取竞对价格", "active_status": "enable", "related_object_api_name": ["NewOpportunityObj"], "follower": "your_side", "positive_criteria": "获取了竞争对手的价格信息，包括：\n  1. **直接获取竞品报价**：客户明确提及竞品的具体报价金额\n  2. **竞品定价策略**：客户介绍竞品的定价模式、付款方式等\n  3. **价格对比参考**：客户以竞品价格作为决策参考或对比基准", "positive_sample": null, "negative_criteria": "未获取任何竞争对手价格信息", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932126159, "last_modified_by": "-10000", "last_modified_time": 1753932126329, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753932126410724}, {"id": "C_5", "tenant_id": "93747", "name": "是否安排客户咨询", "active_status": "enable", "related_object_api_name": ["LeadsObj", "NewOpportunityObj"], "follower": "our_side", "positive_criteria": "我方为客户提供了咨询服务或安排了进一步交流，包括：\n  1. **主动提供咨询**：我方针对客户问题提供专业建议和解答\n  2. **安排后续咨询**：约定了后续的咨询会议或交流安排\n  3. **专业建议输出**：基于客户需求提供解决方案建议或实施指导", "positive_sample": null, "negative_criteria": "未提供咨询服务或安排咨询", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932162181, "last_modified_by": "-10000", "last_modified_time": 1753932162778, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753932162781738}, {"id": "C_6", "tenant_id": "93747", "name": "客户是否完成咨询", "active_status": "enable", "related_object_api_name": ["NewOpportunityObj"], "follower": "both_parties", "positive_criteria": "客户完成了预期的咨询活动，包括：\n  1. **需求得到满足**：客户表达对咨询结果的满意或问题得到解决\n  2. **达成行动共识**：双方就下一步行动达成一致意见\n  3. **咨询目标实现**：客户的咨询目的得到满足，疑虑得到解答", "positive_sample": null, "negative_criteria": "客户未完成咨询或咨询目标未达成", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932233689, "last_modified_by": "-10000", "last_modified_time": 1753932233821, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753932233840450}, {"id": "C_7", "tenant_id": "93747", "name": "是否演示产品方案", "active_status": "enable", "related_object_api_name": ["NewOpportunityObj"], "follower": "our_side", "positive_criteria": "我方向客户实际展示了产品功能或方案，包括：\n  1. **功能操作演示**：实际展示系统界面、操作流程或功能特性\n  2. **方案详细讲解**：通过具体案例或数据详细说明解决方案\n  3. **互动式体验**：让客户参与操作或体验产品功能", "positive_sample": null, "negative_criteria": "仅约定后续演示，本次对话中未实际展示产品", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932283585, "last_modified_by": "-10000", "last_modified_time": 1753932283792, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": ****************}, {"id": "C_20", "tenant_id": "93747", "name": "项目周期是否已明确", "active_status": "enable", "related_object_api_name": ["LeadsObj", "AccountObj", "NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户明确提供了具体的项目时间安排，包括：\n  1. **明确的时间范围**：客户说明了具体的项目起止时间、实施周期（如\"项目需要在3个月内完成\"、\"从下月开始实施，预计6个月交付\"）\n  2. **详细的里程碑计划**：客户提供了项目各阶段的时间节点和里程碑安排\n  3. **具体的时间要求**：客户明确了上线时间、验收时间等关键时间节点", "positive_sample": null, "negative_criteria": "以下情况不满足明确条件：\n  1. **时间未定**：客户表示时间还未确定、待定等\n  2. **模糊时间表述**：仅说\"尽快\"、\"年内\"等不具体的时间要求\n  3. **仅提及但未明确**：虽然讨论了时间话题但未给出具体安排", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932507373, "last_modified_by": "-10000", "last_modified_time": 1753932507521, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753932507574242}, {"id": "C_16", "tenant_id": "93747", "name": "是否提及资金到位情况", "active_status": "enable", "related_object_api_name": ["NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户提到了资金准备、到位情况等相关内容，包括：\n  1. **资金已到账**：客户确认资金已经到位\n  2. **支付计划**：客户说明资金到位的时间安排\n  3. **资金到位条件**：客户介绍资金到位的前提条件\n  4. **资金风险应对**：客户提及资金到位的风险或应对措施", "positive_sample": null, "negative_criteria": "客户完全未提及资金到位相关情况", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932624712, "last_modified_by": "-10000", "last_modified_time": 1753932624875, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753932624903427}, {"id": "C_18", "tenant_id": "93747", "name": "项目是否提及决策流程", "active_status": "enable", "related_object_api_name": ["NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户提供了决策相关的流程信息，包含以下至少一个要素：\n  1. **决策链信息**：说明谁是决策者、决策层级等\n  2. **时间轴安排**：提及决策的时间节点、周期等\n  3. **评估标准**：说明决策的依据、标准或条件\n  4. **预算审批流程**：介绍预算审批的流程和要求", "positive_sample": null, "negative_criteria": "客户未涉及任何决策流程相关内容", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932658529, "last_modified_by": "-10000", "last_modified_time": 1753932658756, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753932658764517}, {"id": "C_19", "tenant_id": "93747", "name": "是否已确定关键联系人", "active_status": "enable", "related_object_api_name": ["LeadsObj"], "follower": "your_side", "positive_criteria": "客户明确了项目中的关键人员，包括：\n  1. **决策人确认**：客户明确了项目的关键决策者\n  2. **负责人指定**：客户确定了项目的主要负责人或协调人\n  3. **联系人约定**：客户约定了后续沟通的主要联系人", "positive_sample": null, "negative_criteria": "客户未确定或明确关键联系人", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932860946, "last_modified_by": "-10000", "last_modified_time": 1753932861132, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753932861188679}, {"id": "C_21", "tenant_id": "93747", "name": "项目是否有预算", "active_status": "enable", "related_object_api_name": ["LeadsObj", "NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户提及了项目预算相关的信息，包括：\n  1. **预算已准备**：客户确认项目有预算或预算已批准\n  2. **预算规划**：客户提及预算的来源、规划或申请情况\n  3. **预算约束**：客户说明了预算限制、范围或相关要求\n  4. **预算讨论**：对话中涉及预算话题，无论是正面还是负面回答", "positive_sample": null, "negative_criteria": "客户完全未提及任何预算相关信息", "negative_sample": null, "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932902597, "last_modified_by": "-10000", "last_modified_time": 1753932902786, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 2, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": ****************}, {"id": "C_8", "tenant_id": "93747", "name": "项目目标是否明确", "active_status": "enable", "related_object_api_name": ["AccountObj", "NewOpportunityObj"], "follower": "your_side", "positive_criteria": "客户明确表达了要解决的具体业务问题和目标，包括以下三个层面之一：\n  1. **明确的业务问题**：客户清晰描述了当前存在的业务痛点、问题或改进需求（如\"现有系统导致订单处理错误率过高，影响客户满意度\"）\n  2. **具体的功能需求与业务价值**：客户说明了需要系统具备的功能，并关联了业务价值（如\"需要CRM系统来规范销售流程，减少客户流失\"）\n  3. **量化的业务目标**：客户提出了明确的量化指标或预期效果（如\"希望将客户复购率从40%提升到60%\"）", "positive_sample": "- 客户：我们要提升销售团队效率30%，需要一套CRM系统来规范销售流程，减少客户流失，月度目标完成率要达到85%以上\n- 客户：现有系统导致订单处理错误率过高，影响客户满意度，希望新系统能将错误率降低到5%以下，提升交付效率\n- 客户：业务扩张后客户管理混乱，需要系统化管理客户生命周期，预期能将客户复购率从40%提升到60%", "negative_criteria": "以下情况不满足明确条件：\n  1. **仅功能需求**：客户只说需要某个系统或功能，但未说明要解决什么业务问题（如\"想要一套CRM系统，能管理客户和订单就行\"）\n  2. **问题描述无目标**：客户描述了问题但没有明确要达到的目标（如\"现在用的系统不太好用，想换个更好的\"）\n  3. **模糊意向**：客户仅表达升级意向，缺乏具体目标（如\"老板说要上个系统，具体要达到什么效果还没定\"）", "negative_sample": "- 客户：我们想要一套CRM系统，能管理客户和订单就行（仅功能需求，无业务目标）\n- 客户：现在用的系统不太好用，想换个更好的（问题描述但无明确目标）\n- 客户：老板说要上个系统，具体要达到什么效果还没定（缺乏明确目标）", "focus_on_precision": false, "system_type": "system", "display_name": null, "owner": "-10000", "lock_status": "0", "life_status": "normal", "record_type": "default__c", "created_by": "-10000", "create_time": 1753932331847, "last_modified_by": "-10000", "last_modified_time": 1753933137842, "extend_obj_data_id": null, "package": "CRM", "object_describe_id": null, "object_describe_api_name": "FeatureTagRuleObj", "version": 3, "lock_user": null, "lock_rule": null, "life_status_before_invalid": null, "is_deleted": 0, "out_tenant_id": null, "out_owner": null, "data_own_department": "999999", "data_own_organization": null, "data_auth_code": "5b3d1ab268725650", "change_type": null, "out_data_auth_code": "0f95a090e5625cb0", "order_by": null, "data_auth_id": 7809836, "out_data_auth_id": 634605, "dimension_d1": null, "dimension_d2": null, "dimension_d3": null, "mc_currency": null, "mc_exchange_rate": null, "mc_functional_currency": null, "mc_exchange_rate_version": null, "origin_source": null, "out_data_own_department": null, "out_data_own_organization": null, "sys_modified_time": 1753933137860749}]