package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 维度评分模型
 * 用于AI Agent的Evaluator组件，记录各个维度的评分
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DimensionScores {
    /**
     * 连贯性评分
     * 评估段落内容的连贯性，主题明确性和逻辑流畅度，范围0-1
     */
    private double coherence;
    
    /**
     * 摘要质量评分
     * 评估摘要的准确性和完整性，是否准确反映段落核心内容，范围0-1
     */
    private double summaryQuality;
    
    /**
     * 分段数量合理性评分
     * 评估分段数量是否恰当，段落长度是否均衡，范围0-1
     */
    private double segmentationCount;
    
    /**
     * 段落区分度评分
     * 评估段落之间的界限清晰度和主题转换自然度，范围0-1
     */
    private double distinction;
}
