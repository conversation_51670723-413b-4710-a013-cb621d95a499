package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureWeightConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileItemScoreConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FeatureWeightDao {

    @Autowired
    private ServiceFacade serviceFacade;
    public Map<String, BigDecimal> fetchFeaturesWeightsByMethodologyIdAndType(List<IObjectData> allFeatureWeights, String itemType, String nodeId) {
        if (itemType.equals(ProfileItemScoreConstants.Type.DIMENSION.getValue())) {
            List<IObjectData> featureWeights = allFeatureWeights.stream()
                    .filter(featureWeight -> featureWeight.get(FeatureWeightConstants.TYPE, String.class).equals(FeatureWeightConstants.Type.DIMENSION.getValue()))
                    .collect(Collectors.toList());
            return featureWeights.stream()
                    .collect(Collectors.toMap(
                            featureWeight -> featureWeight.get(FeatureWeightConstants.FEATURE_ID, String.class),
                            featureWeight -> featureWeight.get(FeatureWeightConstants.WEIGHT, BigDecimal.class)));
        }
        if (itemType.equals(ProfileItemScoreConstants.Type.NODE.getValue())) {
            List<IObjectData> featureWeights = allFeatureWeights.stream()
                    .filter(featureWeight -> featureWeight.get(FeatureWeightConstants.TYPE, String.class).equals(FeatureWeightConstants.Type.FLOW.getValue()))
                    .filter(featureWeight -> featureWeight.get(FeatureWeightConstants.NODE_ID, String.class).equals(nodeId))
                    .collect(Collectors.toList());
            return featureWeights.stream()
                    .collect(Collectors.toMap(
                            featureWeight -> featureWeight.get(FeatureWeightConstants.FEATURE_ID, String.class),
                            featureWeight -> featureWeight.get(FeatureWeightConstants.WEIGHT, BigDecimal.class)));
        }
        return new HashMap<>();
    }

    public List<IObjectData> fetchFeaturesWeightsByFeatureIds(User user, String methodologyId, List<String> featureIds) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, FeatureWeightConstants.METHODOLOGY_ID, methodologyId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, FeatureWeightConstants.FEATURE_ID, featureIds);
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.FEATURE_WEIGHT, query).getData();
    }
}
