package com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.activitysummary.constants.AIAgentConstants;
import com.facishare.crm.task.sfa.activitysummary.model.PlanModel;
import com.facishare.crm.task.sfa.activitysummary.model.SegmentModel;
import com.facishare.crm.task.sfa.activitysummary.model.SegmentsResponse;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Executor服务类
 * 负责执行具体分段任务
 */
@Service
@Slf4j
public class ExecutorService {

    @Autowired
    private CompletionsService completionsService;

    /**
     * 运行Executor组件，执行分段任务
     * <p>
     * 提示词模板:
     * ```
     * 你是一个专业的文本分段专家，负责根据策略将会议内容分成多个有意义的段落。
     * <p>
     * 会议内容:
     * ${sence_variables.custom_sence.meetingContent}
     * <p>
     * 上次会议摘要:
     * ${sence_variables.custom_sence.lastSummary}
     * <p>
     * 分段策略:
     * ${sence_variables.custom_sence.plan_strategy}
     * <p>
     * 预期段落数: ${sence_variables.custom_sence.plan_expected_segments}
     * <p>
     * 关注点:
     * ${sence_variables.custom_sence.plan_focus_points}
     * <p>
     * 特殊处理:
     * ${sence_variables.custom_sence.plan_special_handling}
     * <p>
     * 请将会议内容分成多个有意义的段落，每个段落包含相关的内容ID和摘要。
     * <p>
     * 输出格式要求:
     * {
     * "segments": [
     * {
     * "segment_id": "1",
     * "content_ids": ["id1", "id2", ...],
     * "summary": "段落摘要",
     * "confidence": 置信度(0-1)
     * },
     * ...
     * ]
     * }
     * ```
     */
    public List<SegmentModel> runExecutor(User user, String meetContent, String previousSummary, PlanModel plan) {
        try {
            AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
            arg.setApiName(AIAgentConstants.API_NAME_EXECUTOR);

            // 创建custom_sence子对象
            Map<String, Object> customSence = Maps.newHashMap();
            customSence.put("meetingContent", meetContent);
            customSence.put("lastSummary", StringUtils.isBlank(previousSummary) ? "" : previousSummary);


            // 使用下划线格式添加plan相关属性
            customSence.put("plan_strategy", plan.getStrategy());
            customSence.put("plan_expected_segments", String.valueOf(plan.getExpectedSegments()));
            customSence.put("plan_focus_points", JSON.toJSONString(plan.getFocusPoints()));
            customSence.put("plan_special_handling", JSON.toJSONString(plan.getSpecialHandling()));

            // 直接将customSence赋值给arg.setSceneVariables()
            arg.setSceneVariables(customSence);

            // 使用requestCompletionData方法直接获取对象
            SegmentsResponse segmentsResponse = completionsService.requestCompletionData(
                    user, arg, AIAgentConstants.JSON_FORMAT_SEGMENTS, SegmentsResponse.class);

            if (segmentsResponse == null) {
                return null;
            }

            return segmentsResponse.getSegments();
        } catch (Exception e) {
            log.error("Executor执行失败", e);
            return null;
        }
    }
}
