package com.facishare.crm.task.sfa.activitysummary.service.strategy;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTag;
import com.facishare.crm.task.sfa.activitysummary.model.DirectTaggingResultModel;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphContext;
import com.facishare.crm.task.sfa.activitysummary.model.TagRuleData;
import com.facishare.crm.task.sfa.activitysummary.service.ContentSplitter;
import com.facishare.crm.task.sfa.activitysummary.service.ParagraphAIService;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.ActivityParagraphService;
import com.facishare.crm.task.sfa.activitysummary.service.paragraph.DocumentProcessService;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureTagRuleConstants;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureMqModel;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureValueProducer;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.impl.search.Operator;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 直接打标签服务
 * on 2024/6/1
 *
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class DirectTaggingService {

    @Autowired
    private ParagraphAIService paragraphAIService;

    @Autowired
    private ActivityParagraphService activityParagraphService;

    @Autowired
    private DocumentProcessService documentProcessService;

    @Autowired
    private ContentSplitter contentSplitter;

    @Autowired
    private ServiceFacade serviceFacade;

    private static boolean multipleMatchEnabled;

    private static int concurrentCount;

    @Resource(name = "activityTagExecutor")
    private ExecutorService executorService;

    @Autowired
    private FeatureValueProducer featureValueProducer;

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            multipleMatchEnabled = config.getBool("multipleMatchEnabled", false);
            concurrentCount = config.getInt("tagConcurrentCount", 3);
        });
    }

    public void process(User user, ActivityMessage activityMessage, String type) {
        String objectId = activityMessage.getObjectId();
        // 获取活动记录数据
        IObjectData activeRecordData = getActiveRecordData(user, objectId);
        if (activeRecordData == null) {
            return;
        }

        // 获取交互内容
        String interactiveContent = getInteractiveContent(user, activeRecordData, objectId, type);
        if (interactiveContent == null) {
            return;
        }

        // 对内容进行分割处理
        List<String> contentSegments = contentSplitter.splitContent(interactiveContent);
        if (CollectionUtils.isEmpty(contentSegments)) {
            return;
        }

        // 获取所有子标签
        List<TagRuleData> allChildTags = getAllChildTags(user, activeRecordData);
        if (CollectionUtils.isEmpty(allChildTags)) {
            return;
        }

        Map<String, String> nameToIdMap = buildNameToIdMap(allChildTags);

        // 按focus字段分组标签
        List<TagRuleData> focusTags = allChildTags.stream()
                .filter(TagRuleData::isFocus)
                .collect(Collectors.toList());
        List<TagRuleData> normalTags = allChildTags.stream()
                .filter(tag -> !tag.isFocus())
                .collect(Collectors.toList());
        
        log.info("[DirectTaggingService] 标签分组完成, objectId: {}, 总标签数: {}, focus标签数: {}, 普通标签数: {}", 
                objectId, allChildTags.size(), focusTags.size(), normalTags.size());

        // 处理每个内容段落
        List<DirectTaggingResultModel> allFinalResults = Lists.newArrayList();
        for (String contentSegment : contentSegments) {
            // 1. 处理focus标签（每个标签单独运行）
            List<DirectTaggingResultModel> focusResults = processFocusTags(user, contentSegment, focusTags, nameToIdMap, objectId);
            allFinalResults.addAll(focusResults);
            
            // 2. 处理普通标签（批量运行）
            List<DirectTaggingResultModel> normalResults = processNormalTags(user, contentSegment, normalTags, nameToIdMap, objectId);
            allFinalResults.addAll(normalResults);
        }

        // 去重处理结果
        List<DirectTaggingResultModel> finalResults = deduplicateResults(allFinalResults);

        if (CollectionUtils.isEmpty(finalResults)) {
            log.info("[DirectTaggingService] No results found for objectId: {}", objectId);
            return;
        }

        // 构建完整的nameToId映射（包含focus和normal标签）
        Map<String, String> completeNameToId = buildNameToIdMap(allChildTags);
        
        // 处理结果并保存
        processAndSaveResults(user, activeRecordData, type, objectId, finalResults, completeNameToId);

        // 将ActivityTagMessage转换为FeatureMqModel.Message
        FeatureMqModel.Message featureMessage = FeatureMqModel.Message.builder()
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .objectId(objectId)
                .objectApiName(activityMessage.getObjectApiName())
                .build();
        // 发送特征计算消息
        featureValueProducer.sendTagMessage(featureMessage);
    }

    /**
     * 获取活动记录数据
     */
    private IObjectData getActiveRecordData(User user, String objectId) {
        return activityParagraphService.queryActiveRecord(user, objectId);
    }

    /**
     * 获取交互内容
     */
    private String getInteractiveContent(User user, IObjectData activeRecordData, String objectId, String type) {
        if (type.equals(ParagraphContext.MONGO_KEY)) {
            List<InteractiveDocument> interactiveDocuments = queryAllDocuments(user, objectId);
            if (CollectionUtils.isNotEmpty(interactiveDocuments)) {
                return formatMongoContent(user, interactiveDocuments, objectId);
            }
            return null;
        }
        return activityParagraphService.getInteractiveContent(activeRecordData, objectId);
    }

    /**
     * 查询所有交互文档
     * 循环查询所有页面的数据，直到获取完所有文档
     *
     * @param user     用户信息
     * @param objectId 对象ID
     * @return 所有交互文档列表
     */
    private List<InteractiveDocument> queryAllDocuments(User user, String objectId) {
        List<InteractiveDocument> allDocuments = Lists.newArrayList();

        try {
            // 设置最大页数限制（假设每页50条，最多查询1000页，即50000条记录）
            for (int pageNo = 0; pageNo <= 1000; pageNo++) {
                List<InteractiveDocument> pageDocuments = documentProcessService.queryDocumentsPage(
                        user.getTenantId(), objectId, pageNo);

                if (CollectionUtils.isEmpty(pageDocuments)) {
                    // 没有更多数据，退出循环
                    log.info("queryAllDocuments 完成, objectId: {}, 总页数: {}, 总文档数: {}",
                            objectId, pageNo, allDocuments.size());
                    break;
                }

                allDocuments.addAll(pageDocuments);

                // 检查是否达到最大页数限制
                if (pageNo == 1000) {
                    log.warn("queryAllDocuments 达到最大页数限制, objectId: {}, pageNo: {}", objectId, pageNo);
                }
            }

        } catch (Exception e) {
            log.error("queryAllDocuments error, objectId: {}", objectId, e);
        }

        return allDocuments;
    }

    /**
     * 格式化Mongo内容
     * 输出格式：姓名（时间）：具体的对话
     *
     * @param user            用户信息
     * @param interactiveList 交互文档列表
     * @param objectId        对象ID
     * @return 格式化后的内容
     */
    private String formatMongoContent(User user, List<InteractiveDocument> interactiveList, String objectId) {
        if (CollectionUtils.isEmpty(interactiveList)) {
            return "";
        }
        String speakLabel = I18N.text("sfa.activity.corpus.list_item_user_label");
        StringBuilder sb = new StringBuilder();

        // 查询活动用户信息并构建用户映射
        Map<String, String> userMap = queryActivityUserMap(user, objectId);

        for (InteractiveDocument document : interactiveList) {
            try {
                // 获取用户名
                String userName = getUserName(document, userMap, speakLabel);

                // 获取时间信息
                String timeInfo = document.getStartTime();

                // 构建格式：姓名（时间）：具体的对话
                sb.append(userName);
                if (StringUtils.isNotBlank(timeInfo)) {
                    sb.append("（").append(timeInfo).append("）");
                }
                sb.append("：").append(document.getContent()).append("\n");
            } catch (Exception e) {
                log.error("formatMongoContent error, objectId: {}", objectId, e);
            }
        }
        String resultStr = sb.toString();

        if (StringUtils.isBlank(resultStr) || resultStr.length() < 150) {
            log.warn("活动记录数据中没有交互内容，或少于150个字，objectId: {}", objectId);
            return null;
        }
        return resultStr;
    }

    /**
     * 查询活动用户映射
     *
     * @param user     用户信息
     * @param objectId 活动记录ID
     * @return 用户ID到用户名的映射
     */
    private Map<String, String> queryActivityUserMap(User user, String objectId) {
        SearchTemplateQueryPlus searchQuery = new SearchTemplateQueryPlus();
        searchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        searchQuery.addFilter(Tenantable.TENANT_ID, Operator.EQ, user.getTenantId());
        searchQuery.addFilter("active_record_id", Operator.EQ, objectId);
        searchQuery.setLimit(1000);
        searchQuery.setFindExplicitTotalNum(false);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);

        try {
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(
                    user, "ActivityUserObj", searchQuery);

            if (ObjectUtils.isNotEmpty(queryResult) && CollectionUtils.isNotEmpty(queryResult.getData())) {
                return queryResult.getData().stream()
                        .collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (v1, v2) -> v1));
            }
        } catch (Exception e) {
            log.error("queryActivityUserMap error, objectId: {}", objectId, e);
        }

        return new HashMap<>();
    }

    /**
     * 获取用户名
     *
     * @param document   交互文档
     * @param userMap    用户映射
     * @param speakLabel 发言人标签
     * @return 用户名
     */
    private String getUserName(InteractiveDocument document, Map<String, String> userMap, String speakLabel) {
        String userName = document.getUserName();
        if (userMap.containsKey(document.getActivityUserId())) {
            userName = userMap.get(document.getActivityUserId());
        }
        if (userName.contains("user_")) {
            String[] userNameArr = userName.split("_");
            if (userNameArr.length > 1) {
                userName = speakLabel + userNameArr[1];
            }
        }

        return userName;
    }

    /**
     * 格式化时间信息
     *
     * @param document 交互文档
     * @return 格式化后的时间信息
     */
    private String formatTimeInfo(InteractiveDocument document) {
        String startTime = document.getStartTime();
        String endTime = document.getEndTime();

        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            return startTime + "-" + endTime;
        } else if (StringUtils.isNotBlank(startTime)) {
            return startTime;
        } else if (StringUtils.isNotBlank(endTime)) {
            return endTime;
        }

        return "";
    }

    /**
     * 获取所有子标签
     * 从FeatureTagRuleObj对象查询启用的标签，并根据销售记录的线索字段进行过滤
     */
    private List<TagRuleData> getAllChildTags(User user, IObjectData activeRecordData) {
        try {
            // 构建查询条件：启用状态且关联对象为线索
            SearchTemplateQueryPlus searchQuery = new SearchTemplateQueryPlus();
            searchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
            searchQuery.addFilter(FeatureTagRuleConstants.ACTIVE_STATUS, Operator.EQ,
                    FeatureTagRuleConstants.ActiveStatusType.ENABLE.getActiveStatusType());
            List<String> relatedObjectApiNameList = Lists.newArrayList();
            // 检查活动记录是否有线索字段，如果有则过滤相关的标签
            String leadsId = activeRecordData.get(CommonConstant.LEADS_ID, String.class);
            String newOpportunityId = activeRecordData.get(CommonConstant.NEW_OPPORTUNITY_ID, String.class);
            String accountId = activeRecordData.get(CommonConstant.ACCOUNT_ID, String.class);
            if (StringUtils.isNotBlank(leadsId)) {
                relatedObjectApiNameList.add(FeatureTagRuleConstants.RelatedObjectApiNameType.LEADSOBJ.getRelatedObjectApiNameType());
            }
            if (StringUtils.isNotBlank(newOpportunityId)){
                relatedObjectApiNameList.add(FeatureTagRuleConstants.RelatedObjectApiNameType.NEWOPPORTUNITYOBJ.getRelatedObjectApiNameType());
            }
            if (StringUtils.isNotBlank(accountId)) {
                relatedObjectApiNameList.add(FeatureTagRuleConstants.RelatedObjectApiNameType.ACCOUNTOBJ.getRelatedObjectApiNameType());
            }
            if (CollectionUtils.isEmpty(relatedObjectApiNameList)) {
                return Lists.newArrayList();
            }
            searchQuery.addFilter(FeatureTagRuleConstants.RELATED_OBJECT_API_NAME, Operator.HASANYOF, relatedObjectApiNameList);

            searchQuery.setLimit(1000);
            searchQuery.setFindExplicitTotalNum(false);
            searchQuery.setNeedReturnCountNum(false);
            searchQuery.setPermissionType(0);

            // 查询标签规则数据
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(
                    user, FeatureTagRuleConstants.OBJECT_API_NAME, searchQuery);

            if (ObjectUtils.isEmpty(queryResult) || CollectionUtils.isEmpty(queryResult.getData())) {
                log.warn("No feature tag rules found for tenant: {}", user.getTenantId());
                return Lists.newArrayList();
            }

            // 转换为TagRuleData对象
            return queryResult.getData().stream()
                    .map(this::convertToTagRuleData)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error getting all child tags for tenant: {}", user.getTenantId(), e);
            // 降级到原有逻辑
            return Lists.newArrayList();
        }
    }

    /**
     * 将IObjectData转换为TagRuleData
     */
    private TagRuleData convertToTagRuleData(IObjectData tagRuleData) {
        try {
            String positiveCriteria = tagRuleData.get(FeatureTagRuleConstants.POSITIVE_CRITERIA, String.class);
            String negativeCriteria = tagRuleData.get(FeatureTagRuleConstants.NEGATIVE_CRITERIA, String.class);
            String positiveSample = tagRuleData.get(FeatureTagRuleConstants.POSITIVE_SAMPLE, String.class);
            String negativeSample = tagRuleData.get(FeatureTagRuleConstants.NEGATIVE_SAMPLE, String.class);
            String follower = tagRuleData.get(FeatureTagRuleConstants.FOLLOWER, String.class);
            boolean focus = tagRuleData.get(FeatureTagRuleConstants.FOCUS_ON_PRECISION, Boolean.class,false);

            // 使用标签名称作为name
            String name = tagRuleData.getName();
            if (StringUtils.isBlank(name)) {
                log.warn("Tag rule has empty name, id: {}", tagRuleData.getId());
                return null;
            }

            return TagRuleData.builder()
                    .id(tagRuleData.getId())
                    .name(name.trim())
                    .positiveCriteria(StringUtils.defaultString(positiveCriteria, "").trim())
                    .negativeCriteria(StringUtils.defaultString(negativeCriteria, "").trim())
                    .positiveSample(StringUtils.defaultString(positiveSample, "").trim())
                    .negativeSample(StringUtils.defaultString(negativeSample, "").trim())
                    .follower(StringUtils.defaultString(follower, "").trim())
                    .focus(focus)
                    .build();

        } catch (Exception e) {
            log.error("Error converting tag rule data to TagRuleData, id: {}", tagRuleData.getId(), e);
            return null;
        }
    }

    /**
     * 将ActivityTag列表转换为TagRuleData列表（降级时使用）
     */
    private List<TagRuleData> convertActivityTagsToTagRuleData(List<ActivityTag> activityTags) {
        return activityTags.stream()
                .map(activityTag -> TagRuleData.builder()
                        .id(activityTag.getId())
                        .name(activityTag.getName())
                        .positiveCriteria(activityTag.getDescription() != null ? activityTag.getDescription() : "")
                        .negativeCriteria("")
                        .positiveSample("")
                        .negativeSample("")
                        .follower("")
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 构建标签字符串（用作AI提示词）
     */
    private String buildTagsString(List<TagRuleData> allChildTags) {
        StringBuilder tags = new StringBuilder();

        for (TagRuleData tag : allChildTags) {
            // 1. 标签名称（单独一行）
            tags.append("**").append(tag.getName()).append("**\n");

            // 2. 关注方信息（必填字段）
            String followerText = getFollowerDisplayText(tag.getFollower());
            tags.append("- 关注方 : [").append(followerText).append("]\n");

            // 3. 正向判断标准（必填字段）
            String positiveCriteria = StringUtils.isNotBlank(tag.getPositiveCriteria()) ? tag.getPositiveCriteria() : "";
            tags.append("- 正向判断标准：[").append(positiveCriteria).append("]\n");

            // 4. 负向判断标准（必填字段）
            String negativeCriteria = StringUtils.isNotBlank(tag.getNegativeCriteria()) ? tag.getNegativeCriteria() : "";
            tags.append("- 负向判断标准：[").append(negativeCriteria).append("]\n");

            // 5. 正向样例（可选字段）
            if (StringUtils.isNotBlank(tag.getPositiveSample())) {
                tags.append("- 正向样例：[").append(tag.getPositiveSample()).append("]\n");
            }

            // 6. 负向样例（可选字段）
            if (StringUtils.isNotBlank(tag.getNegativeSample())) {
                tags.append("- 负向样例：[").append(tag.getNegativeSample()).append("]\n");
            }

            tags.append("\n");
        }
        return tags.toString();
    }

    /**
     * 获取关注方显示文本
     */
    private String getFollowerDisplayText(String follower) {
        if (StringUtils.isBlank(follower)) {
            return "";
        }

        switch (follower.toLowerCase()) {
            case "our_side":
                return "我方表现";
            case "your_side":
                return "客方信息";
            case "both_parties":
                return "双方互动";
            default:
                return follower;
        }
    }

    /**
     * 构建标签名称到ID的映射
     */
    private Map<String, String> buildNameToIdMap(List<TagRuleData> allChildTags) {
        return allChildTags.stream().collect(Collectors.toMap(TagRuleData::getName, TagRuleData::getId));
    }

    /**
     * 单次匹配处理
     */
    private List<DirectTaggingResultModel> processWithSingleMatch(User user, String interactiveContent,
                                                                  String tagsString, List<TagRuleData> allChildTags,
                                                                  Map<String, String> nameToId, String objectId) {
        List<DirectTaggingResultModel> results = paragraphAIService.requestDirectTagging(user, interactiveContent,
                tagsString);
        log.info("大模型返回结果, objectId: {}, results: {}", objectId, JSON.toJSONString(results));
        return validateAndRetryTagging(user, interactiveContent, results, allChildTags, nameToId);
    }

    /**
     * 多次匹配处理（多个并发请求）
     */
    private List<DirectTaggingResultModel> processWithMultipleMatch(User user, String interactiveContent,
                                                                    String tagsString, List<TagRuleData> allChildTags,
                                                                    Map<String, String> nameToId, String objectId) {
        // 创建多个并发请求，使用processWithSingleMatch进行单次调用
        List<CompletableFuture<List<DirectTaggingResultModel>>> futures = Lists.newArrayList();

        for (int i = 0; i < concurrentCount; i++) {
            CompletableFuture<List<DirectTaggingResultModel>> future = CompletableFuture
                    .supplyAsync(() -> processWithSingleMatch(user, interactiveContent, tagsString, allChildTags,
                            nameToId, objectId), executorService);
            futures.add(future);
        }

        try {
            // 统一等待所有请求完成，设置120秒超时
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.get(120, TimeUnit.SECONDS);

            // 统一获取所有结果
            List<List<DirectTaggingResultModel>> allResults = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

            // 合并结果，如果有大部分结果认为某个标签匹配，则视为匹配
            return mergeMultipleResults(allResults, nameToId);
        } catch (Exception e) {
            log.error("多次匹配处理异常,转成同步处理", e);
            // 降级到单次匹配
            return processWithSingleMatch(user, interactiveContent, tagsString, allChildTags, nameToId, objectId);
        }
    }

    /**
     * 合并多次匹配的结果
     */
    private List<DirectTaggingResultModel> mergeMultipleResults(List<List<DirectTaggingResultModel>> allResults,
                                                                Map<String, String> nameToId) {

        Map<String, Integer> tagMatchCount = Maps.newHashMap();
        Map<String, String> tagSourceMap = Maps.newHashMap();

        // 统计每个标签在所有结果中的匹配次数
        for (List<DirectTaggingResultModel> results : allResults) {
            countTagMatches(results, tagMatchCount, tagSourceMap, nameToId);
        }

        // 构建最终结果，匹配次数>=大部分(超过一半)的标签视为匹配
        int majorityThreshold = Math.max(1, (allResults.size() + 1) / 2);
        List<DirectTaggingResultModel> finalResults = Lists.newArrayList();
        tagMatchCount.entrySet().stream()
                .filter(entry -> entry.getValue() >= majorityThreshold)
                .forEach(entry -> {
                    DirectTaggingResultModel result = new DirectTaggingResultModel();
                    result.setTagName(entry.getKey());
                    result.setMatch(true);
                    result.setSource(tagSourceMap.get(entry.getKey()));
                    finalResults.add(result);
                });

        log.info("多次匹配合并结果, concurrentCount: {}, majorityThreshold: {}, tagMatchCount: {}, finalResults: {}",
                concurrentCount, majorityThreshold, JSON.toJSONString(tagMatchCount), JSON.toJSONString(finalResults));

        return finalResults;
    }

    /**
     * 统计标签匹配次数
     */
    private void countTagMatches(List<DirectTaggingResultModel> results,
                                 Map<String, Integer> tagMatchCount,
                                 Map<String, String> tagSourceMap,
                                 Map<String, String> nameToId) {
        if (CollectionUtils.isEmpty(results)) {
            return;
        }

        results.stream()
                .filter(DirectTaggingResultModel::isMatch)
                .filter(result -> nameToId.containsKey(result.getTagName())) // 只统计有效的标签
                .forEach(result -> {
                    String tagName = result.getTagName();
                    tagMatchCount.put(tagName, tagMatchCount.getOrDefault(tagName, 0) + 1);
                    if (!tagSourceMap.containsKey(tagName)) {
                        tagSourceMap.put(tagName, result.getSource());
                    }
                });
    }

    /**
     * 验证标签名称并重试
     */
    private List<DirectTaggingResultModel> validateAndRetryTagging(User user, String interactiveContent,
                                                                   List<DirectTaggingResultModel> results,
                                                                   List<TagRuleData> allChildTags,
                                                                   Map<String, String> nameToId) {
        if (CollectionUtils.isEmpty(results)) {
            return results;
        }

        // 检查是否有不在入参里的tagName
        List<String> invalidTagNames = results.stream()
                .filter(DirectTaggingResultModel::isMatch)
                .map(DirectTaggingResultModel::getTagName)
                .filter(tagName -> !nameToId.containsKey(tagName))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(invalidTagNames)) {
            log.warn("发现无效的标签名称，重新调用大模型, invalidTagNames: {}", JSON.toJSONString(invalidTagNames));

            // 重新调用大模型
            String tagsString = buildTagsString(allChildTags);
            List<DirectTaggingResultModel> retryResults = paragraphAIService.requestDirectTagging(user,
                    interactiveContent, tagsString);

            // 再次验证，只保留match为true且tagName有效的结果
            if (CollectionUtils.isNotEmpty(retryResults)) {
                return retryResults.stream()
                        .filter(result -> result.isMatch() && nameToId.containsKey(result.getTagName()))
                        .collect(Collectors.toList());
            }
        }

        // 只保留match为true且tagName有效的结果
        return results.stream()
                .filter(result -> result.isMatch() && nameToId.containsKey(result.getTagName()))
                .collect(Collectors.toList());
    }

    /**
     * 处理结果并保存
     */
    private void processAndSaveResults(User user, IObjectData activeRecordData, String type, String objectId,
                                       List<DirectTaggingResultModel> results, Map<String, String> nameToId) {
        log.info("最终打标签结果, objectId: {}, results: {}", objectId, JSON.toJSONString(results));

        List<String> tagId = Lists.newArrayList();
        List<String> reason = Lists.newArrayList();

        results.stream()
                .filter(DirectTaggingResultModel::isMatch)
                .forEach(result -> {
                    tagId.add(nameToId.get(result.getTagName()));
                    reason.add(result.getSource());
                });

        if (CollectionUtils.isNotEmpty(tagId)) {
            ParagraphDocument paragraphDocument = createParagraphDocument(user, activeRecordData, type, tagId, reason);
            documentProcessService.saveParagraphDocuments(Lists.newArrayList(paragraphDocument), user);
        }
    }

    private ParagraphDocument createParagraphDocument(
            User user,
            IObjectData activeRecordData,
            String type,
            List<String> tags,
            List<String> reason) {

        ParagraphDocument paragraphDocument = new ParagraphDocument();
        // 手动生成并设置ID
        paragraphDocument.setId(new ObjectId());
        // 设置基本信息
        paragraphDocument.setTenantId(user.getTenantId());

        // 设置对象相关信息
        paragraphDocument.setObjectApiName(activeRecordData.getDescribeApiName());
        paragraphDocument.setObjectId(activeRecordData.getId());
        // 设置序号和时间信息
        paragraphDocument.setSeqNo(0);
        // 设置其他标记
        paragraphDocument.setIsDeleted(false);
        // 设置关联ID
        paragraphDocument.setAccountId(activeRecordData.get(CommonConstant.ACCOUNT_ID, String.class));
        paragraphDocument.setLeadsId(activeRecordData.get(CommonConstant.LEADS_ID, String.class));
        paragraphDocument.setNewOpportunityId(activeRecordData.get(CommonConstant.NEW_OPPORTUNITY_ID, String.class));
        paragraphDocument.setTags(tags);
        paragraphDocument.setReason(JSON.toJSONString(reason));
        // 设置时间戳
        long currentTime = System.currentTimeMillis();
        paragraphDocument.setCreateTime(currentTime);
        paragraphDocument.setLastUpdateTime(currentTime);
        paragraphDocument.setType(type);
        return paragraphDocument;
    }

    /**
     * 去重处理结果
     */
    private List<DirectTaggingResultModel> deduplicateResults(List<DirectTaggingResultModel> allResults) {
        if (CollectionUtils.isEmpty(allResults)) {
            return Lists.newArrayList();
        }

        Map<String, DirectTaggingResultModel> uniqueResults = Maps.newLinkedHashMap();
        for (DirectTaggingResultModel result : allResults) {
            if (result.isMatch()) {
                // 如果已存在相同标签名的结果，保留第一个
                uniqueResults.putIfAbsent(result.getTagName(), result);
            }
        }

        return Lists.newArrayList(uniqueResults.values());
    }

    /**
     * 处理focus标签（按5个一组并发执行）
     * 
     * @param user 用户信息
     * @param contentSegment 内容段落
     * @param focusTags focus标签列表
     * @param nameToIdMap 标签名到ID的映射
     * @param objectId 对象ID
     * @return 处理结果列表
     */
    private List<DirectTaggingResultModel> processFocusTags(User user, String contentSegment, 
                                                           List<TagRuleData> focusTags, 
                                                           Map<String, String> nameToIdMap, 
                                                           String objectId) {
        List<DirectTaggingResultModel> focusResults = Lists.newArrayList();
        
        if (CollectionUtils.isEmpty(focusTags)) {
            return focusResults;
        }
        
        // 按5个一组分批处理
        List<List<TagRuleData>> focusTagBatches = Lists.partition(focusTags, 5);
        log.info("[DirectTaggingService] 开始分批并发处理focus标签, objectId: {}, focus标签总数: {}, 分批数: {}", 
                objectId, focusTags.size(), focusTagBatches.size());
        
        for (int batchIndex = 0; batchIndex < focusTagBatches.size(); batchIndex++) {
            List<TagRuleData> batch = focusTagBatches.get(batchIndex);
            log.info("[DirectTaggingService] 开始处理第{}批focus标签, objectId: {}, 本批标签数: {}", 
                    batchIndex + 1, objectId, batch.size());
            
            // 并发处理当前批次的标签
            List<DirectTaggingResultModel> batchResults = processFocusTagsBatch(user, contentSegment, batch, nameToIdMap, objectId, batchIndex + 1);
            focusResults.addAll(batchResults);
        }
        
        log.info("[DirectTaggingService] 所有focus标签批次处理完成, objectId: {}, 总匹配结果数: {}", objectId, focusResults.size());
        return focusResults;
    }

    /**
     * 并发处理一批focus标签
     * 
     * @param user 用户信息
     * @param contentSegment 内容段落
     * @param focusTagBatch 一批focus标签
     * @param nameToIdMap 标签名到ID的映射
     * @param objectId 对象ID
     * @param batchNumber 批次号
     * @return 处理结果列表
     */
    private List<DirectTaggingResultModel> processFocusTagsBatch(User user, String contentSegment, 
                                                                List<TagRuleData> focusTagBatch, 
                                                                Map<String, String> nameToIdMap, 
                                                                String objectId, 
                                                                int batchNumber) {
        try {
            // 创建并发任务
            List<CompletableFuture<List<DirectTaggingResultModel>>> futures = createConcurrentTasks(
                    user, contentSegment, focusTagBatch, nameToIdMap, objectId, batchNumber);
            
            // 收集并发结果
            return collectConcurrentResults(futures, objectId, batchNumber);
            
        } catch (Exception e) {
            // 异常降级处理
            return fallbackSyncProcess(user, contentSegment, focusTagBatch, nameToIdMap, objectId, batchNumber);
        }
    }

    /**
     * 处理普通标签（批量运行）
     * 
     * @param user 用户信息
     * @param contentSegment 内容段落
     * @param normalTags 普通标签列表
     * @param nameToIdMap 标签名到ID的映射
     * @param objectId 对象ID
     * @return 处理结果列表
     */
    private List<DirectTaggingResultModel> processNormalTags(User user, String contentSegment, 
                                                            List<TagRuleData> normalTags, 
                                                            Map<String, String> nameToIdMap, 
                                                            String objectId) {
        List<DirectTaggingResultModel> normalResults = Lists.newArrayList();
        
        if (CollectionUtils.isEmpty(normalTags)) {
            return normalResults;
        }
        
        String normalTagsString = buildTagsString(normalTags);

        List<DirectTaggingResultModel> results = multipleMatchEnabled
                ? processWithMultipleMatch(user, contentSegment, normalTagsString, normalTags, nameToIdMap, objectId)
                : processWithSingleMatch(user, contentSegment, normalTagsString, normalTags, nameToIdMap, objectId);
        
        if (CollectionUtils.isNotEmpty(results)) {
            normalResults.addAll(results);
            log.info("[DirectTaggingService] 普通标签处理完成, objectId: {}, 匹配结果数: {}", objectId, results.size());
        }
        
        return normalResults;
    }

    /**
     * 处理单个focus标签
     * 
     * @param user 用户信息
     * @param contentSegment 内容段落
     * @param focusTag focus标签
     * @param nameToIdMap 标签名到ID的映射
     * @param objectId 对象ID
     * @param batchNumber 批次号
     * @return 处理结果列表
     */
    private List<DirectTaggingResultModel> processSingleFocusTag(User user, String contentSegment, 
                                                                TagRuleData focusTag, 
                                                                Map<String, String> nameToIdMap, 
                                                                String objectId, 
                                                                int batchNumber) {
        // 为每个focus标签创建单独的列表
        List<TagRuleData> singleFocusTagList = Lists.newArrayList(focusTag);
        String singleFocusTagString = buildTagsString(singleFocusTagList);

        List<DirectTaggingResultModel> singleFocusResults = multipleMatchEnabled
                ? processWithMultipleMatch(user, contentSegment, singleFocusTagString, singleFocusTagList, nameToIdMap, objectId)
                : processWithSingleMatch(user, contentSegment, singleFocusTagString, singleFocusTagList, nameToIdMap, objectId);
        
        if (CollectionUtils.isNotEmpty(singleFocusResults)) {
            log.info("[DirectTaggingService] focus标签处理完成, objectId: {}, 批次: {}, 标签名: {}, 匹配结果数: {}", 
                    objectId, batchNumber, focusTag.getName(), singleFocusResults.size());
        }
        
        return singleFocusResults != null ? singleFocusResults : Lists.newArrayList();
    }

    /**
     * 创建并发任务列表
     * 
     * @param user 用户信息
     * @param contentSegment 内容段落
     * @param focusTagBatch 一批focus标签
     * @param nameToIdMap 标签名到ID的映射
     * @param objectId 对象ID
     * @param batchNumber 批次号
     * @return 并发任务列表
     */
    private List<CompletableFuture<List<DirectTaggingResultModel>>> createConcurrentTasks(User user, String contentSegment, 
                                                                                          List<TagRuleData> focusTagBatch, 
                                                                                          Map<String, String> nameToIdMap, 
                                                                                          String objectId, 
                                                                                          int batchNumber) {
        List<CompletableFuture<List<DirectTaggingResultModel>>> futures = Lists.newArrayList();
        
        for (TagRuleData focusTag : focusTagBatch) {
            CompletableFuture<List<DirectTaggingResultModel>> future = CompletableFuture
                    .supplyAsync(() -> processSingleFocusTag(user, contentSegment, focusTag, nameToIdMap, objectId, batchNumber), 
                                executorService);
            futures.add(future);
        }
        
        return futures;
    }

    /**
     * 收集并发任务的结果
     * 
     * @param futures 并发任务列表
     * @param objectId 对象ID
     * @param batchNumber 批次号
     * @return 处理结果列表
     * @throws Exception 处理异常时抛出
     */
    private List<DirectTaggingResultModel> collectConcurrentResults(List<CompletableFuture<List<DirectTaggingResultModel>>> futures, 
                                                                   String objectId, 
                                                                   int batchNumber) throws Exception {
        List<DirectTaggingResultModel> batchResults = Lists.newArrayList();
        
        // 等待所有任务完成，设置60秒超时
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.get(60, TimeUnit.SECONDS);
        
        // 收集所有结果
        for (CompletableFuture<List<DirectTaggingResultModel>> future : futures) {
            List<DirectTaggingResultModel> results = future.join();
            if (CollectionUtils.isNotEmpty(results)) {
                batchResults.addAll(results);
            }
        }
        
        log.info("[DirectTaggingService] 第{}批focus标签并发处理完成, objectId: {}, 本批匹配结果数: {}", 
                batchNumber, objectId, batchResults.size());
        
        return batchResults;
    }

    /**
     * 异常降级处理（同步处理标签）
     * 
     * @param user 用户信息
     * @param contentSegment 内容段落
     * @param focusTagBatch 一批focus标签
     * @param nameToIdMap 标签名到ID的映射
     * @param objectId 对象ID
     * @param batchNumber 批次号
     * @return 处理结果列表
     */
    private List<DirectTaggingResultModel> fallbackSyncProcess(User user, String contentSegment, 
                                                              List<TagRuleData> focusTagBatch, 
                                                              Map<String, String> nameToIdMap, 
                                                              String objectId, 
                                                              int batchNumber) {
        List<DirectTaggingResultModel> batchResults = Lists.newArrayList();
        
        log.info("[DirectTaggingService] 第{}批focus标签降级到同步处理, objectId: {}, 标签数: {}", 
                batchNumber, objectId, focusTagBatch.size());
        
        for (TagRuleData focusTag : focusTagBatch) {
            List<DirectTaggingResultModel> singleFocusResults = processSingleFocusTag(user, contentSegment, focusTag, nameToIdMap, objectId, batchNumber);
            if (CollectionUtils.isNotEmpty(singleFocusResults)) {
                batchResults.addAll(singleFocusResults);
            }
        }
        
        return batchResults;
    }
}