package com.facishare.crm.task.sfa.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import org.codehaus.jackson.annotate.JsonProperty;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/09/18
 */
public interface RecordRestModel {
    @Data
    @Builder
    class AssignRecordArg {
        @JsonProperty("describeApiName")
        @SerializedName("describeApiName")
        private String describeApiName;

        @JsonProperty("role_list")
        @SerializedName("role_list")
        private List<RecordRole> roleList;
    }

    @Data
    @Builder
    class RecordRole {
        @JsonProperty("default_record")
        @SerializedName("default_record")
        private String defaultRecord;

        @JsonProperty("roleCode")
        @SerializedName("roleCode")
        private String roleCode;

        @JsonProperty("records")
        @SerializedName("records")
        private List<String> records;
    }

    @Data
    @Builder
    class SaveLayoutAssignArg {
        @JsonProperty("describeApiName")
        @SerializedName("describeApiName")
        private String describeApiName;

        @JsonProperty("layoutType")
        @SerializedName("layoutType")
        private String layoutType;

        @JsonProperty("role_list")
        @SerializedName("role_list")
        private List<RecordLayoutRole> roleList;
    }

    @Data
    @Builder
    class RecordLayoutRole {
        @JsonProperty("label")
        @SerializedName("label")
        private String label;

        @JsonProperty("roleCode")
        @SerializedName("roleCode")
        private String roleCode;

        @JsonProperty("record_layout")
        @SerializedName("record_layout")
        private List<RecordLayout> recordLayout;
    }

    @Data
    @Builder
    class RecordLayout {
        @JsonProperty("layout_api_name")
        @SerializedName("layout_api_name")
        private String layoutApiName;

        @JsonProperty("record_api_name")
        @SerializedName("record_api_name")
        private String recordApiName;
    }
}
