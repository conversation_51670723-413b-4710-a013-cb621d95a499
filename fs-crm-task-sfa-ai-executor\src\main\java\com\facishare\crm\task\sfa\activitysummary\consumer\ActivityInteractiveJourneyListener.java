package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityInteractiveJourneyService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ActivityInteractiveJourneyListener extends AbstractActivityCommonListener {
    private ActivityInteractiveJourneyService activityInteractiveJourneyService;

    @Override
    String getSection() {
        return "sfa_activity_interactive_journey";
    }

    @Override
    void consume(ActivityMessage activityMessage) {
        String stage = activityMessage.getStage();
        if ("realtime2text".equals(stage)) {
            return;
        }
        activityInteractiveJourneyService.consume(activityMessage);
    }
}
