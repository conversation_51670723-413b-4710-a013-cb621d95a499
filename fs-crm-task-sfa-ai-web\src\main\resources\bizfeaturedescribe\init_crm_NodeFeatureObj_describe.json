{"fields": {"node_id": {"action_on_target_delete": "cascade_delete", "api_name": "node_id", "define_type": "package", "description": "方法论节点", "is_index": true, "is_active": true, "is_unique": false, "label": "节点", "is_required": true, "type": "object_reference", "target_api_name": "MethodologyNodeObj", "target_related_list_name": "node_feature_node_list", "target_related_list_label": "节点特征关系", "wheres": []}, "feature_id": {"action_on_target_delete": "cascade_delete", "api_name": "feature_id", "define_type": "package", "description": "特征", "is_index": true, "is_active": true, "is_unique": false, "label": "特征", "is_required": true, "type": "object_reference", "target_api_name": "FeatureObj", "target_related_list_name": "node_feature_list", "target_related_list_label": "节点特征关系", "wheres": []}, "must_do": {"api_name": "must_do", "define_type": "package", "description": "是否必做", "is_index": true, "is_active": true, "is_unique": false, "label": "是否必做", "is_required": false, "type": "true_or_false", "default_value": false}, "status": {"api_name": "status", "define_type": "package", "description": "状态", "is_index": true, "is_active": true, "is_unique": false, "label": "状态", "is_required": false, "type": "select_one", "options": [{"label": "禁用", "value": "0"}, {"label": "启用", "value": "1"}], "default_value": "0"}, "methodology_id": {"action_on_target_delete": "cascade_delete", "api_name": "methodology_id", "define_type": "package", "description": "方法论", "is_index": true, "is_active": true, "is_unique": false, "label": "方法论", "is_required": true, "type": "object_reference", "target_api_name": "MethodologyObj", "target_related_list_name": "node_feature_methodology_list", "target_related_list_label": "节点特征方法论关系", "wheres": []}, "feature_object_api_name": {"api_name": "feature_object_api_name", "define_type": "package", "description": "特征对象", "is_index": true, "is_active": true, "is_unique": false, "label": "特征对象", "is_required": true, "type": "text", "max_length": 200}, "related_object_api_name": {"api_name": "related_object_api_name", "define_type": "package", "description": "关联对象", "is_index": true, "is_active": true, "is_unique": false, "label": "关联对象", "is_required": true, "type": "text", "max_length": 200}, "related_field": {"api_name": "related_field", "define_type": "package", "description": "关联字段", "is_index": true, "is_active": true, "is_unique": false, "label": "关联字段", "is_required": false, "type": "text", "max_length": 200}, "system_type": {"api_name": "system_type", "label": "数据系统类型", "type": "select_one", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "options": [{"label": "系统", "value": "system"}, {"label": "自定义", "value": "udef"}], "define_type": "system", "status": "released"}, "owner": {"is_index": true, "is_active": true, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "released", "is_extend": false}, "lock_status": {"is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "help_text": "锁定状态", "status": "released", "is_extend": false}, "lock_rule": {"is_index": true, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "help_text": "锁定规则", "is_extend": false}, "lock_user": {"is_index": true, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "help_text": "加锁人", "is_extend": false}, "life_status": {"is_index": true, "is_active": true, "description": "生命状态", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "help_text": "生命状态", "status": "released", "is_extend": false}, "life_status_before_invalid": {"is_index": true, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "help_text": "作废前生命状态", "max_length": 256, "is_extend": false}, "owner_department": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "负责人所在部门", "type": "text", "default_to_zero": false, "is_need_convert": false, "is_required": false, "api_name": "owner_department", "define_type": "package", "is_single": true, "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "relevant_team": {"embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "help_text": "相关团队", "is_extend": false}, "record_type": {"is_index": false, "is_active": true, "description": "业务类型", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "help_text": "", "status": "released", "is_extend": false}, "created_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "创建人", "api_name": "created_by", "description": "创建人", "status": "released", "is_extend": false}, "last_modified_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "最后修改人", "api_name": "last_modified_by", "description": "最后修改人", "status": "released", "is_extend": false}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "api_name": "package", "description": "package", "status": "released", "is_extend": false}, "object_describe_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_id", "api_name": "object_describe_id", "description": "object_describe_id", "status": "released", "is_extend": false}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "is_extend": false}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released", "is_extend": false}, "create_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "创建时间", "status": "released", "is_extend": false}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "最后修改时间", "status": "released", "is_extend": false}, "extend_obj_data_id": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "default_value": "", "type": "text", "label": "extend_obj_data_id", "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}}, "validate_rules": {}, "triggers": {}, "actions": {}, "index_version": 1, "api_name": "NodeFeatureObj", "display_name": "节点特征关联", "package": "CRM", "define_type": "package", "is_active": true, "store_table_name": "biz_node_feature", "is_deleted": false}