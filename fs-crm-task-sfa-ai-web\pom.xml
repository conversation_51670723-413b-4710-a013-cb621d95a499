<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-crm-task-sfa-ai</artifactId>
        <groupId>com.facishare</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-crm-task-sfa-ai-web</artifactId>
    <packaging>war</packaging>
    <name>fs-crm-task-sfa-ai-web</name>


    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <!--日志级别动态调整-->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>logconfig-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-task-sfa-ai-executor</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
        </dependency>

        <!--  注意：foneshare版本（4.7.1），112版本（4.9.5）如果发版不成功，可以考虑换成4.7.1 -->
<!--        <dependency>-->
<!--            <groupId>org.apache.rocketmq</groupId>-->
<!--            <artifactId>rocketmq-common</artifactId>-->
<!--            <version>4.9.5</version>-->
<!--    </dependency>-->

  <!--        <dependency>-->
<!--            <groupId>com.fxiaoke.notifier</groupId>-->
<!--            <artifactId>notifier-support</artifactId>-->
<!--            <version>1.1.0-SNAPSHOT</version>-->
<!--        </dependency>-->
    </dependencies>
</project>
