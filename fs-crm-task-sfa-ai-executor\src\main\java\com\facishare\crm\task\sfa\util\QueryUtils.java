package com.facishare.crm.task.sfa.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;

@Slf4j
public class QueryUtils {
    private static final ServiceFacade metaDataService = SpringUtil.getContext().getBean(ServiceFacade.class);

    /**
     * 保证查询searchQuery所有数据，忽略所有字段权限
     * 每次查询出来的limit数据，
     * 执行consumer的入参
     *
     * @param searchQuery searchQuery
     * @param user        user
     * @param apiName     apiName
     * @param consumer    执行结果的消费者
     */
    public static void templateQueryBatchIgnoreAll(SearchTemplateQuery searchQuery, User user, String apiName, List<String> fields, boolean allowRunning, BiConsumer<List<IObjectData>,Integer> consumer) {
        if (searchQuery.getLimit() <= 0) {
            throw new IllegalArgumentException("Limit must be greater than 0");
        }
        int offset;
        int limit = searchQuery.getLimit();
        for (int i = 0; i < 10000; i++) {
            if (!allowRunning) {
                log.info("Operation stopped by allowRunning check");
                break;
            }
            offset = i * limit;
            searchQuery.setOffset(offset);
            log.info("templateQueryBatchIgnoreAll, apiName: {}, offset: {}, limit: {}", apiName, offset, limit);
            try {
                QueryResult<IObjectData> queryResult = metaDataService.findBySearchQueryWithFieldsIgnoreAll(user, apiName, searchQuery, fields);
                List<IObjectData> detailDataList = Optional.ofNullable(queryResult).map(QueryResult::getData).orElse(Lists.newArrayList());
                if (null == queryResult || CollectionUtils.empty(detailDataList)) {
                    break;
                }
                consumer.accept(detailDataList,i);
                if (queryResult.getData().size() < limit) {
                    break;
                }
            } catch (Exception e) {
                log.error("Error in templateQueryBatchIgnoreAll, apiName: {}, offset: {}, limit: {}", apiName, offset, limit, e);
            }
        }
    }

}
