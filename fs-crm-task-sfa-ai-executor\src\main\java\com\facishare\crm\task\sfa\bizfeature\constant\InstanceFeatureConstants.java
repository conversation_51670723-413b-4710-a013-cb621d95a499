package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 实例特征关系常量
 *
 * <AUTHOR>
 */
public interface InstanceFeatureConstants {
	/**
     * 任务名称
     */
	String TASK_ID = "task_id";
	/**
     * 任务实例
     */
	String TASK_INSTANCE_ID = "task_instance_id";
	/**
     * 节点实例
     */
	String NODE_INSTANCE_ID = "node_instance_id";
	/**
     * 节点
     */
	String NODE_ID = "node_id";
	/**
     * 方法论
     */
	String METHODOLOGY_ID = "methodology_id";
	/**
     * 方法论实例
     */
	String METHODOLOGY_INSTANCE_ID = "methodology_instance_id";
	/**
     * 特征
     */
	String FEATURE_ID = "feature_id";
	/**
     * 特征分
     */
	String FEATURE_SCORE_ID = "feature_score_id";
     /**
     * 特征对象
     */
	String FEATURE_OBJECT_API_NAME = "feature_object_api_name";
	/**
     * 关联对象
     */
	String RELATED_OBJECT_API_NAME = "related_object_api_name";
	/**
     * 关联字段
     */
	String RELATED_FIELD = "related_field";
	/**
     * 负责人所在部门
     */
	String OWNER_DEPARTMENT = "owner_department";
	/**
     * 相关团队
     */
	String RELEVANT_TEAM = "relevant_team";
	/**
	 * node_id的parent
	 */
	String PARENT_ID = "parent_id";
}