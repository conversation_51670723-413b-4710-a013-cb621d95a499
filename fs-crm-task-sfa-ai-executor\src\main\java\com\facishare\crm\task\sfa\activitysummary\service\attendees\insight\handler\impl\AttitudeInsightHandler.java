package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.activity.enums.AttendeesInsightType;
import com.facishare.crm.task.sfa.activitysummary.constant.AttendeesInsightConstants;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel.AttendeesInsightMessage;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class AttitudeInsightHandler extends AbstractInsightHandler<AttendeesInsightModel.AttitudeInsightResult> {

    private static final String PROMPT = "prompt_attendees_insight_attitude";


    @Override
    public String getInsightType() {
        return AttendeesInsightType.ATTITUDE;
    }


    @Override
    public void doInsight(AttendeesInsightMessage attendeesInsightMessage) {

        List<IObjectData> dataList = super.insightByOriginCorpus(attendeesInsightMessage, PROMPT);

        List<IObjectData> activityUserList = attendeesInsightMessage.getExtendData().getActivityUserList();
        String tenantId = attendeesInsightMessage.getTenantId();
        Map<String, IObjectData> userMap = activityUserList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        List<IObjectData> updateActivityUserList = Lists.newArrayList();
        for (IObjectData data : dataList) {
            String activityUserId = data.get(AttendeesInsightConstants.ACTIVITY_USER_ID, String.class);
            IObjectData activityUser = userMap.get(activityUserId);
            if (activityUser != null) {
                String attitude = data.get(AttendeesInsightConstants.ATTITUDE, String.class);
                activityUser.set("attitude", attitude);
                updateActivityUserList.add(activityUser);
            }
        }

        if (!updateActivityUserList.isEmpty()) {
            serviceFacade.batchUpdateByFields(User.systemUser(tenantId), updateActivityUserList, Lists.newArrayList("attitude"));
        }
    }

    @Override
    protected Class<AttendeesInsightModel.AttitudeInsightResult> getInsightResultClass() {
        return AttendeesInsightModel.AttitudeInsightResult.class;
    }

    @Override
    protected void fillDataByOriginRst(IObjectData insightRecord, AttendeesInsightModel.AttitudeInsightResult insightResult) {
        insightRecord.set(AttendeesInsightConstants.ATTITUDE, insightResult.getAttitude());
        insightRecord.set(AttendeesInsightConstants.INSIGHT_RESULT, JSON.toJSONString(insightResult.getInsightList()));
    }

    @Override
    protected String getUserNames(AttendeesInsightMessage insightMessage) {
        return super.getTheirSideNames(insightMessage);
    }
}
