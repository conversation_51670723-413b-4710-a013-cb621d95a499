package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 特征常量
 *
 * <AUTHOR>
 */
public interface FeatureConstants {
    String CONFIG_KEY = "#config#";
    /**
     * 多个对象字段，以这个开头，中间用逗号分隔
     */
    String FIELDS = "#fields#";
    String LEADS_OBJ = "LeadsObj";
    String ACCOUNT_OBJ = "AccountObj";
    String NEW_OPPORTUNITY_OBJ = "NewOpportunityObj";

    String FEATURE_DIMENSION = "FeatureDimensionObj";
    String FEATURE = "FeatureObj";
    String FEATURE_SCORE_HISTORY = "FeatureScoreHistoryObj";
    String FEATURE_SCORE = "FeatureScoreObj";
    String FEATURE_VALUE_HISTORY = "FeatureValueHistoryObj";
    String FEATURE_VALUE = "FeatureValueObj";
    String KNOWLEDGE_CLASS = "KnowledgeClassObj";
    String KNOWLEDGE_DOCUMENT = "KnowledgeDocumentObj";
    String METHODOLOGY = "MethodologyObj";
    String METHODOLOGY_RULE = "MethodologyRuleObj";
    String METHODOLOGY_INSTANCE = "MethodologyInstanceObj";
    String NODE_INSTANCE = "NodeInstanceObj";
    String OBJECT_FEATURE = "ObjectFeatureObj";
    String OBJECT_METHODOLOGY = "ObjectMethodologyObj";
    String PARSE_RULE = "ParseRuleObj";
    String SCORING_RULE = "ScoringRuleObj";
    String TASK_FEATURE = "TaskFeatureObj";
    String TASK_INSTANCE = "TaskInstanceObj";
    String TASK_KNOWLEDGE = "TaskKnowledgeObj";
    String METHODOLOGY_NODE = "MethodologyNodeObj";
    String METHODOLOGY_TASK = "MethodologyTaskObj";
    String FEATURE_WEIGHT = "FeatureWeightObj";
    String PROFILE = "ProfileObj";
    String PROFILE_ITEM_SCORE = "ProfileItemScoreObj";
    String PROFILE_PROS_CONS = "ProfileProsConsObj";
    String PROFILE_ADVICE = "ProfileAdviceObj";
    String FEATURE_SCORE_RULE = "FeatureScoreRuleObj";
    String SALES_COACH_RECORD = "SalesCoachRecordObj";
    String NODE_FEATURE = "NodeFeatureObj";
    String NODE_TASK = "NodeTaskObj";
    String INSTANCE_FEATURE = "InstanceFeatureObj";
    /**
     * 警告信息
     */
    String WARNING_MESSAGE = "warning_message";
    /**
     * 特征主对象
     */
    String MASTER_OBJECT_API_NAME = "master_object_api_name";
    /**
     * 特征主对象字段
     */
    String MASTER_FIELD_API_NAME = "master_field_api_name";
    /**
     * 对比字段
     */
    String COMPARISON_FIELD = "comparison_field";
    /**
     * 开关特征
     */
    String SWITCH_FEATURE = "switch_feature";
    /**
     * 节点开关特征
     */
    String NODE_SWITCH_FEATURE = "node_switch_feature";
    /**
     * 数据源对象
     */
    String DATA_SOURCE_OBJECT = "data_source_object";
    /**
     * 数据源字段
     */
    String DATA_SOURCE_FIELD = "data_source_field";
    /**
     * 数据源外部
     */
    String DATA_SOURCE_THIRD = "data_source_third";
    /**
     * 数据源类型
     */
    String DATA_SOURCE_TYPE = "data_source_type";

    enum DataSourceType {
        /**
         * 内部
         */
        INTERNAL("internal"),
        /**
         * 外部
         */
        EXTERNAL("external"),
        /**
         * Activity聚合
         */
        ACTIVITY_AGG("activity_agg"),
        /**
         * 原文标注
         */
        TAG("tag");

        private final String dataSourceType;

        public String getDataSourceType() {
            return dataSourceType;
        }

        DataSourceType(String dataSourceType) {
            this.dataSourceType = dataSourceType;
        }
    }

    /**
     * 更新方式
     */
    String UPDATE_TYPE = "update_type";

    enum UpdateType {
        /**
         * 实时
         */
        REAL_TIME("real-time"),
        /**
         * 定时
         */
        SCHEDULED("scheduled");

        private final String updateType;

        public String getUpdateType() {
            return updateType;
        }

        UpdateType(String updateType) {
            this.updateType = updateType;
        }
    }

    /**
     * 定时设置
     */
    String TIMER_INFO = "timer_info";
    /**
     * 触发方式
     */
    String TRIGGER_TYPE = "trigger_type";

    enum TriggerType {
        /**
         * 新建
         */
        ADD("add"),
        /**
         * 变更
         */
        EDIT("edit");

        private final String triggerType;

        public String getTriggerType() {
            return triggerType;
        }

        TriggerType(String triggerType) {
            this.triggerType = triggerType;
        }
    }

    /**
     * 状态
     */
    String STATUS = "status";

    /**
     * 解析规则
     */
    String RULE_ID = "rule_id";
    /**
     * 特征维度1
     */
    String FEATURE_DIMENSION_1 = "feature_dimension_1";
    /**
     * 特征维度2
     */
    String FEATURE_DIMENSION_2 = "feature_dimension_2";
    /**
     * 特征维度3
     */
    String FEATURE_DIMENSION_3 = "feature_dimension_3";

    /**
     * 系统数据类型
     */
    String SYSTEM_TYPE = "system_type";

    /**
     * 评分规则ID
     */
    String SCORING_RULE_ID = "scoring_rule_id";

    /**
     * 标注临时变量
     */
    String TAGS_TEMP = "tags_temp";
    /**
     * 解析扩展变量
     */
    String PARSE_EXT_DATA = "parse_ext_data";

    /**
     * 标注
     */
    String TAGS = "tags";

    enum SystemType {
        /**
         * 系统
         */
        SYSTEM("system"),
        /**
         * 自定义
         */
        UDEF("udef");

        private final String systemType;

        public String getSystemType() {
            return systemType;
        }

        SystemType(String systemType) {
            this.systemType = systemType;
        }
    }

    enum StatusType {
        /**
         * 禁用
         */
        DISABLED("0"),
        /**
         * 启用
         */
        ENABLED("1"),
        /**
         * 维护中
         */
        MAINTENANCE("2");

        private final String status;

        public String getStatusType() {
            return status;
        }

        StatusType(String status) {
            this.status = status;
        }
    }

    /**
     * 默认评分
     */
    String DEFAULT_SCORE = "default_score";
}