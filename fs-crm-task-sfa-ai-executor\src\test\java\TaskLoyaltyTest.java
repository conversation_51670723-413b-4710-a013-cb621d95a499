import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.Assert;
import org.junit.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;

public class TaskLoyaltyTest {

    static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Test
    public void formulaEvaluationDate() {
        IObjectData tierClassInfo = ObjectDataExt.of(new HashMap<>());
        tierClassInfo.set(LoyaltyConstants.LoyaltyTierClass.LEVEL_MODE, "FixedDateModel1");
        tierClassInfo.set(LoyaltyConstants.LoyaltyTierClass.ASSESSMENT_FREQUENCY, "Annual");
        tierClassInfo.set(LoyaltyConstants.LoyaltyTierClass.LEVEL_START_MONTH, 7);
        tierClassInfo.set(LoyaltyConstants.LoyaltyTierClass.LEVEL_START_DAY, 7);
        IObjectData member = ObjectDataExt.of(new HashMap<>());
        member.set(LoyaltyConstants.LoyaltyMember.REGISTRATION_TIME, 1688705999999L);
        System.out.println(LoyaltyUtils.evaluationDate(tierClassInfo, member));
    }

    @Test
    public void formulaEffectiveTime() throws ParseException {
        IObjectData pointType = new ObjectData();
        pointType.set("freeze_period_days", 3L);
        pointType.set("activation_method", "Immediately");
        Assert.assertEquals(LoyaltyUtils.formulaEffectiveTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2024-04-15 12:34:56").getTime());
        pointType.set("activation_method", "NextWeek");
        Assert.assertEquals(LoyaltyUtils.formulaEffectiveTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2024-04-22 00:00:00").getTime());
        pointType.set("activation_method", "NextMonth");
        Assert.assertEquals(LoyaltyUtils.formulaEffectiveTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2024-05-01 00:00:00").getTime());
        pointType.set("activation_method", "NextQuarter");
        Assert.assertEquals(LoyaltyUtils.formulaEffectiveTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2024-07-01 00:00:00").getTime());
        pointType.set("activation_method", "NextYear");
        Assert.assertEquals(LoyaltyUtils.formulaEffectiveTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2025-01-01 00:00:00").getTime());
        pointType.set("activation_method", "AfterFreezePeriod");
        Assert.assertEquals(LoyaltyUtils.formulaEffectiveTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2024-04-18 00:00:00").getTime());
    }

    @Test
    public void formulaExpiryTime() throws ParseException {
        IObjectData pointType = new ObjectData();
        pointType.set("expiry_method", "FixedDate");
        pointType.set("expiry_date", 123L);
        Assert.assertEquals(LoyaltyUtils.formulaExpiryTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), 123L);
        pointType.set("expiry_method", "Period");
        pointType.set("validity_period_length", 1);
        pointType.set("validity_period_unit", "Day");
        Assert.assertEquals(LoyaltyUtils.formulaExpiryTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2024-04-16 23:59:59").getTime());
        pointType.set("validity_period_unit", "Week");
        Assert.assertEquals(LoyaltyUtils.formulaExpiryTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2024-04-22 23:59:59").getTime());
        pointType.set("validity_period_unit", "Month");
        Assert.assertEquals(LoyaltyUtils.formulaExpiryTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2024-05-15 23:59:59").getTime());
        pointType.set("validity_period_unit", "Quarter");
        Assert.assertEquals(LoyaltyUtils.formulaExpiryTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2024-07-14 23:59:59").getTime());
        pointType.set("validity_period_unit", "Year");
        Assert.assertEquals(LoyaltyUtils.formulaExpiryTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2025-04-15 23:59:59").getTime());
        pointType.set("validity_period_unit", "Unlimited");
        Assert.assertEquals(LoyaltyUtils.formulaExpiryTime(sdf.parse("2024-04-15 12:34:56").getTime(), pointType).longValue(), sdf.parse("2124-04-15 23:59:59").getTime());
    }
}
