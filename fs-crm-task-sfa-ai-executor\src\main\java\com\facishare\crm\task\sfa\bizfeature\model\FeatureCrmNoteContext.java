package com.facishare.crm.task.sfa.bizfeature.model;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@Builder
public class FeatureCrmNoteContext implements Serializable {
    private String tenantId;

    private List<Integer> receiverIds;

    private String apiName;

    private String objectId;

    private String refreshType;

    private boolean isSucess;

    private String sendMessage = Strings.EMPTY;

    public void setSendMessage(String message) {
        if (Strings.isNotEmpty(message)) {
            sendMessage = Strings.isEmpty(sendMessage) ? message : sendMessage + "；" + message;
        }
    }

}
