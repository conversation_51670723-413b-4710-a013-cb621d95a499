package com.facishare.crm.task.sfa.bizfeature.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * MQ触发特征计算参数
 * 
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface ProfileAdviceMqModel {
    /**
     * 特征计算消息生产者名称
     */
    String MQ_PRODUCER_NAME = "crm-feature-advice-producer";

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Message {
        /**
         * 企业id
         */
        String tenantId;

        String profileId;

        String refreshType;

        String receiverId;

        /**
         * 暂时不用
         */
        private String tags;
    }
}
