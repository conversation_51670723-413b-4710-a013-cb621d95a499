package com.facishare.crm.task.sfa.service.impl;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeListener;
import com.github.autoconf.api.IConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * 策略相关常量类
 */
@Slf4j
public class StrategyConfig {

    /**
     * 查询用户的分页数量
     */
    public static int STRATEGY_QUERY_ACCOUNT_LIMIT = 1000;

    /**
     * 批量处理阈值
     */
    public static int BATCH_LIMIT = 2000;

    /**
     * 历史话题排序字段默认值
     */
    public static int HISTORY_TOPIC_ORDER_VALUE = 99999;

    /**
     * 查询策略任务的前N天
     */
    public static int STRATEGY_TASK_BEFORE_DAY = 1;

    /**
     * 策略任务是否允许执行
     */
    public static boolean STRATEGY_ALLOW_RUNNING = true;


    static {
        IChangeListener listener = StrategyConfig::loadConfig;
        ConfigFactory.getConfig("fs-sfa-ai", listener);
    }

    /**
     * 加载配置
     *
     * @param config 配置对象
     */
    private static void loadConfig(IConfig config) {
        if (config == null) {
            log.warn("Strategy config is null, using default values");
            return;
        }

        try {
            // 查询用户的分页数量
            STRATEGY_QUERY_ACCOUNT_LIMIT = config.getInt("strategy_query_account_limit", 1000);
            log.info("Loaded STRATEGY_QUERY_ACCOUNT_LIMIT: {}", STRATEGY_QUERY_ACCOUNT_LIMIT);
            // 批量处理阈值
            BATCH_LIMIT = config.getInt("batch_limit", 2000);
            log.info("Loaded BATCH_LIMIT: {}", BATCH_LIMIT);

            // 保存建议话题的阈值
            BATCH_LIMIT = config.getInt("strategy_save_activity_question_limit", 2000);
            log.info("Loaded STRATEGY_SAVE_ACTIVITY_QUESTION_LIMIT: {}", BATCH_LIMIT);

            // 查询策略任务的前N天
            STRATEGY_TASK_BEFORE_DAY = config.getInt("strategy_task_before_day", 1);
            log.info("Loaded STRATEGY_TASK_BEFORE_DAY: {}", STRATEGY_TASK_BEFORE_DAY);

            // 策略任务是否允许执行
            STRATEGY_ALLOW_RUNNING = config.getBool("strategy_allow_running", true);
            log.info("Loaded STRATEGY_ALLOW_RUNNING: {}", STRATEGY_ALLOW_RUNNING);
        } catch (Exception e) {
            log.error("Error loading strategy config", e);
        }
    }


}