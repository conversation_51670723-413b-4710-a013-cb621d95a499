package com.facishare.crm.task.sfa.services.rebate.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface FindRuleCodesMatchedDataIds {
    @Data
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Arg extends BaseEngine.Arg {
        private List<String> ruleCodes;
        private List<DataDetail> dataList;

    }

    @Data
    @Builder
    class DataDetail {
        private String tenantId;
        private String apiName;
        private String dataId;
        private Map<String, Object> dataMap;
    }

    @Data
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Result extends BaseEngine.Result<Map<String, Set<String>>> {
    }
}
