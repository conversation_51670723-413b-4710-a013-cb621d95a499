package com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.task.sfa.activitysummary.constants.AIAgentConstants;
import com.facishare.crm.task.sfa.activitysummary.model.EvaluationModel;
import com.facishare.crm.task.sfa.activitysummary.model.EvaluationResponse;
import com.facishare.crm.task.sfa.activitysummary.model.SegmentModel;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Evaluator服务类
 * 负责评估分段结果质量
 */
@Service
@Slf4j
public class EvaluatorService {

    @Autowired
    private CompletionsService completionsService;

    /**
     * 运行Evaluator组件，评估分段结果质量
     * <p>
     * 提示词模板:
     * ```
     * 你是一个专业的文本分段评估专家，负责评估分段结果的质量并提供详细的改进建议。你的评估必须保持高度一致性和客观性。
     *
     * # 任务背景
     * 你正在评估一个会议内容的分段结果。这些分段将用于生成会议摘要，因此分段质量直接影响最终摘要的质量。
     *
     * # 评估材料
     * ## 会议内容:
     * ${sence_variables.custom_sence.meetingContent}
     *
     * ## 上次会议摘要:
     * ${sence_variables.custom_sence.lastSummary}
     *
     * ## 分段结果:
     * ${sence_variables.custom_sence.segments}
     *
     * ## 上次评估结果:
     * ${sence_variables.custom_sence.previousEvaluation}
     *
     * # 评估流程
     * 请严格按照以下步骤进行评估:
     * 1. 仔细阅读会议内容和分段结果
     * 2. 对照上次评估结果(如果有)，确保评估标准一致
     * 3. 分别评估四个维度的得分
     * 4. 计算总体评分
     * 5. 确定是否需要修正
     * 6. 提供具体的调整建议
     *
     * # 评估标准
     * ## 1. 段落内容的连贯性 (25分)
     * 评估每个段落内部的连贯性和主题一致性。
     *
     * 具体评分标准:
     * - 优秀(20-25分):
     *   * 每个段落都有明确的中心主题
     *   * 段落内部句子之间逻辑关系清晰
     *   * 没有明显的主题跳跃或逻辑断裂
     *   * 语义流畅，表达连贯
     *
     * - 良好(15-19分):
     *   * 大部分段落有明确的中心主题
     *   * 段落内部句子之间逻辑关系基本清晰
     *   * 偶有轻微的主题跳跃
     *   * 语义基本流畅
     *
     * - 一般(10-14分):
     *   * 部分段落缺乏明确的中心主题
     *   * 段落内部句子之间逻辑关系不够清晰
     *   * 存在明显的主题跳跃
     *   * 语义不够流畅
     *
     * - 较差(0-9分):
     *   * 大多数段落缺乏明确的中心主题
     *   * 段落内部句子之间逻辑关系混乱
     *   * 频繁的主题跳跃
     *   * 语义不连贯
     *
     * ## 2. 摘要的准确性和完整性 (25分)
     * 评估每个段落的摘要是否准确反映了段落的核心内容。
     *
     * 具体评分标准:
     * - 优秀(20-25分):
     *   * 摘要准确捕捉了段落的所有关键信息
     *   * 摘要与原文内容高度一致
     *   * 没有添加原文中不存在的信息
     *   * 没有遗漏重要信息
     *
     * - 良好(15-19分):
     *   * 摘要捕捉了段落的大部分关键信息
     *   * 摘要与原文内容基本一致
     *   * 极少添加原文中不存在的信息
     *   * 极少遗漏重要信息
     *
     * - 一般(10-14分):
     *   * 摘要只捕捉了段落的部分关键信息
     *   * 摘要与原文内容存在一定差异
     *   * 添加了一些原文中不存在的信息
     *   * 遗漏了一些重要信息
     *
     * - 较差(0-9分):
     *   * 摘要未能捕捉段落的大部分关键信息
     *   * 摘要与原文内容差异较大
     *   * 添加了大量原文中不存在的信息
     *   * 遗漏了大量重要信息
     *
     * ## 3. 分段数量的合理性 (25分)
     * 评估分段数量是否合理，段落长度是否均衡。
     *
     * 具体评分标准:
     * - 优秀(20-25分):
     *   * 分段数量与内容复杂度匹配
     *   * 段落长度分布均衡，没有过长或过短的段落
     *   * 每个段落包含一个完整的主题或子主题
     *   * 分段边界选择恰当，符合内容的自然分割点
     *
     * - 良好(15-19分):
     *   * 分段数量基本与内容复杂度匹配
     *   * 段落长度分布基本均衡，个别段落长度不均
     *   * 大部分段落包含一个完整的主题或子主题
     *   * 大部分分段边界选择恰当
     *
     * - 一般(10-14分):
     *   * 分段数量与内容复杂度不够匹配
     *   * 段落长度分布不均，存在多个过长或过短的段落
     *   * 部分段落包含多个主题或主题不完整
     *   * 部分分段边界选择不恰当
     *
     * - 较差(0-9分):
     *   * 分段数量与内容复杂度严重不匹配
     *   * 段落长度分布极不均衡
     *   * 大部分段落包含多个主题或主题不完整
     *   * 大部分分段边界选择不恰当
     *
     * ## 4. 段落之间的区分度 (25分)
     * 评估段落之间的主题区分度和过渡自然度。
     *
     * 具体评分标准:
     * - 优秀(20-25分):
     *   * 段落之间主题界限清晰，没有主题重叠
     *   * 段落之间的主题转换自然流畅
     *   * 相邻段落之间有明确的语义关联
     *   * 整体结构层次分明
     *
     * - 良好(15-19分):
     *   * 段落之间主题界限较为清晰，极少主题重叠
     *   * 段落之间的主题转换基本自然
     *   * 大部分相邻段落之间有语义关联
     *   * 整体结构基本清晰
     *
     * - 一般(10-14分):
     *   * 段落之间主题界限模糊，存在主题重叠
     *   * 段落之间的主题转换生硬
     *   * 部分相邻段落之间缺乏语义关联
     *   * 整体结构不够清晰
     *
     * - 较差(0-9分):
     *   * 段落之间主题界限极不清晰，大量主题重叠
     *   * 段落之间的主题转换极不自然
     *   * 大部分相邻段落之间缺乏语义关联
     *   * 整体结构混乱
     *
     * # 评分计算方法
     * 1. 每个维度的得分需要除以25转换为0-1之间的小数
     * 2. 总体评分为四个维度得分的算术平均值
     * 3. 如果总体评分低于0.7或任一维度得分低于0.6，则需要修正
     *
     * # 示例评估
     * 以下是一个评估示例，请参考这种评估方式:
     *
     * 段落内容的连贯性: 22分 (0.88)
     * - 每个段落都有明确的中心主题
     * - 段落内部句子之间逻辑关系清晰
     * - 语义流畅，表达连贯
     *
     * 摘要的准确性和完整性: 20分 (0.8)
     * - 摘要准确捕捉了段落的所有关键信息
     * - 摘要与原文内容高度一致
     * - 没有遗漏重要信息
     *
     * 分段数量的合理性: 18分 (0.72)
     * - 分段数量基本与内容复杂度匹配
     * - 段落长度分布基本均衡，个别段落长度不均
     * - 大部分分段边界选择恰当
     *
     * 段落之间的区分度: 21分 (0.84)
     * - 段落之间主题界限清晰，没有主题重叠
     * - 段落之间的主题转换自然流畅
     * - 整体结构层次分明
     *
     * 总体评分: (0.88 + 0.8 + 0.72 + 0.84) / 4 = 0.81
     *
     * # 调整建议要求
     * 请提供具体的调整建议，包括但不限于:
     * 1. 是否需要合并某些段落（明确指出哪些段落ID需要合并）
     * 2. 是否需要拆分某些段落（明确指出哪些段落ID需要拆分）
     * 3. 如何改进段落之间的过渡（提供具体的改进方法）
     * 4. 如何提高摘要质量（针对问题摘要提供具体建议）
     * 5. 建议的理想段落数量（给出一个具体数字）
     *
     * # 输出格式要求
     * 你必须严格按照以下JSON格式输出评估结果:
     * {
     * "evaluation": {
     *   "overall_score": 总体评分(0-1，由四个维度平均得出),
     *   "dimension_scores": {
     *     "coherence": 连贯性得分(0-1),
     *     "summary_quality": 摘要质量得分(0-1),
     *     "segmentation_count": 分段数量合理性得分(0-1),
     *     "distinction": 段落区分度得分(0-1)
     *   },
     *   "issues": ["问题1", "问题2", ...],
     *   "suggestions": ["建议1", "建议2", ...],
     *   "needs_revision": 是否需要修正(true/false),
     *   "adjustment_plan": {
     *     "merge_segments": [需要合并的段落ID数组],
     *     "split_segments": [需要拆分的段落ID数组],
     *     "ideal_segment_count": 理想段落数量,
     *     "focus_areas": ["需要重点关注的区域1", "需要重点关注的区域2", ...]
     *   }
     * }
     * }
     *
     * # 重要提示
     * 1. 如果提供了上次评估结果，请确保本次评估与上次评估保持一致的标准
     * 2. 评分时请参考具体的评分标准，避免主观判断
     * 3. 确保你的评估是可重复的，即对相同的输入应该产生相同的评估结果
     * 4. 在评估过程中，请详细解释你的评分理由，以确保评估的透明度和可靠性
     * ```
     */
    public EvaluationModel runEvaluator(User user, String meetContent, String previousSummary, List<SegmentModel> segments) {
        return runEvaluator(user, meetContent, previousSummary, segments, null);
    }

    /**
     * 运行Evaluator组件，评估分段结果质量，并传入上一次的评估结果作为参考
     *
     * @param user             用户信息
     * @param meetContent      会议内容
     * @param previousSummary  上一次摘要
     * @param segments         分段结果
     * @param previousEvaluation 上一次的评估结果，可以为null
     * @return 评估模型
     */
    public EvaluationModel runEvaluator(User user, String meetContent, String previousSummary,
                                        List<SegmentModel> segments, EvaluationModel previousEvaluation) {
        try {
            AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
            arg.setApiName(AIAgentConstants.API_NAME_EVALUATOR);

            // 创建custom_sence子对象
            Map<String, Object> customSence = Maps.newHashMap();
            customSence.put("meetingContent", meetContent);
            customSence.put("lastSummary", StringUtils.isBlank(previousSummary) ? "" : previousSummary);
            customSence.put("segments", JSON.toJSONString(segments));

            // 添加上一次的评估结果作为参考
            if (previousEvaluation != null) {
                customSence.put("previousEvaluation", JSON.toJSONString(previousEvaluation));
            } else {
                customSence.put("previousEvaluation", "");
            }

            // 直接将customSence赋值给arg.setSceneVariables()
            arg.setSceneVariables(customSence);

            // 使用requestCompletionData方法直接获取对象
            EvaluationResponse evaluationResponse = completionsService.requestCompletionData(
                    user, arg, AIAgentConstants.JSON_FORMAT_EVALUATION, EvaluationResponse.class);

            if (evaluationResponse == null) {
                return null;
            }

            return evaluationResponse.getEvaluation();
        } catch (Exception e) {
            log.error("Evaluator执行失败", e);
            return null;
        }
    }
}
