package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.producer.ActivityRocketProducer;
import com.facishare.crm.task.sfa.activitysummary.enums.FileTypeEnum;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/17 14:37
 * @description:
 */
@Component
@Slf4j
public class AttachmentStrategy {

    @Resource
    private List<File2Text> file2TextList;

    @Resource
    private Rec2TextService rec2TextService;
    @Resource
    private ActivityRocketProducer activityRocketProducer;

    public void handleFile(ActivityMessage message) {
        IObjectData activityData = rec2TextService.findById(message);
        List<Map<String, Object>> interactionRecords = (List<Map<String, Object>>) activityData.get("interaction_records");
        if (CollectionUtils.isEmpty(interactionRecords)){
            log.warn("Rec2TextActivityListener not found interactionRecords, message: {}", message);
            return;
        }
        String ext = interactionRecords.get(0).get("ext").toString();
        FileTypeEnum fileType = FileTypeEnum.getByCode(ext);
        for (File2Text file2Text : file2TextList) {
            if (file2Text.support(fileType)) {
                file2Text.execute(message, activityData);
                break;
            }
        }
    }
}
