package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.sfa.lto.activity.enums.ActivityResourceType;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.service.ActivityResourceUsageService;
import com.facishare.crm.task.sfa.activitysummary.enums.FileTypeEnum;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTaskMessage;
import com.facishare.crm.task.sfa.procurement.service.NomonTask;
import com.facishare.crm.task.sfa.rest.EgressApiProxy;
import com.facishare.crm.task.sfa.rest.dto.EgressApiModels;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.support.GDSHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/9 19:52
 * @description:
 */
@Slf4j
@Component
public class AudioAttachment extends AbstractFile2Text {

    @Resource
    private EgressApiProxy egressApiProxy;

    @Resource
    private NomonTask nomonTask;

    @Resource
    private GDSHandler gdsHandler;

    @Resource
    private ActivityResourceUsageService activityResourceUsageService;


    // 声道数
    private static final Integer CHANNEL_NUM = 1;
    // 是否开启说话人分离 0-不开启 1-开启
    private static final Integer SPEAKER_DIARIZATION = 1;
    // 说话人数量
    private static final Integer SPEAKER_NUMBER = 0;


    @Override
    public boolean support(FileTypeEnum fileType) {
        return fileType == FileTypeEnum.WAV ||
                fileType == FileTypeEnum.MP3 ||
                fileType == FileTypeEnum.M4A ||
                fileType == FileTypeEnum.FLV ||
                fileType == FileTypeEnum.MP4 ||
                fileType == FileTypeEnum.WMA ||
                fileType == FileTypeEnum.GP3 ||
                fileType == FileTypeEnum.AMR ||
                fileType == FileTypeEnum.AAC ||
                fileType == FileTypeEnum.OGG_OPUS ||
                fileType == FileTypeEnum.FLAC;
    }

    @Override
    public void execute(ActivityMessage message, IObjectData activityData) {
        rec2Text(message);
        activityResourceUsageService.add(activityData.getTenantId(), activityData.getId(), activityData.getDescribeApiName(), Collections.singletonList(ActivityResourceType.RECORDING), 0);
    }

    public void rec2Text(ActivityMessage message) {
        IObjectData activityData = findById(message);
        if (activityData == null || activityData.get("interaction_records") == null) {
            log.warn("not found objectdata, message: {}", message);
            return;
        }
        String owner = activityData.getOwner().get(0);
        String tenantId = message.getTenantId();
        List<Map<String, Object>> interactionRecords = (List<Map<String, Object>>) activityData.get("interaction_records");
        for (Map<String, Object> interactionRecord : interactionRecords) {
            String path = (String) interactionRecord.get("path");
            String filename = (String) interactionRecord.get("filename");
            String extension = (String) interactionRecord.get("ext");
            Integer size = (Integer) interactionRecord.get("size");
            String url = getNoSignAcUrl(tenantId,gdsHandler.getEAByEI(tenantId), owner, path);
            log.info("getNoSignAcUrl, objectId:{}, path:{}, filename:{}, extension:{}, size:{}, url:{}", message.getObjectId(), path, filename, extension, size, url);
            String taskId = createAsrRecTask(url, CHANNEL_NUM, SPEAKER_DIARIZATION, SPEAKER_NUMBER);
            log.info("createAsrRecTask , objectId:{}, taskId:{}", message.getObjectId(), taskId);
            ActivityTaskMessage.Rec2TextTask rec2TextTask = ActivityTaskMessage.Rec2TextTask.builder()
                    .tenantId(tenantId)
                    .objectId(message.getObjectId())
                    .objectApiName(message.getObjectApiName())
                    .actionCode(message.getInteractiveTypes())
                    .stage(message.getStage())
                    .opId(message.getOpId())
                    .sourceId(message.getSourceId())
                    .language(message.getLanguage())
                    .taskId(taskId)
                    .build();
            //发送语音识别结果的任务，2-4分钟后收到MQ 获取识别的结果。
            nomonTask.sendActivityTextTask(rec2TextTask);
            log.info("sendActivityTextTask to nomonTask , objectId:{}, rec2TextTask:{}", message.getObjectId(), rec2TextTask);
        }
        log.info("activityData:{}", activityData);
    }

    /**
     * 获取无签名访问URL
     *
     * @param path              文件路径
     * @param enterpriseAccount 当前租户ea
     * @param userId            当前用户ID
     * @return 无签名访问URL
     */


    /**
     * 创建语音识别任务
     *
     * @param url                音频文件URL
     * @param channelNum         音频声道数
     * @param speakerDiarization 是否开启说话人分离 0-不开启 1-开启
     * @param speakerNumber      说话人数量
     * @return 任务ID，如果创建失败返回null
     */
    public String createAsrRecTask(String url, Integer channelNum, Integer speakerDiarization, Integer speakerNumber) {
        EgressApiModels.AsrRecTask.Request request = new EgressApiModels.AsrRecTask.Request();
        request.setUrl(url);
        request.setChannelNum(channelNum);
        request.setSpeakerDiarization(speakerDiarization);
        request.setSpeakerNumber(speakerNumber);
        request.setEngineModelType("16k_zh");  // 16k_zh - 中文普通话通用引擎

        try {
            Map<String, String> headers = new HashMap<>();
            EgressApiModels.AsrRecTask.Response response = egressApiProxy.createAsrRecTask(headers, request);
            if (response != null && response.getCode() == 200 && response.getData() != null) {
                return response.getData().getTaskId();
            }
            log.error("创建语音识别任务失败, request={}, response={}", request, response);
        } catch (Exception e) {
            log.error("创建语音识别任务异常, request={}", request, e);
        }
        return null;
    }

}
