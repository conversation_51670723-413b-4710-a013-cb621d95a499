package com.facishare.crm.task.sfa.activitysummary.enums;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/20 10:42
 * @description:
 *
 *      * 1, "分享"
 *      * 2, "日志"
 *      * 3, "指令"
 *      * 4, "审批"
 *      * 5, "销售记录"
 *      * 6, "任务"
 *      * 7, "日程"
 *      * 8, "服务记录"
 *      * 9, "销售流程"
 *      * 10, "PK助手"
 *      * 99, "外勤"
 *      * 9998, "CRM信息"
 *      * 201, "项目任务"
 *      * 2003, "群通知"
 *      * 2004, "跟进记录"
 *      * 2005, "工作汇报"
 *      * 2006, "任务"
 *      * 2007, "CRM审批"
 *      * 2008, "电销记录"
 *      * 9997, "公告"
 *
 */
public enum FeedTypeEnum {
    SHARE(1, "分享"),
    LOG(2, "日志"),
    INSTRUCTION(3, "指令"),
    APPROVAL(4, "审批"),
    SALES_RECORD(5, "销售记录"),
    TASK(6, "任务"),
    SCHEDULE(7, "日程"),
    SERVICE_RECORD(8, "服务记录"),
    SALES_PROCESS(9, "销售流程"),
    PK_HELPER(10, "PK助手"),
    OUTSIDE_WORK(99, "外勤"),
    CRM_INFO(9998, "CRM信息"),
    PROJECT_TASK(201, "项目任务"),
    GROUP_NOTIFICATION(2003, "群通知"),
    FOLLOW_UP_RECORD(2004, "跟进记录"),
    WORK_REPORT(2005, "工作汇报"),
    TASK_2(2006, "任务"),
    CRM_APPROVAL(2007, "CRM审批"),
    TELEMARKETING_RECORD(2008, "电销记录"),
    ANNOUNCEMENT(9997, "公告");

    private Integer code;
    private String desc;

    FeedTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FeedTypeEnum getByCode(Integer code) {
        for (FeedTypeEnum feedTypeEnum : FeedTypeEnum.values()) {
            if (feedTypeEnum.getCode().equals(code)) {
                return feedTypeEnum;
            }
        }
        return FeedTypeEnum.SALES_RECORD;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


}
