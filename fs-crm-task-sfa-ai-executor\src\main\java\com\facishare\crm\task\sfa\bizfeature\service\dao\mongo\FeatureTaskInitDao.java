package com.facishare.crm.task.sfa.bizfeature.service.dao.mongo;

import lombok.Generated;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FeatureTaskInitDao {

    @Generated
    private static final Logger log = LoggerFactory.getLogger(FeatureTaskInitDao.class);
    @Autowired
    @Qualifier("activityDataMongoDbStore")
    private Datastore customerDatastore;

    public FeatureTaskInitDocument initTask(FeatureTaskInitDocument taskDocument) {
            taskDocument.setCreateTime(System.currentTimeMillis());
            try {
                this.customerDatastore.save(taskDocument);
                log.info("初始化特征计算任任务成功, id:{}", taskDocument.getId());
                return taskDocument;
            } catch (Exception e) {
                log.error("初始化特征计算任任务异常", e);
                throw e;
            }

    }

    public List<FeatureTaskInitDocument> findAllTasks() {
        Query<FeatureTaskInitDocument> query = (Query)this.customerDatastore.createQuery(FeatureTaskInitDocument.class).order("createTime");
        return query.asList();
    }

}
