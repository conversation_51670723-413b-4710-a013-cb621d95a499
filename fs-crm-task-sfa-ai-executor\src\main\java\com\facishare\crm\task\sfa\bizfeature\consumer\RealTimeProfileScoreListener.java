package com.facishare.crm.task.sfa.bizfeature.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileConstants;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureCrmNoteContext;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileScoreModel;
import com.facishare.crm.task.sfa.bizfeature.service.CrmNoteManager;
import com.facishare.crm.task.sfa.bizfeature.service.RealTimeProfileScoreService;
import com.facishare.crm.task.sfa.services.SFALicenseService;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 实时画像打分监听器
 */
@Slf4j
@Component
public class RealTimeProfileScoreListener implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private RealTimeProfileScoreService realTimeProfileScoreService;
    @Resource
    private CrmNoteManager crmNoteManager;

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private SFALicenseService sfaLicenseService;

    private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");

    private static final String PROFILE_AGENT = "sales_portrait_insight_agent_app";

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "crm-feature-profile-consumer", (MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                try {
                    ProfileScoreModel featureScoring = messageParse(msg);
                    consume(featureScoring);
                } catch (Exception e) {
                    log.error("ProfileScoreListener error :{}", msg, e);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    private void consume(ProfileScoreModel profileScoreModel) {
        if (profileScoreModel == null || StringUtils.isAnyEmpty(profileScoreModel.getTenantId(), profileScoreModel.getObjectApiName(), profileScoreModel.getObjectId())) {
            log.error("profile score error, profileScoreModel:{}", profileScoreModel);
            return;
        }
        if (gray.isAllow("profile-score-skip-tenant", profileScoreModel.getTenantId())) {
            return;
        }
        if (!sfaLicenseService.checkModuleLicenseExist(profileScoreModel.getTenantId(), PROFILE_AGENT)) {
            log.info("profile agent not exist, tenantId:{}", profileScoreModel.getTenantId());
            return;
        }
        try {
            realTimeProfileScoreService.scoreCalc(profileScoreModel);
        } catch (Exception e) {
            log.error("realtime profile score error, profileScoreModel:{}", profileScoreModel, e);
            FeatureCrmNoteContext featureCrmNoteContext = FeatureCrmNoteContext.builder()
                    .tenantId(profileScoreModel.getTenantId())
                    .receiverIds(Lists.newArrayList(Integer.valueOf(profileScoreModel.getUserId())))
                    .refreshType(ProfileConstants.RefreshType.REALTIME.getValue())
                    .apiName(profileScoreModel.getObjectApiName())
                    .objectId(profileScoreModel.getObjectId())
                    .isSucess(false).build();
            crmNoteManager.sendCrmNote(featureCrmNoteContext);
        }
    }

    private ProfileScoreModel messageParse(MessageExt messageExt) {
        try {
            return JSON.parseObject(StringHelper.toString(messageExt.getBody()), ProfileScoreModel.class);
        } catch (Exception e) {
            log.error("profile scoring message format failed. msgId:{}, body:{}", messageExt.getMsgId(), StringHelper.toString(messageExt.getBody()));
        }
        return null;
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

}