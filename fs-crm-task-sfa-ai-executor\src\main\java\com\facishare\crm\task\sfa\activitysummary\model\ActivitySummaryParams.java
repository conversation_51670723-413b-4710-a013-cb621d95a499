package com.facishare.crm.task.sfa.activitysummary.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/25 18:19
 * @description:
 */
public interface ActivitySummaryParams {

    @Data
    class Content {
        @JSONField(name = "attrs")
        @JsonProperty("attrs")
        private attrs attrs;

        @JSONField(name = "type")
        @JsonProperty("type")
        private String type = "customComp";


        @JSONField(name = "text")
        @JsonProperty("text")
        private String text;
    }

    @Data
    class attrs {
        @JSONField(name = "params")
        @JsonProperty("params")
        private params params;

        @JSONField(name = "compname")
        @JsonProperty("compname")
        private Compname compname;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Compname {
        @JSONField(name = "ava")
        @JsonProperty("ava")
        private String ava = "avatestcomp";

        @JSONField(name = "web")
        @JsonProperty("web")
        private String web = "webAIReportComp";
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class params {
        @JSONField(name = "activity_summary")
        @JsonProperty("activity_summary")
        private List<ActivitySummary> activitySummary;

        @JSONField(name = "title")
        @JsonProperty("title")
        private String title;
    }

    @Data
    class ActivitySummary {

        @JSONField(name = "api_name")
        @JsonProperty(value = "api_name")
        private String apiName;
        @JSONField(name = "summary_detail")
        @JsonProperty(value = "summary_detail")
        private List<SummaryDetail> summaryDetail;
        private Integer count;
        private String owner;
        @JSONField(name = "activity_type")
        @JsonProperty(value = "activity_type")
        private String activityType;
    }

    @Data
    class SummaryDetail {
        @JSONField(name = "api_name")
        @JsonProperty(value = "api_name")
        private String apiName;
        private String label;
        private String summary;
        private Set<String> image;
    }
}
