package com.facishare.crm.task.sfa.activitysummary.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.task.sfa.activitysummary.model.InteractionModel;
import com.facishare.crm.task.sfa.activitysummary.service.ActivityInteractionService;
import com.facishare.crm.task.sfa.activitysummary.service.RequirementService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ActivityInteractiveAiAnswerListener implements ApplicationListener<ContextRefreshedEvent> {


    private AutoConfMQPushConsumer consumer;

    @Autowired
    private ActivityInteractionService activityInteractionService;

    @Autowired
    private RequirementService requirementService;
    @Resource
    private ActivityMongoDao activityMongoDao;

    /**
     * MQ 配置的 consumer section
     * @return
     */
    public String getSection(){
        return "sfa-activity-interaction-ai-answer-consumer";
    }

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", getSection(), (MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                switch (msg.getTags()) {
                    case "sfa-ai-answer":
                        try {
                            InteractionModel.GetAiSuggestionMsg msgEntity = JSON.parseObject(msg.getBody(), InteractionModel.GetAiSuggestionMsg.class);
                            activityInteractionService.handleAiSuggestion(msgEntity);
                        } catch (Exception e) {
                            log.error("ActivityInteractiveAiAnswerListener handleAiSuggestion :{},e:", msg, e);
                            throw new RuntimeException(e);
                        }
                        break;
                    case "sfa-change-speaker-tag":
                        try {
                            InteractionModel.ChangeSpeakerMsg msgEntity = JSON.parseObject(msg.getBody(), InteractionModel.ChangeSpeakerMsg.class);
                            handleChangeSpeaker(msgEntity);
                        } catch (Exception e) {
                            log.error("ActivityInteractiveAiAnswerListener handleChangeSpeaker:{},e:", msg, e);
                        }
                        break;
                    default:
                        break;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }


    public void handleChangeSpeaker(InteractionModel.ChangeSpeakerMsg msg){
        log.warn("handleChangeSpeaker msg :{}", JSONObject.toJSONString(msg));
        if(ObjectUtils.isEmpty(msg) || CollectionUtils.empty(msg.getDetails())){
            log.warn("handleChangeSpeaker msg is empty");
            return;
        }
        //获取被修改的语料的seq
        List<String> docIds = msg.getDetails().stream().filter(x->ObjectUtils.isNotEmpty(x.getDocId())).map(InteractionModel.ChangeSpeakerDetail::getDocId).collect(Collectors.toList());
        List<InteractiveDocument> documents = new ArrayList<>();
        if(CollectionUtils.empty(docIds)){
            log.warn("handleChangeSpeaker docIds is empty");
            List<String> userId = msg.getDetails().stream().filter(x->ObjectUtils.isNotEmpty(x.getOldActivityUserId())).map(InteractionModel.ChangeSpeakerDetail::getOldActivityUserId).collect(Collectors.toList());
            userId.addAll(msg.getDetails().stream().filter(x->ObjectUtils.isNotEmpty(x.getTargetUserId())).map(InteractionModel.ChangeSpeakerDetail::getTargetUserId).collect(Collectors.toList()));
            if(CollectionUtils.empty(userId)){
                log.warn("handleChangeSpeaker getOldActivityUserId is empty");
                return;
            }
            List<InteractiveDocument> interactiveList = queryListByActiveRecordIdAndPage(msg);
            if(CollectionUtils.empty(interactiveList)){
                log.warn("handleChangeSpeaker interactiveList is empty");
                return;
            }
            documents = interactiveList.stream().filter(x-> userId.contains(x.getActivityUserId())).collect(Collectors.toList());
        }else{
            documents = activityMongoDao.queryByIds(msg.getTenantId(),docIds);
        }
        if(CollectionUtils.empty(documents)){
            log.warn("handleChangeSpeaker documents is empty");
            return;
        }
        activityInteractionService.handleChangeSpeaker(msg,documents);
        requirementService.handleChangeSpeaker(msg,documents);
    }

    public List<InteractiveDocument> queryListByActiveRecordIdAndPage(InteractionModel.ChangeSpeakerMsg msg){
        int index = 0;
        int limit = 1000;
        List<InteractiveDocument> interactiveList  = new ArrayList<>();
        for(int i=0;i<5;i++){
            List<InteractiveDocument> tempList = activityMongoDao.queryListByActiveRecordId(msg.getTenantId(),msg.getObjectId(),index, limit);
            if(CollectionUtils.empty(tempList)){
                break;
            }
            interactiveList.addAll(tempList);
            if(tempList.size()<limit){
                break;
            }
            index++;
        }
        return interactiveList;
    }
}
