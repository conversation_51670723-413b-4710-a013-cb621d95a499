<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <import resource="classpath:spring/metadata.xml"/>
    <import resource="classpath:spring/common.xml"/>
    <import resource="classpath:spring/dubbo.xml"/>
    <import resource="classpath:spring/log.xml"/>
    <import resource="classpath:spring/flow.xml"/>
    <import resource="classpath:spring/privilege.xml"/>
    <import resource="classpath:spring/licence.xml"/>
    <import resource="classpath:spring/fsi.xml"/>


    <context:component-scan base-package="com.facishare.paas.appframework.*,com.facishare.crm"/>
    <context:annotation-config/>


    <bean class="com.facishare.paas.appframework.metadata.MetaDataServiceImpl" id="metaDataService" />
    <bean class="com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl" id="metaDataFindService" />
    <bean class="com.facishare.paas.metadata.dispatcher.ObjectDataDispatcher" id="objectDataService" />
    <bean class="com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl" id="crmNotificationService" />


    <context:component-scan base-package="com.facishare.paas.appframework,com.facishare.crm"/>
    <context:component-scan base-package="com.facishare.paas.metadata" />
    <context:annotation-config/>
</beans>