package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl.feature;

import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class QualityScoreInsightHandler extends AbstractInsightHandler<AttendeesInsightModel.FeatureInsightResult> {

    private static final String PROMPT = "prompt_attendee_insight_quality_score";

    @Override
    public String getInsightType() {
        return "quality_score";
    }

    @Override
    public void insight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        doInsight(attendeesInsightMessage);
    }


    @Override
    public void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        String completion = completion(attendeesInsightMessage, PROMPT);
        AttendeesInsightModel.FeatureInsightResult insightResult = fixJSONFormatService.getDataFixedInvalidJSON(User.systemUser(attendeesInsightMessage.getTenantId()), "", completion, AttendeesInsightModel.FeatureInsightResult.class);
        if (insightResult == null || Safes.isEmpty(insightResult.getInsightText())) {
            return;
        }

        IObjectData activeRecord = attendeesInsightMessage.getExtendData().getActiveRecord();
        activeRecord.set("quality_score", insightResult.getInsightText());
        serviceFacade.updateObjectData(User.systemUser(attendeesInsightMessage.getTenantId()), activeRecord);
        log.info("QualityScoreInsightHandler end.");
    }

    @Override
    protected String getUserNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        return super.getOurSideNames(insightMessage);
    }

    @Override
    protected void addSceneVariables(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, Map<String, Object> sceneVariables) {
        addMeetingObjectives(attendeesInsightMessage, sceneVariables);
    }
}
