#增加相关团队
ADD_RELATED_TEAM_URL = http://10.113.32.47:8009/fs-paas-datarights/team/addMemberToTeam
#更新相关团队
UPDATE_RELATED_TEAM_URL = http://10.113.32.47:8009/fs-paas-datarights/team/updateRecordTeam
#查询相关团队
QUERY_RELATED_TEAM_URL = http://10.113.32.47:8009/fs-paas-datarights/team/queryRecordsTeam
#删除相关团队
DELETE_RELATED_TEAM_URL = http://10.113.32.47:8009/fs-paas-datarights/team/delTeam
#用户数据权限
DATA_RIGHTS_SQL_URL = http://10.113.32.47:8009/fs-paas-datarights/datarights/datarightssql
#详情记录权限
OBJECTS_PERMISSION_URL = http://10.113.32.47:8009/fs-paas-datarights/datarights/objectspermission
#pg数据库用户名
USER_NAME = postgres
#pg数据库密码
USER_PASSWORD = 3278E7C89C0ECB364EDDC109C4DB488184279F1CD8993DA2
#测试密码 681ED3196E9FC7617D25A51778BDC3A50DE791E7CEF3527F
