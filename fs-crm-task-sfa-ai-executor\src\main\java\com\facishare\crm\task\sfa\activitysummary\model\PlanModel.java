package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分段计划模型
 * 用于AI Agent的Planner组件，定义分段策略和参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlanModel {
    /**
     * 分段策略名称
     * 例如：基于主题分段、基于时间分段、基于发言人分段等
     */
    private String strategy;
    
    /**
     * 预期段落数量
     * 根据文本长度和复杂度，预计应该分成多少段
     */
    private int expectedSegments;
    
    /**
     * 关注点列表
     * 分段时需要特别关注的方面，如主题变化、关键词密度、语义连贯性等
     */
    private List<String> focusPoints;
    
    /**
     * 特殊处理指令列表
     * 针对特定文本内容的特殊处理要求，如合并某些段落、拆分某些段落等
     */
    private List<String> specialHandling;
}
