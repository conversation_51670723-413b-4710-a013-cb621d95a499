package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.activity.model.SFANewBusinessModel;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.task.sfa.activitysummary.constant.ActivityQuestionConstants;
import com.facishare.crm.task.sfa.activitysummary.model.InteractionModel;
import com.facishare.crm.task.sfa.activitysummary.model.auditlog.ActivityMessageConverter;
import com.facishare.crm.task.sfa.common.constants.AIQuestionConstants;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.util.*;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityNewBusinessService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    public ActivitySummaryService activitySummaryService;
    @Autowired
    private IObjectDescribeService iObjectDescribeService;
    @Autowired
    private CompletionsService completionsService;
    @Autowired
    @Qualifier("SFAJedisCmd")
    private MergeJedisCmd mergeJedisCmd;

    public static String PROMPT_NEWBUSINESS_ACCOUNT_ABSTRACT = "prompt_newBusiness_account_abstract";
    public static String PROMPT_NEWBUSINESS_OPPORTUNITY_ABSTRACT = "prompt_newBusiness_opportunity_abstract";
    public static String PROMPT_NEWBUSINESS_CONTACT_ABSTRACT = "prompt_newBusiness_contact_abstract";
    public static String PROMPT_NEWBUSINESS_DEPART_ABSTRACT = "prompt_newBusiness_depart_abstract";

    public static String PROMPT_NEWBUSINESS_MATCH_DETAILS="prompt_newBusiness_match_details";
    public static String LIST1="list1";
    public static String LIST2="list2";

    public static List<String>  NEWBUSINESS_FILTER_WORDS=new ArrayList<>();
    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            String filterWords = config.get("NewBusiness_filter_words", "");
            if(ObjectUtils.isNotEmpty(filterWords)) {
                NEWBUSINESS_FILTER_WORDS = JSONObject.parseArray(filterWords, String.class);
            }
        });
    }
    @SFAAuditLog(bizName = "new_business", entityClass = ActivityMessage.class, convertClass = ActivityMessageConverter.class)
    public void handleNewBusiness(ActivityMessage activityMessage,boolean isLastTime){
        try {
            execute(InteractionModel.Arg.builder().tenantId(activityMessage.getTenantId())
                    .userId(activityMessage.getOpId())
                    .activeRecordId(activityMessage.getObjectId()).op("i").language(activityMessage.getLanguage())
                    .realTimeLastTimeFlag(isLastTime).build());
        }catch (Exception e){
            log.error("ActivityNewBusinessService handleNewBusiness e:",e);
        }
    }
    public void execute(InteractionModel.Arg arg) {
        log.info("ActivityNewBusinessService execute tenantId:{},activeRecordId:{},op:{}", arg.getTenantId(), arg.getActiveRecordId(), arg.getOp());
        //2、查询销售记录
        //3、判断有没有关联客户
        //4、判断有没有互动语料
        //5、
        User systemUser = new User(arg.getTenantId(), CommonConstant.SUPER_USER);
        User user = new User(arg.getTenantId(), arg.getUserId());

        IActionContext actionContext = ActionContextExt.of(systemUser).getContext();
        actionContext.put(ActionContextKey.SEARCH_RICH_TEXT_EXTRA, true);
        IObjectData activeRecordData = serviceFacade.findObjectData(actionContext, arg.getActiveRecordId(), CommonConstant.ACTIVE_RECORD_API_NAME);
        if (ObjectUtils.isEmpty(activeRecordData)) {
            log.warn("ActivityNewBusinessService execute activeRecordData is null tenantId:{},activeRecordId:{}", user.getTenantId(), arg.getActiveRecordId());
            return;
        }
        String accountId = InheritRecordUtil.getStringValue(activeRecordData, CommonConstant.ACCOUNT_ID, "");
        if (ObjectUtils.isEmpty(accountId)) {
            log.warn("ActivityNewBusinessService execute accountId is null,activeRecordId:{}", arg.getActiveRecordId());
            return;
        }
        String content = InheritRecordUtil.getStringValue(activeRecordData, CommonConstant.INTERACTIVE_CONTENT, "");
        //处理客户
        if (ObjectUtils.isEmpty(content) || content.length() < 150) {
            log.warn("ActivityNewBusinessService execute content is null,activeRecordId:{}", arg.getActiveRecordId());
            return;
        }
        Map<String, IObjectDescribe> describeMap  = new HashMap<>();
        try {
            //actionContext.setLang(arg.getLanguage());
            List<IObjectDescribe> list = iObjectDescribeService.findDescribeListByApiNames(user.getTenantId(),Lists.newArrayList(Utils.ACCOUNT_API_NAME,Utils.NEW_OPPORTUNITY_API_NAME,Utils.CONTACT_API_NAME,CommonConstant.ACCOUNT_DEPARTMENT_API_NAME),actionContext);
            describeMap = list.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));
        }catch (Exception e){
            log.error("ActivityNewBusinessService execute findDescribeListByApiNames error ",e);
        }
        if(ObjectUtils.isEmpty(describeMap)){
            log.error("ActivityNewBusinessService execute describeMap is null ");
            return;
        }
        StopWatch stopWatch = StopWatch.create("handleLinkAccount");
//        final List<SFANewBusinessModel.Result>[] accountResult = new List[]{new ArrayList<>()};
//        final List<SFANewBusinessModel.Result>[] opportunityResult = new List[]{new ArrayList<>()};
//        final List<SFANewBusinessModel.Result>[] contactResult = new List[]{new ArrayList<>()};
//        final List<SFANewBusinessModel.Result>[] departmentResult = new List[]{new ArrayList<>()};
        SFAJedisLock lock = new SFAJedisLock(mergeJedisCmd, getRedisKey(user,arg,"ActivityNewBusinessService"), 5*60*1000);
        boolean b = lock.tryLock();
        if (!b) {
            log.warn("ActivityNewBusinessService tryLock b:{}",b);
            return;
        }
        try {
            //从monggo中获取语料，如果没有则从对象中获取，使用不同的提问词
            String corpora = completionsService.getCorpusStrWithSpeakerByMax(user.getTenantId(),activeRecordData.getId(),arg.getLanguage());
            Map<String, Object> sceneParamMap = new HashMap<>();
            sceneParamMap.put(ActivityQuestionConstants.CORPORA_FROM_MONGGO,content);
            if(ObjectUtils.isNotEmpty(corpora)){
                sceneParamMap.put(ActivityQuestionConstants.CORPORA_FROM_MONGGO,corpora);
            }

            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            Map<String, IObjectDescribe> finalDescribeMap = describeMap;
            parallelTask.submit(() -> {
                StopWatch stopWatch1 = StopWatch.create("handleLinkAccount");
                stopWatch1.lap("accountResult");
                List<SFANewBusinessModel.Result> accountResult = handleAiAnalysis(user,PROMPT_NEWBUSINESS_ACCOUNT_ABSTRACT,SFANewBusinessModel.ACCOUNT_PREF_FILEDS,Lists.newArrayList(SFANewBusinessModel.AccountAbstractsFieldEnum.values()),arg,sceneParamMap);
                handleLinkAccount(systemUser,accountId, accountResult);
                stopWatch1.lap("accountResult handleSpecialFields");
                handleSpecialFields(systemUser,SFANewBusinessModel.ACCOUNT_PREF_FILEDS, accountResult, finalDescribeMap,Utils.ACCOUNT_API_NAME,arg);
                stopWatch1.logSlow(1000);
                saveData(systemUser,accountResult,"name","account_id",activeRecordData,CommonConstant.ACTIVITY_ACCOUNT_SUMMARY_OBJ);
            });
            parallelTask.submit(() -> {
                StopWatch stopWatch2 = StopWatch.create("handleLinkOpportunity");
                stopWatch2.lap("opportunityResult");
                List<SFANewBusinessModel.Result> opportunityResult = handleAiAnalysis(user,PROMPT_NEWBUSINESS_OPPORTUNITY_ABSTRACT,SFANewBusinessModel.OPPORTUNITY_PREF_FILEDS,Lists.newArrayList(SFANewBusinessModel.OpportunityAbstractsFieldEnum.values()),arg,sceneParamMap);
                handleLinkOpportunity(systemUser,accountId, opportunityResult,arg);
                stopWatch2.lap("opportunityResult handleSpecialFields");
                handleSpecialFields(systemUser,SFANewBusinessModel.OPPORTUNITY_PREF_FILEDS, opportunityResult,finalDescribeMap,Utils.NEW_OPPORTUNITY_API_NAME,arg);
                stopWatch2.logSlow(1000);
                saveData(systemUser,opportunityResult,"name","new_opportunity_id",activeRecordData,CommonConstant.ACTIVITY_OPPORTUNITY_SUMMARY_OBJ);
            });
            parallelTask.submit(() -> {
                StopWatch stopWatch3 = StopWatch.create("handleLinkContact");
                stopWatch3.lap("contactResult");
                List<SFANewBusinessModel.Result> contactResult = handleAiAnalysis(user,PROMPT_NEWBUSINESS_CONTACT_ABSTRACT,SFANewBusinessModel.CONTACTS_PREF_FILEDS,Lists.newArrayList(SFANewBusinessModel.ContactAbstractsFieldEnum.values()),arg,sceneParamMap);
                handleLinkContact(systemUser,accountId, contactResult,arg);
                stopWatch3.lap("contactResult handleSpecialFields");
                handleSpecialFields(systemUser,SFANewBusinessModel.CONTACTS_PREF_FILEDS, contactResult,finalDescribeMap,Utils.CONTACT_API_NAME,arg);
                stopWatch3.logSlow(1000);
                saveData(systemUser,contactResult,"name","contact_id",activeRecordData,CommonConstant.ACTIVITY_CONTACT_SUMMARY_OBJ);
            });
            parallelTask.submit(() -> {
                StopWatch stopWatch4 = StopWatch.create("handleLinkAccountDepartment");
                stopWatch4.lap("departmentResult");
                List<SFANewBusinessModel.Result> departmentResult = handleAiAnalysis(user,PROMPT_NEWBUSINESS_DEPART_ABSTRACT, SFANewBusinessModel.DEPARTMENT_PREF_FILEDS,Lists.newArrayList(SFANewBusinessModel.DepartmentAbstractsFieldEnum.values()),arg,sceneParamMap);
                handleLinkAccountDepartment(systemUser,accountId, departmentResult,arg);
                stopWatch4.lap("departmentResult handleSpecialFields");
                handleSpecialFields(systemUser,SFANewBusinessModel.DEPARTMENT_PREF_FILEDS, departmentResult,finalDescribeMap,CommonConstant.ACCOUNT_DEPARTMENT_API_NAME,arg);
                stopWatch4.logSlow(1000);

                saveData(systemUser,departmentResult,"name","account_department_id",activeRecordData,CommonConstant.ACTIVITY_DEPARTMENT_SUMMARY_OBJ);
            });
            try {
                parallelTask.await(600, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("ActivityNewBusinessService execute error e:", e);
            }
        }catch (Exception e){
            log.error("ActivityNewBusinessService execute lock error ",e);
        } finally {
            lock.unlock();
        }

//        IObjectDescribe describeBusiness = AccountPathUtil.getObjectDescribe(systemUser, CommonConstant.ACTIVITY_BUSINESS_API_NAME);
//        IObjectData iObjectData = new ObjectData();
//        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
//        searchTemplateQuery.setLimit(1);
//        searchTemplateQuery.setFindExplicitTotalNum(false);
//        searchTemplateQuery.setNeedReturnCountNum(false);
//        searchTemplateQuery.setPermissionType(0);
//        List<IFilter> filters =Lists.newArrayList();
//        SearchUtil.fillFilterEq(filters, AIQuestionConstants.Field.active_record_id, arg.getActiveRecordId());
//        searchTemplateQuery.setFilters(filters);
//        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(systemUser, CommonConstant.ACTIVITY_BUSINESS_API_NAME, searchTemplateQuery);
//        if(ObjectUtils.isNotEmpty(queryResult) && CollectionUtil.isNotEmpty(queryResult.getData())){
//            iObjectData = queryResult.getData().get(0);
//        }
//        if(CollectionUtil.isNotEmpty(accountResult[0])){
//            iObjectData.set(SFANewBusinessModel.Field.ACCOUNT_ABSTRACT, accountResult[0]);
//        }
//        if(CollectionUtil.isNotEmpty(opportunityResult[0])){
//            iObjectData.set(SFANewBusinessModel.Field.NEW_OPPORTUNITY_ABSTRACT, opportunityResult[0]);
//        }
//        if(CollectionUtil.isNotEmpty(contactResult[0])){
//            iObjectData.set(SFANewBusinessModel.Field.CONTACT_ABSTRACT, contactResult[0]);
//        }
//        if(CollectionUtil.isNotEmpty(departmentResult[0])){
//            iObjectData.set(SFANewBusinessModel.Field.ACCOUNT_DEPARTMENT_ABSTRACT, departmentResult[0]);
//        }
//        iObjectData.set(AIQuestionConstants.Field.active_record_id, activeRecordData.getId());
//        iObjectData.set("object_describe_api_name", CommonConstant.ACTIVITY_BUSINESS_API_NAME);
//        iObjectData.set("object_describe_id", describeBusiness.getId());
//        iObjectData.set(Tenantable.TENANT_ID, user.getTenantId());
//        iObjectData.set("owner", activeRecordData.getOwner());
//        iObjectData.set("record_type", "default__c");
//        if(ObjectUtils.isNotEmpty(iObjectData.getId())){
//            serviceFacade.updateObjectData(systemUser,iObjectData);
//        }else{
//            try {
//                serviceFacade.bulkSaveObjectData(Lists.newArrayList(iObjectData),systemUser);
//                LogUtil.recordLogs(systemUser, Lists.newArrayList(iObjectData),describeBusiness, EventType.ADD, ActionType.Add);
//            }catch (Exception e){
//                log.error("ActivityInteractionService saveTopic error :",e);
//            }
//        }
        stopWatch.logSlow(1000);
    }

    public List<SFANewBusinessModel.Result> handleAiAnalysis(User user,String apiName,List<String> prefFields ,List<? extends SFANewBusinessModel.AbstractsFieldEnum> enumLists,InteractionModel.Arg arg,Map<String, Object> matchParamMap){
        matchParamMap = activitySummaryService.handleAiCompleteLanguage(matchParamMap,null,arg.getLanguage());
        List<IObjectData> resultList = activitySummaryService.getAi(user,apiName,matchParamMap,arg.getActiveRecordId());
        if(CollectionUtil.isEmpty(resultList)){
            log.warn("ActivityNewBusinessService handleAiAnalysis resultList is null");
            return  Lists.newArrayList();
        }

        List<SFANewBusinessModel.Result> analysisResults = new ArrayList<>();
        resultList.stream().forEach(x->{
            SFANewBusinessModel.Result result1 = new SFANewBusinessModel.Result();
            List<SFANewBusinessModel.Abstracts> abstracts = new ArrayList<>();
            for(SFANewBusinessModel.AbstractsFieldEnum enum1 :enumLists){
                if(x.containsField(enum1.getCode()) && ObjectUtils.isNotEmpty(x.get(enum1.getCode()))){
                    SFANewBusinessModel.Abstracts abstracts1 =  new SFANewBusinessModel.Abstracts();
                    abstracts1.setUniqueSign(enum1.getCode());
                    abstracts1.setLabel(I18nClient.getInstance().getOrDefault(getI18nLable(enum1.getCode()),0, arg.getLanguage(),enum1.getDesc()));
                    abstracts1.setContext(x.get(enum1.getCode()).toString());
                    abstracts.add(abstracts1);
                }
            }
            result1.setAbstracts(abstracts);
            IObjectData objectData = new ObjectData();
            prefFields.stream().forEach(f->{
                if(x.containsField(f) && ObjectUtils.isNotEmpty(x.get(f))){
                    objectData.set(f,x.get(f));
                }
            });
            if(CollectionUtil.isNotEmpty(result1.getAbstracts()) || ObjectUtils.isNotEmpty(objectData)){
                result1.setNeedUpdObjectData(ObjectDataDocument.of(objectData));
                result1.setCreatTime(System.currentTimeMillis()+"");
                result1.setId("");
                result1.setName("");
                analysisResults.add(result1);
            }
        });
        return analysisResults;
    }

    public void handleLinkAccount(User user,String accountId,List<SFANewBusinessModel.Result> accountResult){
        filterSpecialCharacter(accountResult,SFANewBusinessModel.AccountAbstractsFieldEnum.business_require_changes.getCode());
        if(CollectionUtil.isEmpty(accountResult)){
            log.warn("ActivityNewBusinessService handleLinkAccount accountResult is null");
            return ;
        }
        IObjectData objectData =  serviceFacade.findObjectData(user, accountId,CommonConstant.ACCOUNT_API_NAME);
        accountResult.stream().forEach(x->{
            x.setId(objectData.getId());
            x.setName(objectData.getName());
        });

    }
    public void handleLinkOpportunity(User user,String accountId,List<SFANewBusinessModel.Result> opportunityResult,InteractionModel.Arg arg){
        filterSpecialCharacter(opportunityResult,SFANewBusinessModel.OpportunityAbstractsFieldEnum.product_info.getCode());
        if(CollectionUtil.isEmpty(opportunityResult)){
            log.warn("ActivityNewBusinessService handleLinkOpportunity opportunityResult is null");
            return ;
        }
        List<String>  productInfos = extractKeywords(opportunityResult,SFANewBusinessModel.OpportunityAbstractsFieldEnum.product_info.getCode());
        if(CollectionUtil.isEmpty(productInfos)){
            log.warn("ActivityNewBusinessService handleLinkOpportunity productInfos is null");
            return ;
        }
        //获取所有的商机，以及商机明显
        SearchTemplateQuery searchTemplateQuery = buildSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, CommonConstant.ACCOUNT_ID, accountId);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, CommonConstant.NEW_OPPORTUNITY_API_NAME, searchTemplateQuery, Lists.newArrayList("_id","name"));
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            log.warn("ActivityNewBusinessService handleLinkOpportunity pportunity is null");
            return ;
        }
        List<String> opportunityIds = queryResult.getData().stream().map(IObjectData::getId).collect(Collectors.toList());
        filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, CommonConstant.NEW_OPPORTUNITY_ID, opportunityIds);
        searchTemplateQuery.resetFilters(filters);
        QueryResult<IObjectData> queryDetailResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, CommonConstant.NEW_OPPORTUNITY_LINES_API_NAME, searchTemplateQuery, Lists.newArrayList("_id","product_id",CommonConstant.NEW_OPPORTUNITY_ID));
        if(ObjectUtils.isEmpty(queryDetailResult) || CollectionUtil.isEmpty(queryDetailResult.getData())){
            log.warn("ActivityNewBusinessService handleLinkOpportunity NewOpportunityLinesObj is null");
            return ;
        }
        List<String> productIds = queryResult.getData().stream().filter(x->ObjectUtils.isNotEmpty(x.get("product_id"))).map(x->x.get("product_id").toString()).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(productIds)){
            log.warn("ActivityNewBusinessService handleLinkOpportunity productIds is null");
            return ;
        }
        List<IObjectData> productResult = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), productIds,CommonConstant.PRODUCT_API_NAME);
        if(CollectionUtil.isEmpty(productResult)){
            log.warn("ActivityNewBusinessService handleLinkOpportunity productResult is null");
            return ;
        }
        List<String> productNames = productResult.stream().map(IObjectData::getName).collect(Collectors.toList());
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put(LIST1,JSONObject.toJSONString(productInfos));
        sceneParamMap.put(LIST2,JSONObject.toJSONString(productNames));
        sceneParamMap = activitySummaryService.handleAiCompleteLanguage(sceneParamMap,null,arg.getLanguage());
        List<IObjectData> resultList = activitySummaryService.getAi(user,PROMPT_NEWBUSINESS_MATCH_DETAILS,sceneParamMap,null);

        if(CollectionUtil.isEmpty(resultList)){
            log.warn("ActivityNewBusinessService handleLinkOpportunity resultList is null");
            return ;
        }
        Map<String,String> productNamesOfIdMap = productResult.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName,(v1,v2)->v1));

        Map<String,String> productIdOfOpportunityIdMap = queryDetailResult.getData().stream().filter(x->ObjectUtils.isNotEmpty(x.get("product_id")))
                .collect(Collectors.toMap( x->x.get("product_id").toString(),x->x.get(CommonConstant.NEW_OPPORTUNITY_ID).toString(),(v1,v2)->v1));

        Map<String,String> opportunityIdOFNameMap = queryResult.getData().stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));

        Map<String,String> resulMap = aiResultToMap(resultList);

        opportunityResult.stream().forEach(x->{
            if(CollectionUtil.isNotEmpty(x.getAbstracts())){
                String productInfo = extractSingleKeyword(x.getAbstracts(),SFANewBusinessModel.OpportunityAbstractsFieldEnum.product_info.getCode());

                if(StringUtils.isNotEmpty(productInfo) && resulMap.containsKey(productInfo) && ObjectUtils.isNotEmpty(resulMap.get(productInfo))){
                    String productName = resulMap.get(productInfo);
                    String productId = productNamesOfIdMap.get(productName);
                    String opportunityId = productIdOfOpportunityIdMap.get(productId);
                    String opportunityName = opportunityIdOFNameMap.get(opportunityId);
                    x.setId(opportunityId);
                    x.setName(opportunityName);
                }
            }
        });
    }

    public void handleLinkContact(User user,String accountId, List<SFANewBusinessModel.Result> contactResult,InteractionModel.Arg arg){
        filterSpecialCharacter(contactResult,SFANewBusinessModel.ContactAbstractsFieldEnum.contact_name.getCode());
        if(CollectionUtil.isEmpty(contactResult)){
            log.warn("ActivityNewBusinessService handleLinkContact contactResult is null");
            return ;
        }
        List<String>  contactNameList = extractKeywords(contactResult,SFANewBusinessModel.ContactAbstractsFieldEnum.contact_name.getCode());

        if(CollectionUtil.isEmpty(contactNameList)){
            log.warn("ActivityNewBusinessService handleLinkContact contactName is null");
            return ;
        }
        //获取所有的联系人
        SearchTemplateQuery searchTemplateQuery = buildSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, CommonConstant.ACCOUNT_ID, accountId);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, CommonConstant.CONTACT_API_NAME, searchTemplateQuery, Lists.newArrayList("_id","name"));
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            log.warn("ActivityNewBusinessService handleLinkContact queryResult is null");
            return ;
        }
        List<String> dbNames = queryResult.getData().stream().map(IObjectData::getName).collect(Collectors.toList());

        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put(LIST1,JSONObject.toJSONString(contactNameList));
        sceneParamMap.put(LIST2,JSONObject.toJSONString(dbNames));
        sceneParamMap = activitySummaryService.handleAiCompleteLanguage(sceneParamMap,null,arg.getLanguage());
        List<IObjectData> resultList = activitySummaryService.getAi(user,PROMPT_NEWBUSINESS_MATCH_DETAILS,sceneParamMap,null);

        if(CollectionUtil.isEmpty(resultList)){
            log.warn("ActivityNewBusinessService handleLinkContact resultList is null");
            return ;
        }
        Map<String,String> contactNameOfIdMap = queryResult.getData().stream().collect(Collectors.toMap(IObjectData::getName, IObjectData::getId));

        Map<String,String> resulMap = aiResultToMap(resultList);

        contactResult.stream().forEach(x->{
            if(CollectionUtil.isNotEmpty(x.getAbstracts())){
                String contactName = extractSingleKeyword(x.getAbstracts(),SFANewBusinessModel.ContactAbstractsFieldEnum.contact_name.getCode());
                if(StringUtils.isNotEmpty(contactName) && resulMap.containsKey(contactName) && ObjectUtils.isNotEmpty(resulMap.get(contactName))){
                    String dbName = resulMap.get(contactName);
                    String contactId = contactNameOfIdMap.get(dbName);
                    x.setId(contactId);
                    x.setName(dbName);
                }

            }
        });

    }


    public void handleLinkAccountDepartment(User user,String accountId, List<SFANewBusinessModel.Result> departmentResult,InteractionModel.Arg arg){
        filterSpecialCharacter(departmentResult,SFANewBusinessModel.DepartmentAbstractsFieldEnum.departments_info.getCode());
        if(CollectionUtil.isEmpty(departmentResult)){
            log.warn("ActivityNewBusinessService handleLinkAccountDepartment departmentResult is null");
            return ;
        }
        List<String>  departmentsInfoList = extractKeywords(departmentResult,SFANewBusinessModel.DepartmentAbstractsFieldEnum.departments_info.getCode());
        if(CollectionUtil.isEmpty(departmentsInfoList)){
            log.warn("ActivityNewBusinessService handleLinkAccountDepartment departmentsInfoList is null");
            return ;
        }
        //获取所有的部门

        SearchTemplateQuery searchTemplateQuery = buildSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, CommonConstant.ACCOUNT_ID, accountId);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, CommonConstant.ACCOUNT_DEPARTMENT_API_NAME, searchTemplateQuery, Lists.newArrayList("_id","name"));
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            log.warn("ActivityNewBusinessService handleLinkAccountDepartment queryResult is null");
            return ;
        }
        List<String> dbNames = queryResult.getData().stream().map(IObjectData::getName).collect(Collectors.toList());
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put(LIST1,JSONObject.toJSONString(departmentsInfoList));
        sceneParamMap.put(LIST2,JSONObject.toJSONString(dbNames));
        sceneParamMap = activitySummaryService.handleAiCompleteLanguage(sceneParamMap,null,arg.getLanguage());
        List<IObjectData> resultList = activitySummaryService.getAi(user,PROMPT_NEWBUSINESS_MATCH_DETAILS,sceneParamMap,null);
        if(CollectionUtil.isEmpty(resultList)){
            log.warn("ActivityNewBusinessService handleLinkAccountDepartment resultList is null");
            return ;
        }
        Map<String,String> resulMap = aiResultToMap(resultList);

        Map<String,String> dbNameOfIds = queryResult.getData().stream().collect(Collectors.toMap(IObjectData::getName, IObjectData::getId));

        departmentResult.stream().forEach(x->{
            if(CollectionUtil.isNotEmpty(x.getAbstracts())){
                String departmentsInfo = extractSingleKeyword(x.getAbstracts(),SFANewBusinessModel.DepartmentAbstractsFieldEnum.departments_info.getCode());
                if(StringUtils.isNotEmpty(departmentsInfo) && resulMap.containsKey(departmentsInfo) && ObjectUtils.isNotEmpty(resulMap.get(departmentsInfo))){
                    String dbName = resulMap.get(departmentsInfo);
                    String departmentId = dbNameOfIds.get(dbName);
                    x.setId(departmentId);
                    x.setName(dbName);
                }
            }
        });

    }

    public SearchTemplateQuery buildSearchTemplateQuery(){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(500);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        return searchTemplateQuery;
    }

    public List<String> extractKeywords(List<SFANewBusinessModel.Result> departmentResult, String uniqueSign){
        List<String>  keyWords = new ArrayList<>();
        departmentResult.stream().forEach(x->{
            if(ObjectUtils.isNotEmpty(x.getAbstracts())){
                x.getAbstracts().stream().forEach(y->{
                    if(uniqueSign.equals(y.getUniqueSign()) && ObjectUtils.isNotEmpty(y.getContext())){
                        keyWords.add(y.getContext());
                    }
                });
            }
        });
        return keyWords;
    }
    public String extractSingleKeyword(List<SFANewBusinessModel.Abstracts> abstracts, String uniqueSign){
        for(SFANewBusinessModel.Abstracts y:abstracts){
            if(uniqueSign.equals(y.getUniqueSign()) && ObjectUtils.isNotEmpty(y.getContext())){
                return y.getContext();
            }
        }
        return "";
    }


    public Map<String,String> aiResultToMap(List<IObjectData> resultList){
        return resultList.stream().collect(Collectors.toMap(x->x.get(LIST1,String.class),x->x.get(LIST2,String.class),(existing, replacement) -> existing));
    }

    public void handleSpecialFields(User user,List<String> prefFields, List<SFANewBusinessModel.Result> resultList, Map<String, IObjectDescribe> describeMap,String objectApiName,InteractionModel.Arg arg){
        if(CollectionUtil.isEmpty(resultList)){
            return;
        }
        //遍历总结出来的数据
        resultList.stream().forEach(d->{
            if(ObjectUtils.isEmpty(d.getNeedUpdObjectData())){
                return;
            }
            ObjectDataDocument data = d.getNeedUpdObjectData();
            d.setColumns(prefFields);
            //遍历预设的字段
            prefFields.stream().forEach(field->{
                //判断预设字段是否有值
                if(data.containsKey(field) && ObjectUtils.isNotEmpty(data.get(field))){
                    IObjectDescribe describe = describeMap.get(objectApiName);
                    IFieldDescribe fieldDescribe = describe.getFieldDescribe(field);
                    if(IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())){
                        data.put(field+"__r",data.get(field));
                        String targetApiName = fieldDescribe.get("target_api_name").toString();
                        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                        searchTemplateQuery.setLimit(1);
                        searchTemplateQuery.setFindExplicitTotalNum(false);
                        searchTemplateQuery.setNeedReturnCountNum(false);
                        searchTemplateQuery.setPermissionType(0);
                        List<IFilter> filters = com.google.common.collect.Lists.newArrayList();
                        SearchUtil.fillFilterEq(filters, IObjectData.NAME, data.get(field).toString());
                        searchTemplateQuery.setFilters(filters);
                        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, targetApiName, searchTemplateQuery, Lists.newArrayList(IObjectData.ID,IObjectData.NAME));
                        if(ObjectUtils.isNotEmpty(queryResult) && CollectionUtils.isNotEmpty(queryResult.getData())){
                            data.put(field,queryResult.getData().get(0).getId());
                        }
                    }else if (IFieldType.SELECT_ONE.equals(fieldDescribe.getType())){
                        data.put(field+"__r",data.get(field));
                        List<Map> optionsList = fieldDescribe.get("options", List.class);
                        for(Map option:optionsList){
                            if(option.get("label").toString().equals(data.get(field).toString())){
                                data.put(field,option.get("value"));
                                data.put(field+"__r",I18nClient.getInstance().getOrDefault(describe.getApiName()+".field."+field+".option."+option.get("value"),0, arg.getLanguage(),option.get("label").toString()));

                            }
                        }
                    }
                }
            });
            d.setNeedUpdObjectData(data);
        });
    }

    public String getI18nLable(String code){

        return "sfa.activity.newbusiness.abstract."+code;
    }


    /**
     * 过滤特殊字符
     * @param departmentResult
     * @param uniqueSign
     * @return
     */
    public void filterSpecialCharacter(List<SFANewBusinessModel.Result> departmentResult, String uniqueSign){
        if(CollectionUtils.isEmpty(departmentResult)){
            return ;
        }
        if(CollectionUtil.isEmpty(NEWBUSINESS_FILTER_WORDS)){
            return ;
        }
        departmentResult.removeIf(x->filterSpecialCharacter(x,uniqueSign));
    }

    public boolean filterSpecialCharacter(SFANewBusinessModel.Result x,String uniqueSign ){

        String context = x.getAbstracts().stream()
                .filter(abstracts -> uniqueSign.equals(abstracts.getUniqueSign()))
                .findFirst()
                .map(SFANewBusinessModel.Abstracts::getContext)
                .orElse("");
        // 空值保护与提前返回
        if (StringUtils.isBlank(context)) {
            log.warn("Context not found for uniqueSign: {}", uniqueSign);
            return false;
        }
        boolean flag = false;
        for(String filterWord:NEWBUSINESS_FILTER_WORDS){
            if(context.contains(filterWord)){
                flag = true;
                break;
            }
        }
        return flag;
    }

    public String getRedisKey(User user,InteractionModel.Arg arg,String flag){
        return flag + user.getTenantId()+arg.getActiveRecordId();
    }

    /**
     * 保存数据
     * @param systemUser
     * @param resultList
     * @param nameField 名称字段
     * @param linkField  关联其他对象的关联字段如 客户：account_id
     * @param activeRecordData 销售记录
     */
    public void saveData(User systemUser,List<SFANewBusinessModel.Result> resultList,String nameField,String linkField,
                         IObjectData activeRecordData,String saveDataObjApiName){
        if(CollectionUtil.isEmpty(resultList)){
            return;
        }
        List<IObjectData> insertDataList = new ArrayList<>();
        IObjectDescribe describeBusiness = AccountPathUtil.getObjectDescribe(systemUser, saveDataObjApiName);
        resultList.stream().forEach(result->{
            IObjectData objectData = new  ObjectData();
            if(CollectionUtil.isNotEmpty(result.getAbstracts())){
                result.getAbstracts().stream().forEach(abstractsData->{
                    objectData.set(abstractsData.getUniqueSign(),abstractsData.getContext());
                });
            }
            objectData.set(nameField,result.getNeedUpdObjectData().get(nameField));
            objectData.set(AIQuestionConstants.Field.active_record_id, activeRecordData.getId());
            objectData.set(linkField,result.getId());
            objectData.set("object_describe_api_name", saveDataObjApiName);
            objectData.set(Tenantable.TENANT_ID, systemUser.getTenantId());
            objectData.set("owner", activeRecordData.getOwner());
            objectData.set("record_type", "default__c");
            objectData.set("summary_json", result);
            insertDataList.add(objectData);
        });
        serviceFacade.bulkSaveObjectData(insertDataList,systemUser);
        LogUtil.recordLogs(systemUser, insertDataList,describeBusiness, EventType.ADD, ActionType.Add);
    }


    public void refreshData(String tenantId){
        int index = 0;
        int limit = 1000;
        User systemUser = new User(tenantId, CommonConstant.SUPER_USER);
        for(int i = 0 ; i<10000;i++){
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(limit);
            searchTemplateQuery.setFindExplicitTotalNum(false);
            searchTemplateQuery.setNeedReturnCountNum(false);
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy(IObjectData.CREATE_TIME, false)));
            searchTemplateQuery.setOffset(index);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(systemUser, "ActivityBusinessObj", searchTemplateQuery);
            if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
                log.warn("queryResult is empty");
                return;
            }
            queryResult.getData().stream().forEach(objectData->{
                IObjectData activeRecordData = new ObjectData();
                activeRecordData.setId(objectData.get("active_record_id",String.class));
                activeRecordData.setOwner(objectData.getOwner());
               if(objectData.containsField(SFANewBusinessModel.Field.ACCOUNT_ABSTRACT)){
                    List<SFANewBusinessModel.Result> accountResult =  JSONObject.parseArray(JSONObject.toJSONString(objectData.get(SFANewBusinessModel.Field.ACCOUNT_ABSTRACT)),SFANewBusinessModel.Result.class);
                   saveData(systemUser,accountResult,"name","account_id",activeRecordData,CommonConstant.ACTIVITY_ACCOUNT_SUMMARY_OBJ);
                }
                if(objectData.containsField(SFANewBusinessModel.Field.NEW_OPPORTUNITY_ABSTRACT)){
                    List<SFANewBusinessModel.Result> newOpportunityResult =  JSONObject.parseArray(JSONObject.toJSONString(objectData.get(SFANewBusinessModel.Field.NEW_OPPORTUNITY_ABSTRACT)),SFANewBusinessModel.Result.class);
                    saveData(systemUser,newOpportunityResult,"name","new_opportunity_id",activeRecordData,CommonConstant.ACTIVITY_OPPORTUNITY_SUMMARY_OBJ);

                }
                if(objectData.containsField(SFANewBusinessModel.Field.CONTACT_ABSTRACT)){
                    List<SFANewBusinessModel.Result> contactResult =  JSONObject.parseArray(JSONObject.toJSONString(objectData.get(SFANewBusinessModel.Field.CONTACT_ABSTRACT)),SFANewBusinessModel.Result.class);
                    saveData(systemUser,contactResult,"name","contact_id",activeRecordData,CommonConstant.ACTIVITY_CONTACT_SUMMARY_OBJ);
                }
                if(objectData.containsField(SFANewBusinessModel.Field.ACCOUNT_DEPARTMENT_ABSTRACT)){
                    List<SFANewBusinessModel.Result> departmentResult =  JSONObject.parseArray(JSONObject.toJSONString(objectData.get(SFANewBusinessModel.Field.ACCOUNT_DEPARTMENT_ABSTRACT)),SFANewBusinessModel.Result.class);
                    saveData(systemUser,departmentResult,"name","account_department_id",activeRecordData,CommonConstant.ACTIVITY_DEPARTMENT_SUMMARY_OBJ);
                }
            });

            if(queryResult.getData().size()<limit){
                return;
            }
            index = index+limit;

        }
    }
}
