package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.OpenAIChatComplete;
import com.facishare.ai.api.expcetion.ServiceException;
import com.facishare.ai.api.model.Message;
import com.facishare.ai.api.model.service.Openai;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.task.sfa.activitysummary.enums.ActivitySummaryTypeEnum;
import com.facishare.crm.task.sfa.activitysummary.enums.FeedTypeEnum;
import com.facishare.crm.task.sfa.activitysummary.enums.LanguageEnum;
import com.facishare.crm.task.sfa.activitysummary.model.ActivitySummaryParams;
import com.facishare.crm.task.sfa.activitysummary.model.CRMFeedConstants.Field;
import com.facishare.crm.task.sfa.activitysummary.model.SocialAddActionVo;
import com.facishare.crm.task.sfa.activitysummary.model.ViewDataFilterConfig;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.common.constants.SystemConstants;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.model.FeedResult;
import com.facishare.crm.task.sfa.model.SearchFeedModel;
import com.facishare.crm.task.sfa.model.proxy.bi.BiCrmRestQuery;
import com.facishare.crm.task.sfa.model.proxy.bi.StatViewDataQuery;
import com.facishare.crm.task.sfa.rest.AiRestProxy;
import com.facishare.crm.task.sfa.rest.BiCrmRestProxy;
import com.facishare.crm.task.sfa.rest.SocialFeedProxy;
import com.facishare.crm.task.sfa.util.ActivityUtils;
import com.facishare.crm.task.sfa.util.HttpHeaderUtil;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.qixin.api.model.AuthInfo;
import com.facishare.qixin.api.model.EnterpriseEnv;
import com.facishare.qixin.api.model.message.MessageItem;
import com.facishare.qixin.api.model.message.arg.GetMessagesByTimeRangeArg;
import com.facishare.qixin.api.model.message.result.GetMessagesByTimeRangeResult;
import com.facishare.qixin.api.model.session.arg.FindAccountSessionApiArg;
import com.facishare.qixin.api.model.session.result.FindAccountSessionApiResult;
import com.facishare.qixin.api.service.MessageService;
import com.facishare.qixin.api.service.SessionService;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.openapi.Utils.ACTIVE_RECORD_API_NAME;
import static com.facishare.crm.task.sfa.activitysummary.enums.FeedTypeEnum.SALES_RECORD;
import static com.facishare.crm.task.sfa.common.constants.CommonConstant.CRM_APP_ID;
import static com.facishare.crm.task.sfa.rest.BiCrmRestProxy.getHeaders;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/6 16:57
 * @description:
 */
@Service
@Slf4j
public class ActivitySummaryService {


    @Autowired
    private CompletionsService completions;
    @Autowired
    private SocialFeedProxy socialFeedProxy;

    @Autowired
    private Openai openai;

    @Autowired
    private ServiceFacade serviceFacade;

    @Resource
    private BiCrmRestProxy biCrmRestProxy;

    @Resource
    private MessageService messageService;

    @Autowired
    private GDSHandler gdsHandler;

    @Autowired
    private OrgService orgService;

    @Autowired
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;

    @Resource
    @Qualifier("remoteSessionService")
    private SessionService remoteSessionService;

    private static String prompt = "你是一个有用的助手，帮我总结一下本周的客户跟进动态，总结字数控制在200字以内，总结内容清晰、简洁、突出重点。下面的内容为本周跟进动态： ";// ignoreI18n
    private static String customerGroupPrompt = "你是一个有用的助手，帮我总结一下本周的客户群消息总结，返回200字以内。下面的内容为本周群成员的消息内容： ";// ignoreI18n
    private static Integer limit = 10;

    // 发销售记录的人员，格式： 企业id.用户id|企业id.用户id
    private static Map<String, String> tenantUserId = Maps.newHashMap();
    // 企业业务类型
    private static Map<String, String> tenantRecordType = Maps.newHashMap();
    // 企业跟进类型
    private static Map<String, String> tenantActiveRecordType = Maps.newHashMap();
    private static Integer imageNum = 3;
    private static Splitter CONFIG_SPLITTER = Splitter.on("|").omitEmptyStrings();
    private static Splitter CONFIG_DOT_SPLITTER = Splitter.on(".").omitEmptyStrings();

    private static Integer customer_group_message_limit = 50;
    private static Integer feed_count_limit = 3;
    private static Integer feed_text_limit = 50;
    private static String get_feed_user_id = "-1000";
    private static String openai_model = "gpt-3.5-turbo";

    private static Long start_time = 0L;

    public static Map<String, ViewDataFilterConfig.ViewDataFilter> viewDataFilterMap = new HashMap<>();

    public static boolean SFA_ACTIVITY_CORPORA_BY_MONGGO = false;

    static {
        ConfigFactory.getConfig("fs-gray-sfa-follow", config -> {
            prompt = config.get("prompt", prompt);
            customerGroupPrompt = config.get("customer_group_prompt", customerGroupPrompt);
            limit = config.getInt("feed_page_limit", 20);
            tenantUserId = CONFIG_SPLITTER.splitToList(config.get("tenant_user_id", "1.1000")).stream().
                    collect(Collectors.toMap(x -> CONFIG_DOT_SPLITTER.splitToList(x).get(0),
                            x -> CONFIG_DOT_SPLITTER.splitToList(x).get(1)));

            tenantRecordType = CONFIG_SPLITTER.splitToList(config.get("tenant_record_type", "90116.record_z336r__c")).stream().
                    collect(Collectors.toMap(x -> CONFIG_DOT_SPLITTER.splitToList(x).get(0),
                            x -> CONFIG_DOT_SPLITTER.splitToList(x).get(1)));
            tenantActiveRecordType = CONFIG_SPLITTER.splitToList(config.get("tenant_active_record_type", "90116.0171d325d10945ee966f90b176f68ce3")).stream().
                    collect(Collectors.toMap(x -> CONFIG_DOT_SPLITTER.splitToList(x).get(0),
                            x -> CONFIG_DOT_SPLITTER.splitToList(x).get(1)));
            imageNum = config.getInt("imageNum", 3);
            customer_group_message_limit = config.getInt("customer_group_message_limit", 50);
            feed_count_limit = config.getInt("feed_count_limit", 3);
            feed_text_limit = config.getInt("feed_text_limit", 50);
            get_feed_user_id = config.get("get_feed_user_id", "-10000");
            openai_model = config.get("openai_model", "gpt-3.5-turbo");
            start_time = config.getLong("start_time", 0L);
            List<ViewDataFilterConfig.ViewDataFilter> viewDataFilter = JSONObject.parseArray(config.get("view_data_filter"), ViewDataFilterConfig.ViewDataFilter.class);
            viewDataFilterMap = viewDataFilter.stream().collect(Collectors.toMap(ViewDataFilterConfig.ViewDataFilter::getTenantId, x -> x));
        });

        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            SFA_ACTIVITY_CORPORA_BY_MONGGO = config.getBool("sfa_activity_corpora_by_monggo", false);
        });
    }


    @SFAAuditLog(bizName = "activity_summary", status = "#status", objectIds = "#objectIds", extra = "#extra", message = "#msg")
    public void processAllSummary(String tenantId, String objectId, String sessionId) {
        int activityCount = 0;
        SFALogContext.putVariable("objectIds", objectId);
        SFALogContext.putVariable("extra", sessionId);
        List<ActivitySummaryParams.ActivitySummary> activitySummaries = getFollowSummary(tenantId, objectId);
        ActivitySummaryParams.ActivitySummary customerGroupSummary = getCustomerGroupSummary(tenantId, objectId, sessionId);
        if (CollectionUtils.isEmpty(activitySummaries) && CollectionUtils.isEmpty(customerGroupSummary.getSummaryDetail())) {
            SFALogContext.putVariable("status", "false");
            return;
        }
        if (CollectionUtils.isNotEmpty(activitySummaries)) {
            activityCount++;
        }
        if (CollectionUtils.isNotEmpty(customerGroupSummary.getSummaryDetail())) {
            activitySummaries.add(customerGroupSummary);
            activityCount++;
        }

        // 业务动态 调用BI查询数据
        List<ActivitySummaryParams.ActivitySummary> businessSummary = getBusinessSummary(tenantId, objectId);
        if (CollectionUtils.isNotEmpty(businessSummary.get(0).getSummaryDetail())) {
            activitySummaries.addAll(businessSummary);
            activityCount++;
        }
        // 客户群动态
        if (activityCount < 2) {
            SFALogContext.putVariable("msg", "activityCount < 2: " + activityCount);
            SFALogContext.putVariable("status", "false");
            log.warn("activityCount < 2,tenantId:{},objectId:{}, sessionId:{},activityCount:{}", tenantId, objectId, sessionId, activityCount);
            return;
        }
        SFALogContext.putVariable("status", "true");
        SFALogContext.putVariable("msg", activitySummaries.toString());
        saveActiveRecord(tenantId, objectId, activitySummaries);
    }

    public List<ActivitySummaryParams.ActivitySummary> getFollowSummary(String tenantId, String objectId) {
        long weekStartTime = getThisWeekStartTime();
        long weekEndTime = getThisWeekEndTime();
        FeedResult.Result socialFeed = getSocialFeed(tenantId, limit, objectId, weekStartTime, weekEndTime);
        List<ActivitySummaryParams.ActivitySummary> activitySummaries = new ArrayList<>();
        if (socialFeed == null || CollectionUtils.isEmpty(socialFeed.getFeedList()) || socialFeed.getFeedList().size() < feed_count_limit) {
            log.warn("socialFeed is empty or size < 3,tenantId:{},objectId:{},feed:{}", tenantId, objectId, socialFeed);
            return activitySummaries;
        }
        log.debug("socialFeed:{}", socialFeed);
        List<ActivitySummaryParams.ActivitySummary> feedContent = getSocialFeedContent(tenantId, socialFeed, SALES_RECORD.getCode());
        List<ActivitySummaryParams.ActivitySummary> checkinsContent = getSocialFeedContent(tenantId, socialFeed, FeedTypeEnum.OUTSIDE_WORK.getCode());
        if (CollectionUtils.isNotEmpty(checkinsContent)) {
            // 合并外勤和销售记录 到销售记录
            mergeFeedCheckinsContent(feedContent, checkinsContent);
            activitySummaries.addAll(checkinsContent);
        }
        if (CollectionUtils.isNotEmpty(feedContent)) {
            // 总结销售记录文本
            summary(tenantId, feedContent);
            activitySummaries.addAll(feedContent);
        }
        List<ActivitySummaryParams.ActivitySummary> logContent = getSocialFeedContent(tenantId, socialFeed, FeedTypeEnum.LOG.getCode());
        if (CollectionUtils.isNotEmpty(logContent)) {
            summary(tenantId, logContent);
            activitySummaries.addAll(logContent);
        }
        List<ActivitySummaryParams.ActivitySummary> shareContent = getSocialFeedContent(tenantId, socialFeed, FeedTypeEnum.SHARE.getCode());
        if (CollectionUtils.isNotEmpty(shareContent)) {
            summary(tenantId, shareContent);
            activitySummaries.addAll(shareContent);
        }
        log.info("feedContent:{}", activitySummaries);
        return activitySummaries;
    }


    private ActivitySummaryParams.ActivitySummary getCustomerGroupSummary(String tenantId, String objectId, String sessionId) {
        String string = getMessages(tenantId, "-10000", getThisWeekStartTime(), getThisWeekEndTime(), sessionId);
        ActivitySummaryParams.ActivitySummary activitySummary = new ActivitySummaryParams.ActivitySummary();
        if (StringUtils.isBlank(string)) {
            return activitySummary;
        }
        ActivitySummaryParams.SummaryDetail detail = new ActivitySummaryParams.SummaryDetail();
        String summarized = summaryActivityByOpenAI(tenantId, customerGroupPrompt, string);
        detail.setApiName("customer_group_summary");
        detail.setSummary(summarized);
        activitySummary.setActivityType(ActivitySummaryTypeEnum.CUSTOMER_GROUP.getType());
        activitySummary.setSummaryDetail(Lists.newArrayList(detail));
        return activitySummary;
    }

    private void mergeFeedCheckinsContent(List<ActivitySummaryParams.ActivitySummary> feedContent, List<ActivitySummaryParams.ActivitySummary> checkinsContent) {
        for (ActivitySummaryParams.ActivitySummary activitySummary : checkinsContent) {
            for (ActivitySummaryParams.ActivitySummary feed : feedContent) {
                if (feed.getOwner().equals(activitySummary.getOwner())) {
                    String summary = feed.getSummaryDetail().get(0).getSummary();
                    String checkinContent = activitySummary.getSummaryDetail().get(0).getSummary();
                    feed.getSummaryDetail().get(0).setSummary(summary + "\n ---- \n 外勤内容： \n " + checkinContent);
                    // feed 图片不够两张，并且外勤有图片，把外勤图片给feed
                    if (CollectionUtils.size(feed.getSummaryDetail().get(0).getImage()) < 2 && CollectionUtils.isNotEmpty(activitySummary.getSummaryDetail().get(0).getImage())) {
                        if (CollectionUtils.isEmpty(feed.getSummaryDetail().get(0).getImage())) {
                            feed.getSummaryDetail().get(0).setImage(activitySummary.getSummaryDetail().get(0).getImage());
                        } else {
                            feed.getSummaryDetail().get(0).getImage().add(activitySummary.getSummaryDetail().get(0).getImage().stream().findFirst().get());
                        }
                    } else if (CollectionUtils.size(feed.getSummaryDetail().get(0).getImage()) >= 2 && CollectionUtils.size(activitySummary.getSummaryDetail().get(0).getImage()) >= 1) {
                        // 图片各取一张
                        feed.getSummaryDetail().get(0).setImage(Sets.newHashSet(feed.getSummaryDetail().get(0).getImage().stream().findFirst().get(),
                                activitySummary.getSummaryDetail().get(0).getImage().stream().findFirst().get()));
                    }
                    activitySummary.setSummaryDetail(Lists.newArrayList());
                    break;
                }
            }
        }
    }


    private void summary(String tenantId, List<ActivitySummaryParams.ActivitySummary> activitySummaries) {
        for (ActivitySummaryParams.ActivitySummary activitySummary : activitySummaries) {
            String summary = activitySummary.getSummaryDetail().get(0).getSummary();
            String summaryActivityByOpenAI = summaryActivityByOpenAI(tenantId, prompt, summary);
            activitySummary.getSummaryDetail().get(0).setSummary(summaryActivityByOpenAI);
        }
    }

    public String summaryActivityByOpenAI(String tenantId, String prompt, String text) {
        BaseArgument baseArgument = new BaseArgument();
        baseArgument.setTenantId(tenantId);
        baseArgument.setBusiness("sfa_ai");
        OpenAIChatComplete.Arg arg = new OpenAIChatComplete.Arg();
        arg.setModel(openai_model);
        arg.setMessages(getMessage(prompt, text));
        arg.setUser_id("-10000");
        arg.setStream(Boolean.FALSE);
        OpenAIChatComplete.Result result = null;
        try {
            result = openai.chatComplete(baseArgument, arg);
        } catch (ServiceException e) {
            log.error("summaryActivityByOpenAI error msg is {}",e.getMessage());
            return "";
        }
        log.info("result:{}", result);
        return result.getMessage();
    }
    public String summaryActivityByOpenAI(String tenantId, String prompt, String text,String openaiModel) {
        BaseArgument baseArgument = new BaseArgument();
        baseArgument.setTenantId(tenantId);
        baseArgument.setBusiness("sfa_ai");
        OpenAIChatComplete.Arg arg = new OpenAIChatComplete.Arg();
        if(ObjectUtils.isNotEmpty(openaiModel)){
            arg.setModel(openaiModel);
        }else{
            arg.setModel(openai_model);
        }
        arg.setMessages(getMessage(prompt, text));
        arg.setUser_id("-10000");
        arg.setStream(Boolean.FALSE);
        OpenAIChatComplete.Result result = null;
        try {
            result = openai.chatComplete(baseArgument, arg);
        } catch (ServiceException e) {
            log.error("summaryActivityByOpenAI error msg is {}",e.getMessage());
            return "";
        }
        log.info("result:{}", result);
        return result.getMessage();
    }


    private List<Message> getMessage(String prompt, String text) {
        List<Message> messages = new ArrayList<>();
        Message message = new Message();
        message.setRole("system");
        message.setContent(prompt);
        messages.add(message);
        Message message2 = new Message();
        message2.setRole("user");
        message2.setContent(text);
        messages.add(message2);
        return messages;
    }

    private List<ActivitySummaryParams.ActivitySummary> getSocialFeedContent(String tenantId, FeedResult.Result result, Integer feedType) {
        if (result == null || CollectionUtils.isEmpty(result.getFeedList())) {
            return null;
        }
        List<ActivitySummaryParams.ActivitySummary> activitySummaries = new ArrayList<>();
        List<FeedResult.FeedList> feedLists = result.getFeedList().stream().filter(feedList -> feedList.getFeedType().equals(feedType)).collect(Collectors.toList());
        Map<Integer, ActivitySummaryParams.ActivitySummary> userParamsMap = new HashMap<>();
        // feed 数量 < 3条 不做处理
        if (CollectionUtils.isEmpty(feedLists)) {
            return activitySummaries;
        }
        for (FeedResult.FeedList feed : feedLists) {
            Integer userIdTemp = feed.getFeedProfile().getJSONObject("head").getInteger("userId");
            // 过滤掉 AI 发出的消息
            if (userIdTemp == null || tenantUserId.get(tenantId).equals(userIdTemp.toString())) {
                log.info("userIdTemp is null or skip userId,tenantId:{},{},feed:{}", tenantId, userIdTemp, feed);
                continue;
            }

            StringBuilder sb = new StringBuilder();
            Set<String> imagePath = new HashSet<>();
            if (FeedTypeEnum.LOG.getCode().equals(feedType)) {
                getStringAndImageLog(feed.getBizDataArea(), sb, imagePath);
            } else {
                List<JSONObject> simpleContent = feed.getBizDataArea().stream().filter(bizDataArea -> bizDataArea.getString("cmpt").equals("SIMPLE_CONTENT")).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(simpleContent)) {
                    continue;
                }
                getStringAndImage(simpleContent, sb, imagePath);
            }
            // 字符数量小于 50 则跳过该 feed
            if (sb.length() < feed_text_limit) {
                continue;
            }
            ActivitySummaryParams.ActivitySummary activitySummary = new ActivitySummaryParams.ActivitySummary();
            if (userParamsMap.get(userIdTemp) == null) {
                activitySummary.setOwner(userIdTemp.toString());
                userParamsMap.put(userIdTemp, activitySummary);
            } else {
                activitySummary = userParamsMap.get(userIdTemp);
            }
            activitySummary.setActivityType(ActivitySummaryTypeEnum.FOLLOW.getType());
            ActivitySummaryParams.SummaryDetail detail;
            if (activitySummary.getSummaryDetail() == null) {
                detail = new ActivitySummaryParams.SummaryDetail();
                activitySummary.setSummaryDetail(Lists.newArrayList(detail));
            } else {
                detail = activitySummary.getSummaryDetail().get(0);
            }
            if (detail.getSummary() == null) {
                detail.setSummary(sb.toString());
            } else {
                detail.setSummary(detail.getSummary() + sb);
            }

            if (detail.getImage() == null || detail.getImage().isEmpty()) {
                detail.setImage(imagePath);
            } else if (CollectionUtils.size(detail.getImage()) == 1 && CollectionUtils.size(imagePath) > 0) {
                // 获取imagePath的第一张图片
                detail.getImage().addAll(imagePath.stream().limit(1).collect(Collectors.toSet()));
            }
            if (FeedTypeEnum.getByCode(feedType) != null) {
                activitySummary.setApiName(getCountApiNameByFeedType(FeedTypeEnum.getByCode(feedType)));
                detail.setApiName(getApiNameByFeedType(FeedTypeEnum.getByCode(feedType)));
            }
            activitySummary.setCount(activitySummary.getCount() == null ? 1 : activitySummary.getCount() + 1);
        }
        activitySummaries.addAll(userParamsMap.values());
        return activitySummaries;
    }

    private void getStringAndImageLog(List<JSONObject> bizDataArea, StringBuilder sb, Set<String> imagePath) {
        if (CollectionUtils.isEmpty(bizDataArea)) {
            return;
        }
        for (JSONObject x : bizDataArea) {
            if (x.getJSONObject("content").getString("content") != null) {
                sb.append(x.getJSONObject("content").getString("content"));
                if (x.getJSONObject("content").get("__xt") == null) {
                    // 一个字的直接跳过。
                    continue;
                }
                JSONObject jsonObject = x.getJSONObject("content").getJSONObject("__xt").getJSONObject("__json");
                if ("doc".equalsIgnoreCase(jsonObject.getString("type"))) {
                    JSONArray content = jsonObject.getJSONArray("content");
                    if (content != null && content.size() > 0) {
                        for (int i = 0; i < content.size(); i++) {
                            JSONObject jsonObject1 = content.getJSONObject(i);
                            JSONArray content1 = jsonObject1.getJSONArray("content");
                            if (content1 != null) {
                                for (Object c : content1) {
                                    LinkedTreeMap<String, Object> jsonObject2 = (LinkedTreeMap) c;
                                    if ("image".equals(jsonObject2.get("type"))) {
                                        if (imagePath.size() < imageNum) {
                                            imagePath.add(((LinkedTreeMap) jsonObject2.get("attrs")).get("src").toString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private static void getStringAndImage(List<JSONObject> simpleContent, StringBuilder sb, Set<String> imagePath) {
        if (CollectionUtils.isNotEmpty(simpleContent)) {
            for (JSONObject x : simpleContent) {
                if ("XT_TEXT".equals(x.getJSONObject("content").get("cmpt"))) {
                    if (x.getJSONObject("content").getString("content") != null) {
                        if (x.getJSONObject("content").getJSONObject("__xt") == null) {
                            sb.append(x.getJSONObject("content").getString("content"));
                            continue;
                        }
                        JSONObject jsonObject = x.getJSONObject("content").getJSONObject("__xt").getJSONObject("__json");
                        if ("doc".equalsIgnoreCase(jsonObject.getString("type"))) {
                            JSONArray content = jsonObject.getJSONArray("content");
                            if (content != null && content.size() > 0) {
                                for (int i = 0; i < content.size(); i++) {
                                    JSONObject jsonObject1 = content.getJSONObject(i);
                                    JSONArray content1 = jsonObject1.getJSONArray("content");
                                    if (content1 != null) {
                                        for (Object c : content1) {
                                            LinkedTreeMap<String, Object> jsonObject2 = (LinkedTreeMap) c;
                                            if ("image".equals(jsonObject2.get("type"))) {
                                                if (imagePath.size() < imageNum) {
                                                    imagePath.add(((LinkedTreeMap) jsonObject2.get("attrs")).get("src").toString());
                                                }
                                            } else if ("text".equals(jsonObject2.get("type"))) {
                                                sb.append(jsonObject2.get("text").toString());
                                            }
                                        }
                                    }
                                }
                            }
                            continue;
                        }
                        // 非富文本的内容处理
                        sb.append(x.getJSONObject("content").getString("content"));
                        extractedImagePath(imagePath, x);
                    } else if (x.getJSONObject("content").getString("element") != null) {
                        extractedImagePath(imagePath, x);
                    }
                }
            }
        }
    }

    private static void extractedImagePath(Set<String> imagePath, JSONObject x) {
        JSONObject element = x.getJSONObject("content").getJSONObject("element");
        if (element != null && element.getJSONObject("attachment") != null) {
            JSONObject attachments = element.getJSONObject("attachment").getJSONObject("attachments");
            if (attachments != null) {
                JSONArray images = attachments.getJSONArray("IMAGE");
                if (images != null && !images.isEmpty()) {
                    // 获取 jsonarray 里面的每个 path ：path :"N_202312_26_be36fb8214694241b8e6782c5aeb6974"
                    for (int i = 0; i < Math.min(images.size(), 2); i++) {
                        JSONObject image = images.getJSONObject(i);
                        String path = image.getString("path");
                        imagePath.add(path);
                    }
                }
            }
        }
    }


    private List<ActivitySummaryParams.ActivitySummary> getBusinessSummary(String tenantId, String objectId) {
        StatViewDataQuery.ResultData resultData = queryViewData(tenantId, objectId);
        List<List<JSONObject>> dataSet = resultData.getDataSet();
        List<ActivitySummaryParams.SummaryDetail> details = new ArrayList<>();
        List<String> apiNames = Lists.newArrayList("sales_order_amount", "payment_amount", "ticket_count", "feedback_count", "bug_count");
        if (CollectionUtils.isNotEmpty(dataSet)) {
            int i = -1;
            for (JSONObject jsonObjects : dataSet.get(0)) {
                i++;
                if (jsonObjects.getString("formattedShowValue") == null || "0.00".equals(jsonObjects.getString("formattedShowValue"))
                        || "0".equals(jsonObjects.getString("formattedShowValue"))) {
                    continue;
                }
                ActivitySummaryParams.SummaryDetail detail = new ActivitySummaryParams.SummaryDetail();
                detail.setSummary(jsonObjects.getString("formattedShowValue"));
                detail.setApiName(apiNames.get(i));
                details.add(detail);
            }
        }
        List<ActivitySummaryParams.ActivitySummary> businessSummary = new ArrayList<>();
        ActivitySummaryParams.ActivitySummary activitySummary = new ActivitySummaryParams.ActivitySummary();
        activitySummary.setSummaryDetail(details);
        activitySummary.setActivityType(ActivitySummaryTypeEnum.BUSINESS.getType());
        businessSummary.add(activitySummary);
        return businessSummary;
    }


    private String getApiNameByFeedType(FeedTypeEnum feedType) {
        switch (feedType) {
            case SALES_RECORD:
            case OUTSIDE_WORK:
                return "active_record_summary";
            case SHARE:
                return "blog_summary";
            case LOG:
                return "journal_summary";
            default:
                return "";
        }
    }


    private String getCountApiNameByFeedType(FeedTypeEnum feedType) {
        switch (feedType) {
            case SALES_RECORD:
                return "active_record_count";
            case OUTSIDE_WORK:
                return "checkins_count";
            case SHARE:
                return "blog_count";
            case LOG:
                return "journal_count";
            default:
                return "";
        }
    }

    private FeedResult.Result getSocialFeed(String tenantId, Integer limit, String objectId, Long startTime, Long endTime) {
        Map<String, String> headers = HttpHeaderUtil.getHeaders(new User(tenantId, get_feed_user_id), CRM_APP_ID);
        headers.put("x-fs-enterprise-account", "shengtai");
        SearchFeedModel.Arg arg = buildSearchFeedModelArg(limit, objectId, startTime, endTime);
        FeedResult.Result particularResult = null;
        try {
            particularResult = socialFeedProxy.searchFeedResourceList(arg, headers);
        } catch (Exception e) {
            log.error("getSocialFeed error, tenantId:{}, arg:{}, headers:{}", tenantId, arg, headers, e);
        }
        return particularResult;
    }

    private SearchFeedModel.Arg buildSearchFeedModelArg(Integer limit, String objectId, Long startTime, Long endTime) {
        SearchFeedModel.Arg arg = SearchFeedModel.Arg.builder()
                .resourceId("AccountObj|" + objectId)
                .limit(limit)
                .searchArg(buildSearchArg(startTime, endTime))
                .build();
        return arg;
    }

    private SearchFeedModel.SearchArg buildSearchArg(Long startTime, Long endTime) {
        SearchFeedModel.SearchArg searchArg = SearchFeedModel.SearchArg.builder()
                .feedType(0)
                .subType(0)
                .startTime(startTime)
                .endTime(endTime)
                .senderId("")
                .keyword("")
                .build();
        return searchArg;
    }

    private StatViewDataQuery.ResultData queryViewData(String tenantId, String objectId) {
        StatViewDataQuery.Arg arg = new StatViewDataQuery.Arg();
        List<BiCrmRestQuery.Filter> filterList = new ArrayList<>();
        BiCrmRestQuery.Filter filter = new BiCrmRestQuery.Filter();
        filter.setFilterId(viewDataFilterMap.get(tenantId).getDateFilterId());
        filter.setDateRangeId(2);
        filterList.add(filter);
        BiCrmRestQuery.Filter filter2 = new BiCrmRestQuery.Filter();
        filter2.setFilterId(viewDataFilterMap.get(tenantId).getObjectFilterId());
        filter2.setValue1("[{\"optionCode\":\"" + objectId + "\"}]");
        filter2.setOperator(26);
        filterList.add(filter2);
        arg.setViewId(viewDataFilterMap.get(tenantId).getViewId());
        arg.setShowMode(1);
        arg.setFilterList(filterList);
        arg.setPageSize(200);
        StatViewDataQuery.ResultData resultData = queryViewData(tenantId, arg);
        return resultData;
    }


    private StatViewDataQuery.ResultData queryViewData(String tenantId, StatViewDataQuery.Arg arg) {
        StatViewDataQuery.Result result = biCrmRestProxy.queryViewData(arg, getHeaders(tenantId));
        if (result == null || result.getCode() == null || result.getCode() != 200) {
            log.warn("queryViewData error, tenantId:{}, arg:{}, result:{}", tenantId, arg, result);
        }
        return result.getData();
    }

    public static long getThisWeekStartTime() {
        if (start_time != 0) {
            return start_time;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    public static long getThisWeekEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek() + 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTimeInMillis();
    }


    public void saveActiveRecord(String tenantId, String objectId, List<ActivitySummaryParams.ActivitySummary> activitySummaries) {
        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName(ACTIVE_RECORD_API_NAME);
        objectData.setCreatedBy("-10000");
        objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        Map<String, Object> activeRecordContent = Maps.newHashMap();
        Map<String, Object> paragraph = new HashMap<>();
        paragraph.put("type", "paragraph");
        Map<String, Object> attrs = new HashMap<>();
        attrs.put("textAlign", "left");
        //Map<String, Object> content = new HashMap<>();
        //content.put("text","");
        //content.put("type", "text");
        //paragraph.put("content", Lists.newArrayList(content));
        paragraph.put("content", Lists.newArrayList());
        paragraph.put("attrs", attrs);
        List<Object> contentList = Lists.newArrayList();
        contentList.add(paragraph);
        contentList.add(getContent(activitySummaries));

        Map<String, Object> doc = Maps.newHashMap();
        doc.put(Field.TYPE, "doc");
        doc.put(Field.CONTENT, contentList);

        Map<String, Object> json = Maps.newHashMap();
        json.put(Field.__json, doc);
        activeRecordContent.put(Field.__xt, json);
        objectData.set(Field.ACTIVE_RECORD_CONTENT, activeRecordContent);
        Map<String, Object> relatedObject = Maps.newHashMap();
        relatedObject.put(Utils.ACCOUNT_API_NAME, Lists.newArrayList(objectId));
        objectData.set(Field.RELATED_OBJECT, relatedObject);
        objectData.set(Field.ACTIVE_RECORD_TYPE, tenantActiveRecordType.get(tenantId) == null ? "0171d325d10945ee966f90b176f68ce3" : tenantActiveRecordType.get(tenantId));
        // 业务类型
        objectData.set(MultiRecordType.RECORD_TYPE, tenantRecordType.get(tenantId) == null ? MultiRecordType.RECORD_TYPE_DEFAULT : tenantRecordType.get(tenantId));
        SocialAddActionVo.Argument saveArg = new SocialAddActionVo.Argument();
        saveArg.setSource(502);
        saveArg.setObjectData(ObjectDataDocument.of(objectData));
        RequestContext requestContext = RequestContext.builder().user(new User(tenantId, tenantUserId.get(tenantId))).build();
        requestContext.setAttribute(ActionContextKey.SKIP_REQUIRED_VALIDATE, true);
        ActionContext actionContext = new ActionContext(requestContext, ACTIVE_RECORD_API_NAME,
                SystemConstants.ActionCode.Add.getActionCode());
        BaseObjectSaveAction.Result result = serviceFacade.triggerAction(actionContext, saveArg,
                BaseObjectSaveAction.Result.class);
        log.info("saveActiveRecord result:{}", result);
    }


    private ActivitySummaryParams.Content getContent(List<ActivitySummaryParams.ActivitySummary> activitySummaries) {
        ActivitySummaryParams.params params = new ActivitySummaryParams.params(activitySummaries, "客户本周新增动态简报已生成，请查阅");// ignoreI18n
        ActivitySummaryParams.Compname compname = new ActivitySummaryParams.Compname("avatestcomp", "webAIReportComp");
        ActivitySummaryParams.attrs attrs = new ActivitySummaryParams.attrs();
        attrs.setParams(params);
        attrs.setCompname(compname);

        ActivitySummaryParams.Content content = new ActivitySummaryParams.Content();
        content.setType("customComp");
        content.setAttrs(attrs);
        return content;
    }

    private String getMessages(String tenantId, String userId, long startTime, long endTime, String sessionId) {
        StringBuilder sb = new StringBuilder();
        GetMessagesByTimeRangeArg arg = new GetMessagesByTimeRangeArg();
        arg.setSessionId(sessionId);
        arg.setBeginTimestamp(startTime);
        arg.setEndTimestamp(endTime);
        arg.setEnv(EnterpriseEnv.INNER);
        AuthInfo authInfo = new AuthInfo();
        authInfo.setEnterpriseId(Integer.parseInt(tenantId));
        authInfo.setEmployeeId(String.format("E.%s.-10000", gdsHandler.getEAByEI(tenantId)));
        //EmployeeId employeeId = authInfo.getEmployeeId();
        //employeeId.setEnterpriseAccount(tenantId);
        arg.setAuthInfo(authInfo);
        GetMessagesByTimeRangeResult messagesByTimeRange = messageService.getMessagesByTimeRange(arg);
        if (messagesByTimeRange.getMessages() == null) {
            return sb.toString();
        }
        List<MessageItem> messages = messagesByTimeRange.getMessages();
        if (messages != null && messages.size() < customer_group_message_limit) {
            return sb.toString();
        }
        Set<Integer> senderIds = messages.stream().map(MessageItem::getSenderId).collect(Collectors.toSet());
        List<String> senderStrs = senderIds.stream().map(String::valueOf).collect(Collectors.toList());
        Map<String, String> nameMapByIds = orgService.getUserNameMapByIds(tenantId, User.SUPPER_ADMIN_USER_ID, senderStrs);
        for (MessageItem message : messages) {
            sb.append(nameMapByIds.get(String.valueOf(message.getSenderId()))).append("： ").append(message.getContent()).append("\n");
        }
        return sb.toString();
    }

    public List<Map<String, String>> getSession(String tenantId, List<String> dataIds) {
        FindAccountSessionApiArg arg = new FindAccountSessionApiArg();
        arg.setDataIds(dataIds);
        AuthInfo authInfo = new AuthInfo();
        authInfo.setEnterpriseId(Integer.parseInt(tenantId));
        authInfo.setEmployeeId(String.format("E.%s.-10000", gdsHandler.getEAByEI(tenantId)));
        arg.setAuthInfo(authInfo);
        arg.setEnv(EnterpriseEnv.INNER);
        FindAccountSessionApiResult accountObjSession = remoteSessionService.findAccountObjSession(arg);
        log.info("accountObjSession:{}", accountObjSession);
        List<Map<String, String>> sessionInfos = accountObjSession.getSessionInfos();
        return sessionInfos;
    }


    public String getAiComplete(User user,String apiName,Map<String, Object> sceneParamMap,String dataId){
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
        arg.setApiName(apiName);
        if(ObjectUtils.isNotEmpty(sceneParamMap)){
            arg.setSceneVariables(sceneParamMap);
        }
        if(ObjectUtils.isNotEmpty(dataId)){
            arg.setBingObjectDataId(dataId);
        }
        String aiResult = completions.requestCompletion(user,arg);
        if(ObjectUtils.isEmpty(aiResult)){
            log.error("ActivitySummaryService aiRestProxy.completions resposne is null");
            return "";
        }
        return ActivityUtils.replaceStr(aiResult);
    }
    public Map<String, Object> handleAiCompleteLanguage(Map<String, Object> sceneParamMap,String key,String language){
        if(ObjectUtils.isEmpty(sceneParamMap)){
            sceneParamMap = new HashMap<>();
        }
        String languageEnum = LanguageEnum.getByCode(language);
        if(ObjectUtils.isEmpty(key)){
            sceneParamMap.put("language",languageEnum);
        }else{
            sceneParamMap.put(key,languageEnum);
        }
        return sceneParamMap;
    }

    public String captureAiResult(String result){
        if(ObjectUtils.isEmpty(result)){
            return "";
        }
        if(result.startsWith("[{") && result.endsWith("}]")){
            return result;
        }else if(result.startsWith("[") && result.endsWith("]")){
            return result;
        }else if(result.contains("[") && result.contains("]")){
            return result.substring(result.indexOf("["),result.lastIndexOf("]")+1);
        }else if(result.contains("{") && result.contains("}")){
            return result.substring(result.indexOf("{"),result.lastIndexOf("}")+1);
        }else{
            log.warn("captureAiResult error result:{}",result);
        }
        return "";
    }

    public List<IObjectData> getAi(User user,String apiName,Map<String, Object> sceneParamMap,String dataId){
        List<Map> resultList = new ArrayList<>();
        String result = getAiComplete(user,apiName,sceneParamMap,dataId);
        result = captureAiResult(result);
        if(ObjectUtils.isEmpty(result)){
            log.warn("ActivitySummaryService getAi result is null");
            return null;
        }
        try {
            if ( (result.startsWith("[") && result.endsWith("]"))  || (result.startsWith("[{") && result.endsWith("}]")) ) {
                resultList = JSONObject.parseArray(result, Map.class);
            }else if(result.startsWith("{") && result.endsWith("}")){
                try {
                    Map objectData = JSONObject.parseObject(result, Map.class);
                    resultList.add(objectData);
                }catch (Exception e){
                    log.warn("ActivitySummaryService JSONObject.parseObject result：{}",result);
                    return null;
                }
            }else{
                log.warn("ActivitySummaryService getAi result 格式不对 result：{}",result);
                return null;
            }
        }catch (Exception e){
            log.warn("ActivitySummaryService getAi result is error result:{}，e:",result,e);
            return null;
        }
        return resultList.stream().map(x->{
            return ObjectDataExt.of((Map<String,Object>)x);
        }).collect(Collectors.toList());
    }

}
