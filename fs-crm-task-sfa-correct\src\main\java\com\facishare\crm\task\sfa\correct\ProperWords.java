package com.facishare.crm.task.sfa.correct;

import com.facishare.crm.task.sfa.correct.util.Util;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ProperWords {
    final List<ProperWord> chinese4le;
    final List<ProperWord> chinese5;
    final List<ProperWord> chinese6;
    final List<ProperWord> chinese7;
    final List<ProperWord> english;

    public static ProperWords withSim(List<String> properWords, float threshold) {
        List<ProperWord> list = properWords.stream().map(t -> {
            if (Util.isEnglish(t)) {
                return new ProperWord(t, threshold * 0.9f); // 0.85 * 0.9 = 0.765
            } else {
                return new ProperWord(t, threshold);
            }
        }).collect(Collectors.toCollection(() -> new ArrayList<>(properWords.size())));
        return new ProperWords(list);
    }

    public ProperWords(List<ProperWord> properWordWords) {
        chinese4le = new ArrayList<>();
        chinese5 = new ArrayList<>();
        chinese6 = new ArrayList<>();
        chinese7 = new ArrayList<>();
        english = new ArrayList<>();
        for (ProperWord word : properWordWords) {
            if (Util.isEnglish(word.text)) {
                english.add(word);
            } else {
                if (word.text.length() <= 4) {
                    chinese4le.add(word);
                } else if (word.text.length() == 5) {
                    chinese5.add(word);
                } else if (word.text.length() == 6) {
                    chinese6.add(word);
                } else if (word.text.length() == 7) {
                    chinese7.add(word);
                }
            }
        }
    }
}