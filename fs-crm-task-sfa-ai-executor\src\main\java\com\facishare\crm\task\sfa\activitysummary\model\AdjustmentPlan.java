package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 调整计划模型
 * 用于AI Agent的Evaluator组件，提供具体的分段调整建议
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdjustmentPlan {
    /**
     * 需要合并的段落ID列表
     * 指示哪些段落应该被合并，通常是相邻且主题相似的段落
     */
    private List<String> mergeSegments;
    
    /**
     * 需要拆分的段落ID列表
     * 指示哪些段落应该被拆分，通常是过长或包含多个主题的段落
     */
    private List<String> splitSegments;
    
    /**
     * 理想段落数量
     * 根据评估结果建议的最佳段落数量
     */
    private int idealSegmentCount;
    
    /**
     * 重点关注区域列表
     * 下一轮分段时需要特别关注的方面，如内容连贯性、摘要质量等
     */
    private List<String> focusAreas;
}
