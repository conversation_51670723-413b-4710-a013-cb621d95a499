package com.facishare.crm.task.sfa.service.impl;

import com.facishare.crm.sfa.lto.activity.mongo.InteractionStrategyTaskDocument;
import com.facishare.crm.task.sfa.service.StrategyTaskService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 策略任务服务实现类
 * 重构后通过组合方式调用各个独立职责的服务
 */
@Service
@Slf4j
public class StrategyTaskServiceImpl implements StrategyTaskService {

    @Autowired
    private StrategyQueryService strategyQueryService;
    
    @Autowired
    private StrategyStatisticsService strategyStatisticsService;
    
    @Autowired
    private StrategyDataProcessService strategyDataProcessService;

    /**
     * 查询策略
     *
     * @param user 用户
     * @param strategyId 策略ID
     * @return 策略对象
     */
    @Override
    public IObjectData queryStrategy(User user, String strategyId) {
        return strategyQueryService.queryStrategy(user, strategyId);
    }

    /**
     * 查询策略明细
     *
     * @param user 用户
     * @param strategyId 策略ID
     * @return 策略明细列表
     */
    @Override
    public List<IObjectData> queryStrategyDetails(User user, String strategyId) {
        return strategyQueryService.queryStrategyDetails(user, strategyId);
    }

    /**
     * 统计符合策略条件的客户数量
     *
     * @param user 用户
     * @param strategy 策略对象
     * @return 客户数量
     */
    @Override
    public Integer countCustomersByStrategy(User user, IObjectData strategy) {
        return strategyStatisticsService.countCustomersByStrategy(user, strategy);
    }

    /**
     * 处理策略下的客户数据
     *
     * @param user 用户
     * @param strategy 策略对象
     * @param strategyDetails 策略明细列表
     * @param task 任务对象
     * @return 处理的客户数量
     */
    @Override
    public int processCustomersForStrategy(User user, IObjectData strategy, 
                                          List<IObjectData> strategyDetails, 
                                          InteractionStrategyTaskDocument task) {
        return strategyDataProcessService.processCustomersForStrategy(user, strategy, strategyDetails, task);
    }
}