package com.facishare.crm.task.sfa.activitysummary.service;

import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.task.sfa.activitysummary.constant.ActiveRecordConstants;
import com.facishare.crm.task.sfa.activitysummary.model.auditlog.ActivityMessageConverter;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.common.StopWatch;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Optional;

@Service
@Slf4j
@AllArgsConstructor
public class ActivityInteractiveJourneyService {
    private final ServiceFacade serviceFacade;
    private final CompletionsService completions;

    @SFAAuditLog(bizName = "interactive_journey", entityClass = ActivityMessage.class, convertClass = ActivityMessageConverter.class)
    public void consume(ActivityMessage activityMessage) {
        StopWatch stopWatch = new StopWatch("consume");
        String tenantId = activityMessage.getTenantId();
        String objectId = activityMessage.getObjectId();
        if (StringUtils.isAnyBlank(tenantId, objectId)) {
            log.warn("[consume] tenantId or objectId is blank");
            return;
        }
        User user = new User(tenantId, activityMessage.getOpId());
        IObjectData activeRecordData = serviceFacade.findObjectData(user, objectId, CommonConstant.ACTIVE_RECORD_API_NAME);
        stopWatch.lap("find");
        if (activeRecordData == null || activeRecordData.get(ActiveRecordConstants.INTERACTIVE_CONTENT_O) == null) {
            log.warn("[consume] activeRecordData or interactive_content is null");
            return;
        }
        int score = score(user, activeRecordData);
        log.info("[consume] score: {}|{}|{}", tenantId, objectId, score);
        stopWatch.lap("score");
        activeRecordData.set("interactive_scores", score);
        serviceFacade.batchUpdateByFields(user, Collections.singletonList(activeRecordData), Collections.singletonList("interactive_scores"));
        stopWatch.lap("update");
        stopWatch.logSlow();
    }

    private int score(User user, IObjectData activeRecordData) {
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
        arg.setApiName("prompt_sfa_activity_interactive_journey");
        arg.setBingObjectDataId(activeRecordData.getId());
        return Optional.ofNullable(completions.requestCompletionData(user, arg, "", AiResult.class)).map(AiResult::getScore).orElse(0);
    }

    @Data
    private static class AiResult {
        int score;
    }
}
