package com.facishare.crm.task.sfa.bizfeature.service.dao.mongo;


import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

@Data
@Entity(value = "feature_scheduled_task",noClassnameStored = true)
public class FeatureTaskDocument implements Serializable {
    @Id
    private ObjectId id; // MongoDB主键

    @Property("tenantId")
    private String tenantId; // 企业id

    /**
     * 任务状态
     * init-初始化
     * running-执行中
     * finished-执行结束
     * fail-执行失败
     */
    @Property("status")
    private String status;

    @Property("failReason")
    private String failReason; // 失败原因
        @Property("createBy")
    private String createBy; // 创建人

    @Property("createTime")
    private Long createTime; // 创建时间

    @Property("lastModifyTime")
    private Long lastModifyTime; // 最后修改时间
}
