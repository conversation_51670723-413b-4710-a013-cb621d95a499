package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 任务实例常量
 *
 * <AUTHOR>
 */
public interface TaskInstanceConstants {
	/**
     * API名称
     */
	String API_NAME = "TaskInstanceObj";
	/**
     * 任务名称
     */
	String TASK_ID = "task_id";
	/**
     * 节点实例
     */
	String NODE_INSTANCE_ID = "node_instance_id";
	/**
     * 流程节点
     */
	String NODE_ID = "node_id";
	/**
     * 关联对象
     */
	String OBJECT_API_NAME = "object_api_name";
	/**
     * 对象id
     */
	String OBJECT_ID = "object_id";
	/**
     * 状态
     */
	String STATUS = "status";
	/**
     * 方法论
     */
	String METHODOLOGY_ID = "methodology_id";
	/**
     * 方法论实例
     */
	String METHODOLOGY_INSTANCE_ID = "methodology_instance_id";
	enum StatusType {
		/**
		 * 未启动
		 */
		NOT_STARTED("0"),
		/**
		 * 进行中
		 */
		PROGRESS("1"),
		/**
		 * 完成
		 */
		COMPLETED("2");

		private final String status;

		public String getStatusType() {
			return status;
		}

		StatusType(String status) {
			this.status = status;
		}
	}
}