package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyInstanceConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MethodologyInstanceDao {

    @Autowired
    private ServiceFacade serviceFacade;
    /**
     * 查询特征by dimension
     */
    public List<IObjectData> fetchInstanceByMethodologyAndObject(User user, List<String> methodologyIds,String objectId,String objectApiName) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, MethodologyInstanceConstants.METHODOLOGY_ID, methodologyIds);
        if (objectApiName.equals(Utils.ACCOUNT_API_NAME)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, MethodologyInstanceConstants.ACCOUNT_ID, objectId);
        }
        if (objectApiName.equals(Utils.LEADS_API_NAME)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, MethodologyInstanceConstants.LEAD_ID, objectId);
        }
        if (objectApiName.equals(Utils.NEW_OPPORTUNITY_API_NAME)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, MethodologyInstanceConstants.OPPORTUNITY_ID, objectId);
        }

        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, MethodologyInstanceConstants.STATUS, MethodologyInstanceConstants.StatusType.ENABLE.getStatusType());
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.METHODOLOGY_INSTANCE, query).getData();
    }
}
