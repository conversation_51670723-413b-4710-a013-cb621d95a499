package com.facishare.crm.task.sfa.activitysummary.service.paragraph.agent;

import com.facishare.crm.task.sfa.activitysummary.model.EvaluationModel;
import com.facishare.crm.task.sfa.activitysummary.model.ParagraphResultModel;
import com.facishare.crm.task.sfa.activitysummary.model.SegmentModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Converter服务类
 * 负责将Agent的分段结果转换为ParagraphResultModel
 */
@Service
@Slf4j
public class ConverterService {
    
    /**
     * 将Agent的分段结果转换为ParagraphResultModel
     */
    public ParagraphResultModel convertToParagraphResultModel(List<SegmentModel> segments, EvaluationModel evaluation) {
        ParagraphResultModel resultModel = new ParagraphResultModel();
        
        // 设置自评分和是否需要高级模型
        resultModel.setSelfScore(evaluation.getOverallScore());
        resultModel.setNeedsHighLevelModel(evaluation.isNeedsRevision());
        
        // 转换段落
        List<ParagraphResultModel.Chunk> chunks = new ArrayList<>();
        for (SegmentModel segment : segments) {
            ParagraphResultModel.Chunk chunk = new ParagraphResultModel.Chunk();
            chunk.setContent(segment.getContentIds());
            chunk.setSummary(segment.getSummary());
            chunks.add(chunk);
        }
        resultModel.setChunks(chunks);
        
        return resultModel;
    }
}
