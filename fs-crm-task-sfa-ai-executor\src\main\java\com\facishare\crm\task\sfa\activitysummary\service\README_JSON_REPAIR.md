# JSON 修复工具使用说明

## 概述

基于开源 jsonrepair 算法实现的 Java JSON 修复工具，用于修复各种常见的 JSON 格式问题，特别是来自 LLM (大语言模型) 输出的异常 JSON。

## 特性

✅ **修复各种 JSON 格式问题**
- 单引号转双引号
- 未引用的键名
- Python 常量 (True/False/None)
- 尾随逗号
- 字符串连接
- 不完整的结构 (缺少括号)
- Markdown 代码块标记
- 注释清理
- 转义字符问题

✅ **多层防护机制**
1. 新的 JsonRepairUtil 智能修复
2. 传统字符串清理方法 (向后兼容)
3. AI 修复兜底 (原有逻辑)

✅ **高可靠性**
- 支持嵌套结构
- 括号平衡检查
- 输入验证
- 错误降级处理

## 使用方法

### 1. 基本使用

```java
// 修复简单的 JSON 格式问题
String brokenJson = "{ name: 'John', age: 30 }";
String fixed = JsonRepairUtil.repair(brokenJson);
// 结果: {"name":"John","age":30}

// 带验证的修复
String validated = JsonRepairUtil.repairAndValidate(brokenJson);
// 确保返回的是有效的 JSON
```

### 2. 服务中使用

```java
@Autowired
private FixJSONFormatService fixJSONFormatService;

// 修复并解析为对象
User user = fixJSONFormatService.getDataFixedInvalidJSON(
    currentUser, 
    "User", 
    brokenJsonString, 
    User.class
);

// 修复并解析为列表
List<Item> items = fixJSONFormatService.getDataListFixedInvalidJSON(
    currentUser,
    "Item[]",
    brokenJsonArrayString,
    Item.class,
    false
);
```

## 支持的修复场景

### 1. LLM 常见输出问题

```java
// Python 常量
"{ 'active': True, 'deleted': False, 'data': None }"
// → {"active":true,"deleted":false,"data":null}

// 单引号
"{ 'name': 'John', 'age': '30' }"
// → {"name":"John","age":"30"}

// 未引用的键
"{ name: 'John', age: 30 }"
// → {"name":"John","age":30}
```

### 2. Markdown 代码块

```java
// Markdown 包装
"```json\n{ 'name': 'John' }\n```"
// → {"name":"John"}
```

### 3. 不完整的结构

```java
// 缺少结束括号
"{ 'items': ['apple', 'banana'"
// → {"items":["apple","banana"]}

// 嵌套对象不完整
"{ 'user': { 'name': 'John', 'details': { 'age': 30"
// → {"user":{"name":"John","details":{"age":30}}}
```

### 4. 注释和尾随逗号

```java
// 包含注释
"{ 'name': 'John', // 这是注释\n 'age': 30 }"
// → {"name":"John","age":30}

// 尾随逗号
"{ 'name': 'John', 'age': 30, }"
// → {"name":"John","age":30}
```

### 5. 字符串连接

```java
// JavaScript 风格的字符串连接
"{ 'message': 'Hello' + ' World' }"
// → {"message":"Hello World"}
```

## 集成策略

该工具已集成到现有的 `FixJSONFormatService` 中，采用三层防护策略：

1. **第一层**: `JsonRepairUtil.repairAndValidate()` - 新的智能修复
2. **第二层**: 原有的 `cleanJSONStr()` 方法 - 传统清理方式  
3. **第三层**: AI 修复 - 最后的兜底方案

这确保了：
- 🚀 **性能提升**: 大多数情况下第一层就能解决问题
- 🔄 **向后兼容**: 保留原有逻辑作为备选
- 🛡️ **稳定性**: 多层防护确保不会完全失败

## 测试

运行测试用例验证功能：

```bash
mvn test -Dtest=JsonRepairUtilTest
```

测试覆盖了各种常见的 JSON 修复场景，确保工具的可靠性。

## 注意事项

- 工具会尽最大努力修复 JSON，但极端异常的输入可能仍需要 AI 修复
- 修复后的 JSON 会自动验证有效性
- 对于无法修复的情况，会返回空对象 `{}` 或空数组 `[]`
- 所有修复操作都有详细的日志记录，便于问题排查

## 与原有系统的对比

| 特性 | 原有方法 | 新的 JsonRepairUtil |
|------|----------|-------------------|
| 修复能力 | 基础字符串替换 | 状态机解析 |
| 支持场景 | 5-6种 | 10+种 |
| 嵌套结构 | ❌ | ✅ |
| 括号平衡 | ❌ | ✅ |
| 性能 | 中等 | 更优 |
| 可靠性 | 一般 | 高 |
| AI依赖 | 高 | 低 |

## 示例对比

### 复杂嵌套结构

**输入**:
```json
```json
{ 'user': { name: 'John', 'details': { 'age': 30, settings: { theme: 'dark'
```

**原有方法**: 可能失败或产生错误结果  
**新方法**: `{"user":{"name":"John","details":{"age":30,"settings":{"theme":"dark"}}}}`

### LLM 典型输出

**输入**:
```json
```json
{ 'employees': ['John', 'Anna', // 员工列表
  'status': True,
  'count': 2,
}
```

**原有方法**: 难以处理注释和 Python 常量  
**新方法**: `{"employees":["John","Anna"],"status":true,"count":2}`