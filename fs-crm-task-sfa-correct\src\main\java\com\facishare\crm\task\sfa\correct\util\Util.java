package com.facishare.crm.task.sfa.correct.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Util {
    public static boolean isChinese(char c) {
        return 19968 <= c && c <= 40869;
    }

    public static boolean isNotChinese(char c) {
        return !isChinese(c);
    }

    public static boolean isNumber(String str) {
        String regex = "^[-+]?[-0-9]\\d*\\.\\d*|[-+]?\\.?[0-9]\\d*$";
        return isMatch(regex, str);
    }

    public static boolean isEnglish(String str) {
        String regex = "[a-zA-Z]+(?:[\\s-'][a-zA-Z]+)*";
        return isMatch(regex, str);
    }

    public static boolean hasEnglish(String str) {
        int count = 0;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c >= 'a' && c <= 'z' || c >= 'A' && c <= 'Z') {
                count++;
                if (count > 1) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isUrl(String str) {
        String regex = "(http(s)?://)?([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?";
        return isMatch(regex, str);
    }

    public static boolean isMatch(String regex, String str) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    public static boolean isSubMatch(String regex, String str) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.find();
    }

    public static String half2FullChange(String input) {
        char[] c = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == ' ') {
                c[i] = '\u3000';
            } else if (c[i] < '\177') {
                c[i] = (char) (c[i] + 65248);

            }
        }
        return new String(c);
    }

    public static String full2HalfChange(String input) {
        char[] c = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '｟') {
                c[i] = (char) (c[i] - 65248);

            }
        }

        return new String(c);
    }

    public static String utf8ToUnicode(String str) {
        char[] myBuffer = str.toCharArray();

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            Character.UnicodeBlock ub = Character.UnicodeBlock.of(myBuffer[i]);
            if (ub == Character.UnicodeBlock.BASIC_LATIN) {
                sb.append(myBuffer[i]);
            } else if (ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS) {
                int j = (int) myBuffer[i] - 65248;
                sb.append((char) j);
            } else {
                short s = (short) myBuffer[i];
                String hexS = Integer.toHexString(s);
                String unicode = "\\u" + hexS;
                sb.append(unicode.toLowerCase());
            }
        }
        return sb.toString();
    }
}