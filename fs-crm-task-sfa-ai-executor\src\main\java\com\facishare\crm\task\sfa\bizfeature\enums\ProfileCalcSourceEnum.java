package com.facishare.crm.task.sfa.bizfeature.enums;

public enum ProfileCalcSourceEnum {

    NOMON("nomon", "定时"),
    REAL_TIME("real_time", "实时"),
    INIT("init", "初始化");

    private String type;
    private String desc;

    ProfileCalcSourceEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
