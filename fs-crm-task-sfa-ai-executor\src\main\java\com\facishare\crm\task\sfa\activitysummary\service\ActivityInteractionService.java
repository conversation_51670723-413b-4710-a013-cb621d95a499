package com.facishare.crm.task.sfa.activitysummary.service;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.activity.enums.AttendeesInsightType;
import com.facishare.crm.sfa.lto.activity.enums.TaskStatusEnum;
import com.facishare.crm.sfa.lto.activity.model.ActivityPollingConstants;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.service.ActivityLongPollingService;
import com.facishare.crm.sfa.lto.activity.service.ActivityTaskStateService;
import com.facishare.crm.sfa.lto.common.SFALtoRedisService;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.RichTextUtil;
import com.facishare.crm.task.sfa.activitysummary.constant.ActivityQuestionConstants;
import com.facishare.crm.task.sfa.activitysummary.model.InteractionModel;
import com.facishare.crm.task.sfa.activitysummary.model.InteractiveScenarioModel;
import com.facishare.crm.task.sfa.activitysummary.model.auditlog.ActivityMessageConverter;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.AttendeesInsightService;
import com.facishare.crm.task.sfa.common.constants.AIQuestionConstants;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.util.*;
import com.facishare.eservice.rest.online.model.SearchSceneKnowledgeModel;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 互动话题
 * <AUTHOR> lik
 * @date : 2024/12/2 20:25
 */
@Service
@Slf4j
public class ActivityInteractionService {
    @Autowired
    public MetaDataService metaDataService;
    @Autowired
    public ActivitySummaryService activitySummaryService;
    @Autowired
    public ObjectDescribeServiceImpl objectDescribeService;
    @Autowired
    private ActivityInteractionServiceManager activityInteractionServiceManager;
    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    private ConfigService configService;
    @Autowired
    private ActivityLongPollingService activityLongPollingService;
    @Autowired
    @Qualifier("SFAJedisCmd")
    private MergeJedisCmd mergeJedisCmd;
    @Autowired
    private CompletionsService completionsService;
    @Resource
    private ActivityMongoDao activityMongoDao;
    @Resource
    private InteractiveScenarioService interactiveScenarioService;
    @Autowired
    private SFALtoRedisService sfaLtoRedisService;
    @Resource
    private ActivityTaskStateService activityTaskStateService;
    @Autowired
    private AIKnowledgeBaseService aiKnowledgeBaseService;
    @Autowired
    private AttendeesInsightService attendeesInsightService;

    /**实时场景总结的类型*/
    public static String HANDLE_INTERACTION_SUMMARY_TYPE = "";
    /**实时场景分片总结的段落数量*/
    public static int HANDLE_FRAGMENTATION_SIZE = 0;
    public static int REDIS_EXPIRE_MS = 5*60*1000;
    public static boolean IS_MATCH_SUGGESTION_TOPIC = false;
    /**非实时场景分片提取的段落字节数*/
    public static int NOT_REALTIME_FRAGMENTATION_BYTE_SIZE = 0;
    /**非实时场景总结的类型*/
    public static String NOT_REALTIME_INTERACTION_SUMMARY_TYPE = "";

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            HANDLE_FRAGMENTATION_SIZE = config.getInt("handle_fragmentation_size", 50);
            HANDLE_INTERACTION_SUMMARY_TYPE = config.get("handle_interaction_summary_type", "all_summary");
            IS_MATCH_SUGGESTION_TOPIC = config.getBool("is_match_suggestion_topic", false);
            NOT_REALTIME_FRAGMENTATION_BYTE_SIZE = config.getInt("not_realtime_fragmentation_byte_size", 4000);
            NOT_REALTIME_INTERACTION_SUMMARY_TYPE = config.get("not_realtime_interaction_summary_type", "all_summary");
        });
    }
    public String getObjectApiName() {
        return null;
    }

    public static String PROMPT_INTERACTIVEISSUES_AFTER = "prompt_interactiveIssues_after";
    public static String PROMPT_INTERACTIVEISSUES_AFTER_REAL_TIME = "prompt_interactiveIssues_after_realTime";
    public static String PROMPT_INTERACTIVEISSUES_HAND = "prompt_interactiveIssues_hand";
    public static String SUGGESTED_QUESTIONS_LIST = "suggestedQuestionsList";
    public static String EXIST_QUESTION_LIST = "existQuestionList";

    public static String JSON_QUESTION_LIST = "[{\"questionId\":\"\",\"question\":\"\",\"questionSeq\":\"\",\"questioner\":\"\",\"answer\":\"\",\"answerSeq\":\"\",\"responder\":\"\",\"questioner_attitude\":\"\",\"attitude_analysis\":\"\",\"tags\":\"\"}]";

    public static List<String> SPECIAL_PROBLEM_LIST= Lists.newArrayList("{问题}","问题");// ignoreI18n

    @SFAAuditLog(bizName = "interaction_issues", entityClass = ActivityMessage.class, convertClass = ActivityMessageConverter.class)
    public void handleActivityInteraction(ActivityMessage activityMessage, boolean isRealTime, boolean isLastTime){
        try {
            execute(InteractionModel.Arg.builder().tenantId(activityMessage.getTenantId())
                    .activeRecordId(activityMessage.getObjectId()).op("i").realTimeFlag(isRealTime).realTimeLastTimeFlag(isLastTime).userId(activityMessage.getOpId()).language(activityMessage.getLanguage()).build());
            activityTaskStateService.insOrUpdate(activityMessage.getTenantId(), "interactive_issues", activityMessage.getObjectId(), TaskStatusEnum.COMPLETED, null);
        }catch (Exception e){
            log.error("ActivityInteractionService handleActivityInteraction e:",e);
        }
    }
    public void execute(InteractionModel.Arg arg){
        log.info("ActivityInteractiveIssuesService execute tenantId:{},activeRecordId:{},op:{}",arg.getTenantId(),arg.getActiveRecordId(),arg.getOp());
        //2、查询销售记录
        //3、判断有没有关联客户
        //4、判断有没有互动语料
        //5、

        User user ;
        if(ObjectUtils.isNotEmpty(arg.getUserId())){
            user = new User(arg.getTenantId(), arg.getUserId());
        }else {
            user = new User(arg.getTenantId(), CommonConstant.SUPER_USER);
        }
        IActionContext actionContext = ActionContextExt.of(user).getContext();
        actionContext.put(ActionContextKey.SEARCH_RICH_TEXT_EXTRA, true);
        IObjectData activeRecordData = serviceFacade.findObjectData(actionContext, arg.getActiveRecordId(), CommonConstant.ACTIVE_RECORD_API_NAME);
        if(ObjectUtils.isEmpty(activeRecordData)){
            log.warn("ActivityInteractiveIssuesService execute activeRecordData is null tenantId:{},activeRecordId:{}",user.getTenantId(),arg.getActiveRecordId());
            return;
        }
        String accountId = InheritRecordUtil.getStringValue(activeRecordData,CommonConstant.ACCOUNT_ID,"");
        String newOpportunityId = InheritRecordUtil.getStringValue(activeRecordData,CommonConstant.NEW_OPPORTUNITY_ID,"");
        String leadsId = InheritRecordUtil.getStringValue(activeRecordData,CommonConstant.LEADS_ID,"");
        if(ObjectUtils.isEmpty(accountId)  && ObjectUtils.isEmpty(newOpportunityId) && ObjectUtils.isEmpty(leadsId)){
            log.warn("ActivityInteractiveIssuesService  accountId && newOpportunityId is null tenantId:{},activeRecordId:{}",user.getTenantId(),arg.getActiveRecordId());
            return;
        }
        if(ObjectUtils.isNotEmpty(accountId)){
            arg.setLinkAccountDataId(accountId);
            arg.setLinkAccountFieldApiName(CommonConstant.ACCOUNT_ID);
        }
        if(ObjectUtils.isNotEmpty(newOpportunityId)){
            arg.setLinkOpportunityDataId(newOpportunityId);
            arg.setLinkOpportunityFieldApiName(CommonConstant.NEW_OPPORTUNITY_ID);
        }
        if(ObjectUtils.isNotEmpty(leadsId)){
            arg.setLinkLeadsDataId(leadsId);
            arg.setLinkLeadsFieldApiName(CommonConstant.LEADS_ID);
        }
        checkIsRealTime(user,activeRecordData,arg);

    }
    public void checkIsRealTime(User user,IObjectData activeRecordData,InteractionModel.Arg arg){
        if(arg.isRealTimeFlag()){
            handleRealTime(user,activeRecordData,arg);
        }else{
            handleNotRealTime(user,activeRecordData,arg);
            List<String> insightTypeList = Lists.newArrayList(AttendeesInsightType.MEETING_SUMMARY, AttendeesInsightType.QUESTION_ANSWER_PERFORMANCE, AttendeesInsightType.DIALOGUE_SKILL_PERFORMANCE, AttendeesInsightType.SOP_COVERAGE);
            insightTypeList.addAll(Lists.newArrayList("skill_score", "question_score", "answer_score"));// 特征依赖
            attendeesInsightService.sendInsightMessage(arg.getTenantId(), arg.getActiveRecordId(), insightTypeList);
        }
    }
    public void handleNotRealTime(User user,IObjectData activeRecordData,InteractionModel.Arg arg){
        log.info("ActivityInteractiveIssuesService handleTopic tenantId:{},linkAccountDataId:{},linkOpportunityDataId:{}",user.getTenantId(),arg.getLinkAccountDataId(),arg.getLinkOpportunityDataId());
        String content = InheritRecordUtil.getStringValue(activeRecordData,CommonConstant.INTERACTIVE_CONTENT,"");
        InteractiveScenarioModel.TemplateConfig templateConfig = interactiveScenarioService.getTemplateConfig(InteractiveScenarioModel.ModelApiName.ACTIVITY_QUESTION, user, activeRecordData);
        String questionWords = templateConfig.getTemplateApiName();
        if(ObjectUtils.isEmpty(content)){
            log.info("ActivityInteractiveIssuesService handleTopic interactiveContent is null dataId:{}",activeRecordData.getId());
            content = InheritRecordUtil.getStringValue(activeRecordData,CommonConstant.ACTIVE_RECORD_CONTENT,"");
            if(ObjectUtils.isEmpty(content) || content.length()<=150){
                log.info("ActivityInteractiveIssuesService handleTopic content is null content:{}",content);
                return;
            }
        }

        StopWatch stopWatch = StopWatch.create("handleNotRealTime");
        stopWatch.lap("suggestionTopicMap");
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put(EXIST_QUESTION_LIST,"");
        sceneParamMap.putAll(templateConfig.getSourceConfig());
        List<InteractiveDocument> interactiveList = activityMongoDao.queryListByActiveRecordId(user.getTenantId(),arg.getActiveRecordId(),0,CompletionsService.maxNum);
        if(CollectionUtil.isNotEmpty(interactiveList)){
            content = activityMongoDao.montageMongoContent(interactiveList,true,arg.getLanguage());
        }
        List<AIQuestionConstants.InteractionResult> resultList = new ArrayList<>();
        stopWatch.lap("resultAi");
        if(NOT_REALTIME_INTERACTION_SUMMARY_TYPE.equals("all_summary")){
            String resultAi = handleAiProxy(user,arg,content,questionWords,sceneParamMap);
            if (ObjectUtils.isEmpty(resultAi)){
                log.warn("ActivityInteractiveIssuesService handleTopic result is null content:{}",resultAi);
                return;
            }
            resultList  = formatByAiResultStr(user,resultAi,arg);
        }else if(NOT_REALTIME_INTERACTION_SUMMARY_TYPE.equals("fragmentation_summary")){
            resultList = handleFragmentationSummaryNotRealTime(user,arg,content,questionWords,sceneParamMap);
        }else if(NOT_REALTIME_INTERACTION_SUMMARY_TYPE.equals("words_summary")){

        }
        resultList = filterSpecialQuestion(resultList);
        if(CollectionUtil.isEmpty(resultList)){
            return ;
        }
        //String linkFieldApiName = queryLinkFieldApiName(arg.getLinkApiName());
        Integer version = queryDataVersion(user,arg);

        matchSuggestionTopic(user,arg,resultList,stopWatch);
        stopWatch.lap("existQuestionList");
        setUser(user,arg.getActiveRecordId(),resultList);
        stopWatch.lap("setUser");
        saveTopic(user,version,arg ,activeRecordData,resultList);
        stopWatch.logSlow(1000);
        callFunction(user,activeRecordData.getId());
    }

    public void queryAiSuggestion(InteractionModel.Arg arg,List<AIQuestionConstants.InteractionResult> resultList,StopWatch stopWatch){
        if(ObjectUtils.isEmpty(arg.getUserId())){
            log.warn("queryAiSuggestion userId is null userId:{}",arg.getUserId());
            return;
        }
        if(CollectionUtil.isEmpty(resultList)){
            log.warn("queryAiSuggestion resultList is null");
            return;
        }
        User user = new User(arg.getTenantId(), arg.getUserId());
        String value = configService.findUserConfig(user,"sfa_activity_set_ai_switch");
        if(ObjectUtils.isEmpty(value) || "false".equals(value)){
            log.warn("queryAiSuggestion userId:{},value:{}",arg.getUserId(),value);
            return ;
        }
        try {

            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            resultList.stream().forEach(x->{
                if(ObjectUtils.isNotEmpty(x.getQuestion())){
                    parallelTask.submit(() -> {
                        String searchWord = x.getQuestion();
                        SearchSceneKnowledgeModel.SearchResult knowledgeRet = aiKnowledgeBaseService.proxyKnowledgeService(user,searchWord);
                        stopWatch.lap("queryAiSuggestion-searchKnowledgeResult-"+searchWord);
                        if(ObjectUtils.isNotEmpty(knowledgeRet)){
                            x.setAiAnswerContent(knowledgeRet.getChatGPTReplyContent());
                            List<AIQuestionConstants.WordResult> list = new ArrayList<>();
                            if(CollectionUtil.isNotEmpty(knowledgeRet.getSearchResult())){
                                knowledgeRet.getSearchResult().stream().forEach(w->{
                                    AIQuestionConstants.WordResult wordResult = new AIQuestionConstants.WordResult();
                                    wordResult.setDataId(w.getDataId());
                                    wordResult.setTitle(w.getTitle());
                                    wordResult.setUrl(w.getUrl());
                                    list.add(wordResult);
                                });
                            }
                           x.setAiAnswerWord(list);
                        }
                    });
                }
            });
            parallelTask.await(300, TimeUnit.SECONDS);
        }catch (Exception e){
            log.warn("queryAiSuggestion exception:",e);
        }
    }

    public void saveTopic(User user,Integer version,InteractionModel.Arg arg,IObjectData activeRecordData,List<AIQuestionConstants.InteractionResult> resultList){
        IObjectDescribe describe = AccountPathUtil.getObjectDescribe(user, CommonConstant.ACTIVITY_QUESTION_API_NAME);
        List<IObjectData> list =new ArrayList<>();
        resultList.stream().forEach(x->{
            if(ObjectUtils.isEmpty(x.getQuestion())){
                return;
            }
            IObjectData iObjectData = new ObjectData();

            if(ObjectUtils.isNotEmpty(arg.getLinkAccountDataId())){
                iObjectData.set(arg.getLinkAccountFieldApiName(), arg.getLinkAccountDataId());
            }
            if(ObjectUtils.isNotEmpty(arg.getLinkOpportunityDataId())){
                iObjectData.set(arg.getLinkOpportunityFieldApiName(), arg.getLinkOpportunityDataId());
            }
            if(ObjectUtils.isNotEmpty(arg.getLinkLeadsDataId())){
                iObjectData.set(arg.getLinkLeadsFieldApiName(), arg.getLinkLeadsDataId());
            }
            iObjectData.set(AIQuestionConstants.Field.active_record_id, Lists.newArrayList(activeRecordData.getId()));
            iObjectData.set(AIQuestionConstants.Field.question_content, x.getQuestion());
            iObjectData.set(AIQuestionConstants.Field.question_proposer, x.getQuestioner());
            if(ObjectUtils.isNotEmpty( x.getAnswer())){
                iObjectData.set(AIQuestionConstants.Field.response_status, AIQuestionConstants.ResponseStatusEnum.ANSWERED_ALREADY.getValue());
            }else{
                iObjectData.set(AIQuestionConstants.Field.response_status, AIQuestionConstants.ResponseStatusEnum.NOT_RESPONDED.getValue());
            }
            iObjectData.set(AIQuestionConstants.Field.match_suggest_topic, x.getMatching_question());
            iObjectData.set(AIQuestionConstants.Field.question_type, AIQuestionConstants.QuestionTypeEnum.INTERACTION.getValue());
            iObjectData.set(AIQuestionConstants.Field.answer_summary, x.getAnswer());
            iObjectData.set(AIQuestionConstants.Field.answer_person, x.getResponder());
            iObjectData.set(AIQuestionConstants.Field.answer_time, System.currentTimeMillis());
            iObjectData.set(AIQuestionConstants.Field.answer_version, version);
            iObjectData.set(AIQuestionConstants.Field.proposer_attitude, AIQuestionConstants.ProposerAttitudeEnum.of(x.getQuestioner_attitude()));
            iObjectData.set(AIQuestionConstants.Field.proposer_attitude_analysis, x.getAttitude_analysis());
            if(ObjectUtils.isNotEmpty(x.getAiAnswerContent())){
                iObjectData.set(AIQuestionConstants.Field.ai_answer_content, RichTextUtil.buildCompleteRich(x.getAiAnswerContent(),null));
                iObjectData.set(AIQuestionConstants.Field.ai_answer_time, System.currentTimeMillis());
            }
            if(ObjectUtils.isNotEmpty(x.getTags())){
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(AIQuestionConstants.Field.tags);
                List<Map> options = (List<Map>) fieldDescribe.get("options");
                List<String> optionValues = new ArrayList<>();
                options.stream().forEach(op->{
                    if(x.getTags().equals(op.get("label").toString())){
                        optionValues.add(op.get("value").toString());
                    }
                });
                iObjectData.set(AIQuestionConstants.Field.tags,optionValues);
            }
            iObjectData.set(AIQuestionConstants.Field.question_seq,x.getQuestionSeq());
            if(CollectionUtil.isNotEmpty(x.getAnswerSeqList())){
                iObjectData.set(AIQuestionConstants.Field.answer_seq,JSONObject.toJSONString(x.getAnswerSeqList()));
            }
            iObjectData.set(AIQuestionConstants.Field.question_user,x.getQuestionUser());
            iObjectData.set(AIQuestionConstants.Field.answer_user,x.getAnswerUser());
            iObjectData.set("ai_answer_document", JSONObject.toJSON(x.getAiAnswerWord()));
            iObjectData.set("object_describe_api_name", CommonConstant.ACTIVITY_QUESTION_API_NAME);
            iObjectData.set("object_describe_id", describe.getId());
            iObjectData.set(Tenantable.TENANT_ID, user.getTenantId());
            iObjectData.set("owner", activeRecordData.getOwner());
            iObjectData.set("record_type", "default__c");
            list.add(iObjectData);
        });
        try {
            serviceFacade.bulkSaveObjectData(list,user);
            LogUtil.recordLogs(user, Lists.newArrayList(list),describe, EventType.ADD, ActionType.Add);
        }catch (Exception e){
            log.error("ActivityInteractionService saveTopic error :",e);
        }

    }

    //获取建议问题
    public List<IObjectData> getSuggestionTopicList(User user,String id,String apiName){
        return null;
    }

    /**
     * 获取数据对象与ai问题对象（AIQuestionObj）的关联字段
     * 比如 客户    account_id
     * 商机       new_opportunity_id
     * @return
     */
    public String getLinkFieldApiName(String apiName){
        return "";
    }

    private String queryLinkFieldApiName(String apiName){
        ActivityInteractionService interactionService = activityInteractionServiceManager.getInteractionService(apiName);
        if (interactionService != null) {
            return interactionService.getLinkFieldApiName(apiName);
        }else{
            throw new ValidateException("参数有问题");// ignoreI18n
        }
    }

    public Integer getDataVersion(User user, InteractionModel.Arg arg){
        return 0;
    }
    private Integer queryDataVersion(User user, InteractionModel.Arg arg){
        return 0;
//        if(arg.getOp().equals("i")){
//            return 0;
//        }
//        if(ObjectUtils.isEmpty(arg.getLinkApiName())){
//            return 0;
//        }
//        ActivityInteractionService interactionService = activityInteractionServiceManager.getInteractionService(arg.getLinkApiName());
//        if (interactionService != null) {
//            return interactionService.getDataVersion(user,arg);
//        }
//        throw new ValidateException("参数有问题");// ignoreI18n
    }



    public void handleRealTime(User user,IObjectData activeRecordData,InteractionModel.Arg arg){
        log.info("ActivityInteractiveIssuesService handleRealTime tenantId:{},dataId:{},linkOpportunityDataId:{}",user.getTenantId(),arg.getLinkAccountDataId(),arg.getLinkOpportunityDataId());
        String content = InheritRecordUtil.getStringValue(activeRecordData,CommonConstant.INTERACTIVE_CONTENT,"");
        if(ObjectUtils.isEmpty(content)){
            return;
        }

        StopWatch stopWatch = StopWatch.create("handleRealTime");

        Map<String,IObjectData> existQuestionMap = new HashMap<>();

        SFAJedisLock lock = new SFAJedisLock(mergeJedisCmd, getRedisKey(user,arg,"activityInteractionService"), REDIS_EXPIRE_MS);
        /**
         * 加了redis锁，原因是，ai分析比较慢，实时录音的场景下，可能10s，20s就发一次，分析一次可能需要20s，这个给第一批在分析，第二批次又过来了又分析，这个时候就会出现相同的问题，保存在数据库中
         * 所以加了redis锁，防止重复保存
         * for的5次，每次循环获取锁，如果获取不到，就等待1.5s，如果获取不到，就退出循环，这个给是因为实时录音已经结束了，做最后一次的分析，确保其他的分析已经结束了，然后再去最后的分析
         */
            boolean b = lock.tryLock();
            if (!b) {
                log.warn("ActivityInteractiveIssuesService handleRealTime tryLock b:{}",b);
//                if(arg.isRealTimeLastTimeFlag()){
//                    for(int i = 0; i < 10;i++){
//                        log.warn("handleRealTime isRealTimeLastTimeFlag i:{}",i);
//                        Thread.sleep(10000);
//                        b = lock.tryLock();
//                        if(b){
//                            break;
//                        }
//                    }
//                    if(!b){
//                        return;
//                    }
//                }else{
//                    return;
//                }
                return;
            }
        try  {
            stopWatch.lap("tryLock");
            //获取已经生成的问题
            List<IObjectData> existQuestionList = queryExistQuestionByActiveRecordId(user,arg.getActiveRecordId());
            Map<String,String> existQuestionIdOfNameMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(existQuestionList)){
                existQuestionMap = existQuestionList.stream().collect(Collectors.toMap(IObjectData::getId,x->x));
                existQuestionIdOfNameMap = existQuestionList.stream().filter(x->ObjectUtils.isNotEmpty(x.get(AIQuestionConstants.Field.question_content))).collect(Collectors.toMap(IObjectData::getId,x->x.get(AIQuestionConstants.Field.question_content).toString()));

            }
            stopWatch.lap("suggestionTopicList");
            String resultAi = "";
            if(HANDLE_INTERACTION_SUMMARY_TYPE.equals("all_summary")){
                resultAi = handleAllContextSummary(user,arg,existQuestionIdOfNameMap);
            }else if(HANDLE_INTERACTION_SUMMARY_TYPE.equals("fragmentation_summary")){
                resultAi = handleFragmentationSummary(user,arg);
            }else if(HANDLE_INTERACTION_SUMMARY_TYPE.equals("words_summary")){

            }
            stopWatch.lap("resultAi");
            if (ObjectUtils.isEmpty(resultAi)){
                log.warn("ActivityInteractiveIssuesService handleTopic result is null content:{}",resultAi);
                return;
            }
            if(resultAi.startsWith("{") && resultAi.endsWith("}")){
                resultAi = "["+resultAi+"]";
            }
            List<AIQuestionConstants.InteractionResult> resultList = formatByAiResultStr(user,resultAi,arg);
            resultList = filterSpecialQuestion(resultList);

            if(CollectionUtil.isEmpty(resultList)){
                return ;
            }
            //String linkFieldApiName = queryLinkFieldApiName(arg.getLinkApiName());
            Integer version = 0;
            queryAiSuggestion(arg,resultList,stopWatch);
            stopWatch.lap("queryAiSuggestion");

            matchSuggestionTopic(user,arg,resultList,stopWatch);
            stopWatch.lap("existQuestionList");
            setUser(user,arg.getActiveRecordId(),resultList);
            stopWatch.lap("setUser");
            List<AIQuestionConstants.InteractionResult> insertList = new ArrayList<>();
            List<IObjectData> updList = new ArrayList<>();
            if(ObjectUtils.isNotEmpty(existQuestionMap)){
                Map<String, IObjectData> finalExistQuestionMap = existQuestionMap;
                resultList.stream().forEach(x->{
                    if(ObjectUtils.isNotEmpty(x.getQuestionId()) && finalExistQuestionMap.containsKey(x.getQuestionId())){
                        IObjectData existData = finalExistQuestionMap.get(x.getQuestionId());
                        existData.set(AIQuestionConstants.Field.answer_summary, x.getAnswer());
                        existData.set(AIQuestionConstants.Field.answer_person, x.getResponder());
                        existData.set(AIQuestionConstants.Field.answer_time, System.currentTimeMillis());
                        existData.set(AIQuestionConstants.Field.question_seq, x.getQuestionSeq());
                        existData.set(AIQuestionConstants.Field.answer_seq, x.getAnswerSeq());
                        updList.add(existData);
                    }else{
                        insertList.add(x);
                    }
                });
                if(CollectionUtil.isNotEmpty(updList)){
                    serviceFacade.batchUpdateByFields(user,updList,Lists.newArrayList(AIQuestionConstants.Field.answer_summary,
                            AIQuestionConstants.Field.answer_person,AIQuestionConstants.Field.answer_time,AIQuestionConstants.Field.question_seq,
                            AIQuestionConstants.Field.answer_seq));
                }
            }else{
                insertList.addAll(resultList);
            }
            saveTopic(user,version,arg,activeRecordData,insertList);
        }catch (Exception e){
            log.error("ActivityInteractiveIssuesService SFAJedisLock error",e);
        }finally {
            lock.unlock();
        }
        ActivityPollingConstants.PollingMessageData pollingMessageData = ActivityPollingConstants.PollingMessageData.builder()
                .functionModule(ActivityPollingConstants.Function.ACTIVITY)
                .primaryModule(ActivityPollingConstants.Primary.INTERACTIVE_ISSUES)
                .tenantId(user.getTenantId())
                .userId(user.getUpstreamOwnerIdOrUserId())
                .action(ActivityPollingConstants.Action.REFRESH_ALL)
                .build();
        activityLongPollingService.sendPollingMessage(pollingMessageData);
        log.info("ActivityInteractiveIssuesService activityLongPollingService.sendPollingMessage");
        stopWatch.logSlow(1000);
    }

    public List<IObjectData> queryExistQuestionByActiveRecordId(User user,String activeRecordId){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AIQuestionConstants.Field.active_record_id, activeRecordId);
        SearchUtil.fillFilterEq(filters, AIQuestionConstants.Field.question_type, AIQuestionConstants.QuestionTypeEnum.INTERACTION.getValue());
        searchTemplateQuery.setFilters(filters);
        IActionContext actionContext = ActionContextExt.of(user).getContext();
        actionContext.put(ActionContextKey.SEARCH_RICH_TEXT_EXTRA, true);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext, CommonConstant.ACTIVITY_QUESTION_API_NAME, searchTemplateQuery);
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    public String getRedisKey(User user,InteractionModel.Arg arg,String flag){
        return flag + user.getTenantId()+arg.getActiveRecordId();
    }




    public void matchSuggestionTopic(User user,InteractionModel.Arg arg,List<AIQuestionConstants.InteractionResult> resultList,StopWatch stopWatch){
//        if(!IS_MATCH_SUGGESTION_TOPIC){
//            log.warn("ActivityInteractiveIssuesService matchSuggestionTopic is_match_suggestion_topic is false");
//            return;
//        }
//        ActivityInteractionService interactionService = activityInteractionServiceManager.getInteractionService(arg.getLinkApiName());
//        if (interactionService == null) {
//            throw new ValidateException("参数有问题");// ignoreI18n
//        }
//        List<IObjectData> suggestionTopicList = interactionService.getSuggestionTopicList(user,arg.getLinkDataId(),arg.getLinkApiName());
//        if(ObjectUtils.isEmpty(suggestionTopicList)){
//            return;
//        }
//        stopWatch.lap("matchSuggestionTopic-getSuggestionTopicList");
//        List<String> suggestionList = suggestionTopicList.stream().filter(x->ObjectUtils.isNotEmpty(x.get(AIQuestionConstants.Field.question_content)))
//                .map(x->x.get(AIQuestionConstants.Field.question_content).toString()).collect(Collectors.toList());
//        List<String> interactionList = resultList.stream().map(x->x.getQuestion()).collect(Collectors.toList());
//        Map<String, Object> matchParamMap = new HashMap<>();
//        matchParamMap.put("list1",JSONObject.toJSONString(interactionList));
//        matchParamMap.put("list2",JSONObject.toJSONString(suggestionList));
//        matchParamMap = activitySummaryService.handleAiCompleteLanguage(matchParamMap,null,arg.getLanguage());
//        List<IObjectData> matchResultList = activitySummaryService.getAi(user,"prompt_interactiveIssues_match_suggest",matchParamMap,null);
//        stopWatch.lap("matchSuggestionTopic-matchResultList");
//        if(CollectionUtil.isEmpty(matchResultList)){
//            log.warn("matchSuggestionTopic matchResultList is null");
//            return;
//        }
//        try {
//            Map<String,List<String>> matchResultMap = matchResultList.stream().collect(Collectors.toMap(x->x.get("list1",String.class),x->x.get("list2",List.class),(existing, replacement) -> existing));
//            Map<String,String> suggestionTopicOfIdMap =  suggestionTopicList.stream().collect(Collectors.toMap(x->x.get(AIQuestionConstants.Field.question_content).toString(),IObjectData::getId,(v1,v2)->v1));
//            resultList.stream().forEach(x->{
//                if(matchResultMap.containsKey(x.getQuestion())){
//                    List<String> list = matchResultMap.get(x.getQuestion());
//                    if(CollectionUtil.isNotEmpty(list)){
//                        List<String> suggestionIds=new ArrayList<>();
//                        list.stream().forEach(y->{
//                            if(suggestionTopicOfIdMap.containsKey(y)){
//                                suggestionIds.add(suggestionTopicOfIdMap.get(y));
//                            }
//                        });
//                        x.setMatching_question(suggestionIds);
//                    }
//                }
//            });
//        }catch (Exception e){
//            log.error("matchSuggestionTopic error matchResultList:{}",JSONObject.toJSONString(matchResultList));
//        }

    }

    public List<AIQuestionConstants.InteractionResult>  filterSpecialQuestion(List<AIQuestionConstants.InteractionResult> resultList){
        if(CollectionUtil.isEmpty(resultList)){
            return null;
        }
        resultList.stream().forEach(x->{
            String questionSeq = x.getQuestionSeq();
            if(x.getQuestionSeq().contains("无") || x.getQuestionSeq().contains(":") ){// ignoreI18n
                questionSeq = "";
            }else if(x.getQuestionSeq().contains("-")){
                String[] str =  x.getQuestionSeq().split("-");
                questionSeq = str[0];
            }else if(x.getQuestionSeq().contains(";")){
                String[] str =  x.getQuestionSeq().split(";");
                questionSeq = str[0];
            }else if(x.getQuestionSeq().contains(",")){
                String[] str =  x.getQuestionSeq().split(",");
                questionSeq = str[0];
            }
            x.setQuestionSeq(removeSpace(questionSeq));
            if(x.getAnswerSeq().contains("无") || x.getAnswerSeq().contains(":")){// ignoreI18n
                x.setAnswerSeq("");
            }else if(x.getAnswerSeq().contains("-")){
                String[] str =  x.getAnswerSeq().split("-");
                x.setAnswerSeq(str[0]+";"+str[1]);
            }else if(x.getAnswerSeq().contains(",")){
                x.setAnswerSeq(x.getAnswerSeq().replaceAll(",",";"));
            }

            if(ObjectUtils.isNotEmpty(x.getAnswerSeq()) ){
                List<String> answerSeq = new ArrayList<>();
                if(x.getAnswerSeq().contains(";")){
                    answerSeq = Arrays.asList(x.getAnswerSeq().split(";"));
                }else{
                    answerSeq.add(x.getAnswerSeq());
                }
                x.setAnswerSeqList(answerSeq.stream().map(this::removeSpace).collect(Collectors.toList()));
            }

        });
        return resultList.stream().filter(x->!SPECIAL_PROBLEM_LIST.contains(x.getQuestion())).collect(Collectors.toList());
    }


    public void callFunction(User user,String dataId){
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            String value = configService.findTenantConfig(user,"sfa_activity_call_function_name");
            if(ObjectUtils.isEmpty(value)){
                log.warn("callFunction value is null");
                return ;
            }
            IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user, value, CommonConstant.ACTIVE_RECORD_API_NAME);
            if (function == null) {
                log.warn("callFunction function is null");
                return;
            }
            Map<String, Object> parameters = Maps.newHashMap();
            parameters.put(AIQuestionConstants.Field.active_record_id, dataId);
            serviceFacade.getFunctionLogicService().executeUDefFunction(user, function, parameters, null, null);
        });
        parallelTask.run();
    }


    /**
     * 分片提取互动话题
     * 分片的数量是配置文件中 hadnle_fragmentation_size 设置，默认50
     * @param user
     * @param arg
     * @return
     */
    public String  handleFragmentationSummary(User user,InteractionModel.Arg arg){
        /**
         * 1、先查询一共有多少条记录
         * 2、50条 50条的去处理
         * 3、查询redis当前处理的批次
         */
        List<InteractiveDocument> interactiveList = completionsService.getCorpusStrWithSpeakerByMax(user.getTenantId(),arg.getActiveRecordId());
        if(CollectionUtil.isEmpty(interactiveList)){
            return "";
        }
        String lockKey = getRedisKey(user,arg,"handleFragmentationSummary");
        String redisValue = mergeJedisCmd.get(lockKey);
        if(StringUtils.isBlank(redisValue)){
            redisValue = "1";
        }
        Integer handleTime = Integer.parseInt(redisValue);
        int limit = 0;
        int offset = 0;
        //第一次 第二次总结10句话总结一次，从第三次开始每个 HANDLE_FRAGMENTATION_SIZE  句话处理
        if(handleTime == 1 ||  handleTime == 2){
            limit = handleTime * 10;
            offset = limit - 10;
        }else{
            //从第三次开始 每个间隔 HANDLE_FRAGMENTATION_SIZE  句话总结一下，但是必须要 handleTime-1，因为第一次是 1*10，第二次是2*10，
            // 第三次就是3*20=60，其中间隔了40句话，时间比较长，所以超过第三次之后就-1，（3-1）*20=40，这就间隔20句话了
            limit = (handleTime-1) * HANDLE_FRAGMENTATION_SIZE;
            offset = limit - HANDLE_FRAGMENTATION_SIZE;
        }
        if(interactiveList.size() < limit){
            log.warn("handleFragmentationObtain size not enough.interactiveList.size:{},handleTime:{}",interactiveList.size() , handleTime);
            return "";
        }
        //开始段落，向前多拉取一段
        if(offset!=0){
            offset = offset - 2;
        }
        List<InteractiveDocument> newList = interactiveList.subList(offset,limit);
        String content = activityMongoDao.montageMongoContent(newList,true,arg.getLanguage());
        if(ObjectUtils.isEmpty(content)){
            log.warn("handleFragmentationObtain sb is null ");
            return "";
        }
        InteractiveScenarioModel.TemplateConfig templateConfig = interactiveScenarioService.getTemplateConfig(InteractiveScenarioModel.ModelApiName.ACTIVITY_QUESTION, user, arg.getActiveRecordId());
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put(EXIST_QUESTION_LIST,"");
        sceneParamMap.putAll(templateConfig.getSourceConfig());
        String aiResult = handleAiProxy(user,arg,content,templateConfig.getTemplateApiName(),sceneParamMap);
        mergeJedisCmd.setex(lockKey,60*60*3,String.valueOf(handleTime+1));
        return aiResult;
    }

    public String handleAllContextSummary(User user,InteractionModel.Arg arg,Map<String,String> existQuestionIdOfNameMap){
        InteractiveScenarioModel.TemplateConfig templateConfig = interactiveScenarioService.getTemplateConfig(InteractiveScenarioModel.ModelApiName.ACTIVITY_QUESTION, user, arg.getActiveRecordId());
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put(EXIST_QUESTION_LIST,JSONObject.toJSONString(existQuestionIdOfNameMap));
        sceneParamMap.putAll(templateConfig.getSourceConfig());
        String context = completionsService.getCorpusStrWithSpeakerByMax(user.getTenantId(),arg.getActiveRecordId(),arg.getLanguage());
        return handleAiProxy(user,arg,context,templateConfig.getTemplateApiName(),sceneParamMap);
    }



    public String handleAiProxy(User user,InteractionModel.Arg arg,String content,String propApiName,Map<String, Object> sceneParamMap){
        if(ObjectUtils.isEmpty(sceneParamMap)){
            sceneParamMap = new HashMap<>();
        }
        sceneParamMap.put(ActivityQuestionConstants.CORPORA_FROM_MONGGO,content);
        sceneParamMap = activitySummaryService.handleAiCompleteLanguage(sceneParamMap,null,arg.getLanguage());
        handleSelectOneFieldLabel(user,sceneParamMap);
        String resultAi = activitySummaryService.getAiComplete(user,propApiName,sceneParamMap,arg.getActiveRecordId());
        resultAi = activitySummaryService.captureAiResult(resultAi);
        return resultAi;
    }

    public void handleSelectOneFieldLabel(User user,Map<String, Object> sceneParamMap){
        IObjectDescribe describe = AccountPathUtil.getObjectDescribe(user, CommonConstant.ACTIVITY_QUESTION_API_NAME);
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(AIQuestionConstants.Field.tags);
        List<Map> options = (List<Map>) fieldDescribe.get("options");
        StringBuilder sb = new StringBuilder();
        options.stream().forEach(x->{
            sb.append(x.get("label")).append("/");
        });
        sceneParamMap.put(AIQuestionConstants.Field.tags,sb.toString());
    }





    public void handleAiSuggestion(InteractionModel.GetAiSuggestionMsg msg) {
        log.warn("handleAiSuggestion msg:{}",JSONObject.toJSONString(msg));
        try {
            if(CollectionUtil.isEmpty(msg.getQuestionIds())){
                log.warn("handleAiSuggestion getQuestionIds is null");
                return;
            }
            List<IObjectData> list = serviceFacade.findObjectDataByIdsIgnoreAll(msg.getTenantId(),msg.getQuestionIds(),CommonConstant.ACTIVITY_QUESTION_API_NAME);
            User user = new User(msg.getTenantId(), msg.getUserId());
            List<IObjectData> updateList = new ArrayList<>();
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            list.stream().forEach(x->{
                parallelTask.submit(() -> {
                    String question = x.get("question_content").toString();
                    SearchSceneKnowledgeModel.SearchResult aiResult = aiKnowledgeBaseService.proxyKnowledgeService(user,question);
                    if(ObjectUtils.isNotEmpty(aiResult) && ObjectUtils.isNotEmpty(aiResult.getChatGPTReplyContent())){
                        String aiAnswerContent = aiResult.getChatGPTReplyContent();
                        aiAnswerContent = aiAnswerContent.replaceAll("#+|\\*+", "").trim();
                        x.set("ai_answer_content",RichTextUtil.buildCompleteRich(aiAnswerContent,null));
                        x.set("ai_answer_time", System.currentTimeMillis());
                        x.set("ai_answer_document", JSONObject.toJSON(aiResult.getSearchResult()));
                        updateList.add(x);
                    }
                });
            });
            parallelTask.await(300, TimeUnit.SECONDS);
            if(CollectionUtils.empty(updateList)){
                log.warn("InteractQuestionServicee getAiSuggestion updateList is null");
                return ;
            }
            Map<String,IObjectData>oldDataListMap = list.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
            IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(),CommonConstant.ACTIVITY_QUESTION_API_NAME);
            serviceFacade.batchUpdateByFields(user, updateList, Lists.newArrayList("ai_answer_content","ai_answer_time","ai_answer_document"));
            String buttonApiName = I18N.text("sfa.interact.question.get_ai_suggestion");
            InternationalItem internationalPeerDisplayName = new InternationalItem();
            internationalPeerDisplayName.setDefaultInternationalValue(buttonApiName);
            internationalPeerDisplayName.setInternationalKey("sfa.interact.question.get_ai_suggestion");
            updateList.stream().forEach(x->{
                IObjectData oldObjectData = oldDataListMap.get(x.getId());
                Map<String, Object> diffMap = Maps.newHashMap();
                diffMap.put("ai_answer_content", x.get("ai_answer_content"));
                diffMap.put("ai_answer_time", x.get("ai_answer_time"));
                diffMap.put("ai_answer_document", x.get("ai_answer_document"));
                serviceFacade.log(user, EventType.MODIFY, ActionType.Modify, objectDescribe, x, diffMap, oldObjectData, buttonApiName, buttonApiName,internationalPeerDisplayName,null, Maps.newHashMap());
            });
            ActivityPollingConstants.PollingMessageData pollingMessageData = ActivityPollingConstants.PollingMessageData.builder()
                    .functionModule(ActivityPollingConstants.Function.ACTIVITY)
                    .primaryModule(ActivityPollingConstants.Primary.INTERACTIVE_ISSUES)
                    .tenantId(user.getTenantId())
                    .userId(user.getUpstreamOwnerIdOrUserId())
                    .build();
            pollingMessageData.setAction(ActivityPollingConstants.Action.REFRESH_ALL);
            activityLongPollingService.sendPollingMessage(pollingMessageData);
        }catch (Exception e){
            log.error("handleAiSuggestion e",e);
        }finally {
            sfaLtoRedisService.releaseLock(msg.getTenantId(),msg.getObjectApiName(),msg.getObjectActionCode(),msg.getKey(),msg.getRedisRequestId());
        }
    }


    /**
     * 附件上传的文档语料分片提取互动话题
     * @param user
     * @param arg
     * @param corpus
     */
    public List<AIQuestionConstants.InteractionResult> handleFragmentationSummaryNotRealTime(User user,InteractionModel.Arg arg,String corpus,String propApiName,Map<String, Object> sceneParamMap){
        List<String> chunks = new ArrayList<>();
        if (corpus == null || corpus.isEmpty()) {
            return null; // 空字符串返回空列表
        }

        int length = corpus.length();
        for (int i = 0; i < length; i += NOT_REALTIME_FRAGMENTATION_BYTE_SIZE) {
            int end = Math.min(i + NOT_REALTIME_FRAGMENTATION_BYTE_SIZE, length);
            chunks.add(corpus.substring(i, end));
        }
        List<AIQuestionConstants.InteractionResult> allQuestionList =new ArrayList<>();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        for(String str :chunks){
            parallelTask.submit(() -> {
                Map<String, Object>  map = new HashMap<>();
                map.putAll(sceneParamMap);
                String  resultAi = handleAiProxy(user,arg,str,propApiName,map);
                if (ObjectUtils.isEmpty(resultAi)){
                    log.warn("handleFragmentationSummaryNotRealTime result is null content:{}",resultAi);
                    return;
                }
                allQuestionList.addAll(formatByAiResultStr(user,resultAi,arg));
            });
        }
        try {
            parallelTask.await(600, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("handleFragmentationSummaryNotRealTime error e:", e);
        }
        List<String> questionLists = allQuestionList.stream().map(AIQuestionConstants.InteractionResult::getQuestion).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(questionLists)){
            log.warn("handleFragmentationSummaryNotRealTime questionLists is empty");
            return allQuestionList;
        }
        String resultAi = handleAiProxy(user,arg,JSONObject.toJSONString(questionLists),"prompt_interactiveIssues_filter_repeat",sceneParamMap);
        questionLists = JSONObject.parseArray(resultAi, String.class);
        if(CollectionUtil.isEmpty(questionLists)){
            return allQuestionList;
        }

        List<String> finalQuestionLists = questionLists;
        Map<String,AIQuestionConstants.InteractionResult>  interactionResultMap =allQuestionList.stream().collect(Collectors.toMap(AIQuestionConstants.InteractionResult::getQuestion,Function.identity(),(v1, v2)->v1));
        List<AIQuestionConstants.InteractionResult> resultList = new ArrayList<>();
        finalQuestionLists.stream().forEach(result->{
            if(interactionResultMap.containsKey(result)){
                resultList.add(interactionResultMap.get(result));
            }
        });
        return resultList;
    }

    public String repairJSONStr(User user,InteractionModel.Arg arg,String errorAiResult){
        Map<String, Object> sceneParamMap = new HashMap<>();
        sceneParamMap.put("invalidJson",errorAiResult);
        sceneParamMap.put("jsonFormat",JSON_QUESTION_LIST);
        String resultAi = activitySummaryService.getAiComplete(user,"prompt_fix_invalid_json_by_format",sceneParamMap,arg.getActiveRecordId());
        resultAi = activitySummaryService.captureAiResult(resultAi);
        return resultAi;
    }

    public List<AIQuestionConstants.InteractionResult> formatByAiResultStr(User user,String resultAi,InteractionModel.Arg arg ){
        List<AIQuestionConstants.InteractionResult>  resultList = new ArrayList<>();
        if(resultAi.startsWith("{") && resultAi.endsWith("}")){
            resultAi = "["+resultAi+"]";
        }
        try {
            resultList = JSONObject.parseArray(resultAi, AIQuestionConstants.InteractionResult.class);
        }catch (Exception e){
            log.warn("handleInteractiveIssuesService formatByAiResultStr parseArray error:{}",resultAi);
            String repairDataStr =  repairJSONStr(user,arg,resultAi);
            log.warn("handleInteractiveIssuesService repairJSONStr parseArray error:{}",repairDataStr);
            resultList = JSONObject.parseArray(repairDataStr, AIQuestionConstants.InteractionResult.class);
        }
        return resultList;
    }


    /**
     * 给每一个话题找到对应的用户
     * @param user
     * @param activeRecordId
     * @param resultList
     */
    public void setUser(User user,String activeRecordId, List<AIQuestionConstants.InteractionResult> resultList){
        if(CollectionUtil.isEmpty(resultList)){
            return;
        }
        Set<Long> seqList = new HashSet<>();
        resultList.stream().forEach(x->{
           if(ObjectUtils.isNotEmpty(x.getQuestionSeq())){
               try {
                   seqList.add(Long.valueOf(x.getQuestionSeq()));
               }catch (Exception e){
                   log.warn("handleInteractiveIssuesService getQuestionSeq error:{}",x.getQuestionSeq());
               }

           }
           if(CollectionUtil.isNotEmpty(x.getAnswerSeqList())){
                seqList.addAll(x.getAnswerSeqList().stream()
                        .map(s -> {
                            try {
                                return Long.parseLong(s);
                            } catch (Exception e) {
                                log.warn("handleInteractiveIssuesService getAnswerSeqList error:{}",s);
                                return null; // 标记无效值
                            }
                        })
                        .filter(Objects::nonNull) // 过滤掉null（无效值）
                        .collect(Collectors.toList()));
            }
        });
        if(CollectionUtil.isEmpty(seqList)){
            log.warn("setUser seqList is null");
            return;
        }
        List<InteractiveDocument> interactiveDocuments = activityMongoDao.queryListByActiveRecordIdAndSeq(user.getTenantId(),activeRecordId,Lists.newArrayList(seqList));
        if(CollectionUtil.isEmpty(interactiveDocuments)){
            log.warn("setUser interactiveDocuments is null");
            return;
        }
        interactiveDocuments  = interactiveDocuments.stream().filter(x->ObjectUtils.isNotEmpty(x.getSeq()) && ObjectUtils.isNotEmpty(x.getActivityUserId())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(interactiveDocuments)){
            log.warn("setUser interactiveDocuments filter is null");
            return;
        }
        Map<Long,String> activityUserIdMap = interactiveDocuments.stream().collect(Collectors.toMap(InteractiveDocument::getSeq,InteractiveDocument::getActivityUserId));
        resultList.stream().forEach(x->{
            if(ObjectUtils.isNotEmpty(x.getQuestionSeq())){
                Long seq = Long.valueOf(x.getQuestionSeq());
                if(activityUserIdMap.containsKey(seq)){
                    x.setQuestionUser(Lists.newArrayList(activityUserIdMap.get(seq)));
                }else{
                    log.warn("setUser setQuestionUser seq is null seq:{}",seq);
                }
            }
            if(CollectionUtil.isNotEmpty(x.getAnswerSeqList())){
                List<String> answerUsers = new ArrayList<>();
                x.getAnswerSeqList().stream().forEach(q->{
                    Long seq = Long.valueOf(q);
                    if(activityUserIdMap.containsKey(Long.valueOf(seq))){
                        answerUsers.add(activityUserIdMap.get(seq));
                    }else{
                        log.warn("setUser setAnswerUser seq is null seq:{}",seq);
                    }
                });
                x.setAnswerUser(answerUsers);
            }
        });
    }

    public String removeSpace(String str){
        return str.replaceAll(" ","");
    }




    public void handleChangeSpeaker(InteractionModel.ChangeSpeakerMsg msg,List<InteractiveDocument> documents){
        User user = new User(msg.getTenantId(), CommonConstant.SUPER_USER);
        List<String> modifiedSeqList = documents.stream().map(y->String.valueOf(y.getSeq())).collect(Collectors.toList());
        List<IObjectData> needUpdateList = new ArrayList<>();
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(500);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AIQuestionConstants.Field.active_record_id, msg.getObjectId());
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, CommonConstant.ACTIVITY_QUESTION_API_NAME, searchTemplateQuery);
        if(ObjectUtils.isNotEmpty(queryResult) || CollectionUtils.notEmpty(queryResult.getData())){
            queryResult.getData().stream().forEach(x->{
                //根据提问人查询有哪些要修改的问题
                if(ObjectUtils.isNotEmpty(x.get(AIQuestionConstants.Field.question_seq))){
                    String question_seq = x.get(AIQuestionConstants.Field.question_seq).toString();
                    if(modifiedSeqList.contains(question_seq)){
                        needUpdateList.add(x);
                    }
                }
                //根据回答人查询
                try {
                    if(ObjectUtils.isNotEmpty(x.get(AIQuestionConstants.Field.answer_seq))){
                        List<String> questionSeqList = JSONObject.parseArray(x.get(AIQuestionConstants.Field.answer_seq).toString(),String.class);
                        for(String y:questionSeqList){
                            if(modifiedSeqList.contains(y)){
                                needUpdateList.add(x);
                                break;
                            }
                        }
                    }
                }catch (Exception e){
                    log.warn("handleChangeSpeaker error x:{},e:",x.getId(),e);
                }

            });
        }

        if (CollectionUtils.empty(needUpdateList)){
            log.info("needUpdateList is empty");
            return;
        }
        //组装新的seq
        Set<String> seqSet = new HashSet<>();
        needUpdateList.stream().forEach(x->{
            if(ObjectUtils.isNotEmpty(x.get(AIQuestionConstants.Field.question_seq))){
                String question_seq = x.get(AIQuestionConstants.Field.question_seq).toString();
                seqSet.add(question_seq);
            }
            if(ObjectUtils.isNotEmpty(x.get(AIQuestionConstants.Field.answer_seq))){
                List<String> answerSeq = JSONObject.parseArray(x.get(AIQuestionConstants.Field.answer_seq).toString(),String.class);
                seqSet.addAll(answerSeq);
            }
        });
        List<InteractiveDocument> interactiveDocumentList = activityMongoDao.queryListByActiveRecordIdAndSeq(msg.getTenantId(),msg.getObjectId(),seqSet.stream().map(Long::valueOf).collect(Collectors.toList()));
        Map<String,String> seqOfActivityUserIdMap = interactiveDocumentList.stream().collect(Collectors.toMap(y->String.valueOf(y.getSeq()), InteractiveDocument::getActivityUserId));
        needUpdateList.stream().forEach(x->{
            if(ObjectUtils.isNotEmpty(x.get(AIQuestionConstants.Field.question_seq))){
                String question_seq = x.get(AIQuestionConstants.Field.question_seq).toString();
                x.set(AIQuestionConstants.Field.question_user,Lists.newArrayList(seqOfActivityUserIdMap.get(question_seq)));
            }
            if(ObjectUtils.isNotEmpty(x.get(AIQuestionConstants.Field.answer_seq))){
                List<String> answerSeq = JSONObject.parseArray(x.get(AIQuestionConstants.Field.answer_seq).toString(),String.class);
                Set<String> answerNewSet = new HashSet<>();
                answerSeq.stream().forEach(y->{
                    answerNewSet.add(seqOfActivityUserIdMap.get(y));
                });
                x.set(AIQuestionConstants.Field.answer_user,Lists.newArrayList(answerNewSet));
            }

        });
        serviceFacade.batchUpdateByFields(user, needUpdateList, Lists.newArrayList(AIQuestionConstants.Field.question_user,AIQuestionConstants.Field.answer_user));
    }
}
