<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean class="com.fxiaoke.paas.gnomon.api.NomonProducer"/>

    <bean id="featureNodeProducer" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer" init-method="start">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="crm-feature-node-producer"/>
    </bean>

    <bean id="attendeeInsightsProducer" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start" destroy-method="close">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="sfa-activity-attendee-insights-producer"/>
    </bean>

    <bean id="licenseParaChange" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start" destroy-method="close">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="license_para_change"/>
    </bean>
</beans>
