package com.facishare.crm.task.sfa.activitysummary.enums;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/17 11:20
 * @description: 附件类型
 */
public enum FileTypeEnum {
    DOC("doc", "doc"),
    TXT("txt", "txt"),
    DOCX("docx", "docx"),
    PPT("ppt", "ppt"),
    PPTX("pptx", "pptx"),
    PDF("pdf", "pdf"),
    WAV("wav", "wav"),
    MP3("mp3", "mp3"),
    M4A("m4a", "m4a"),
    FLV("flv", "flv"),
    MP4("mp4", "mp4"),
    WMA("wma", "wma"),
    GP3("3gp", "3gp"),
    AMR("amr", "amr"),
    AAC("aac", "aac"),
    OGG_OPUS("ogg-opus", "ogg-opus"),
    FLAC("flac", "flac"),
    PNG("png", "png"),
    JPG("jpg", "jpg"),
    JPEG("jpeg", "jpeg");

    private String code;
    private String desc;

    FileTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FileTypeEnum getByCode(String code) {
        for (FileTypeEnum value : values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }



}
