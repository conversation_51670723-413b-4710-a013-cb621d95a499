package com.facishare.crm.task.sfa.correct;

import com.facishare.crm.task.sfa.correct.dict.Confusion;
import com.facishare.crm.task.sfa.correct.dict.Stroke;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

class ProperCorrectorTest {

    static ProperCorrector CORRECTOR;

    @BeforeAll
    static void beforeAll() throws IOException {
        Properties props = new Properties();
        props.load(ProperCorrector.class.getResourceAsStream("/correct.properties"));
        SpringConfig config = new SpringConfig();
        config.setStrokeDictPath(props.getProperty("path.dict.stroke"));
        config.setConfusionEnDictPath(props.getProperty("path.dict.confusion.en"));
        Confusion confusion = new Confusion(config);
        confusion.afterPropertiesSet();
        Stroke stroke = new Stroke(config);
        stroke.afterPropertiesSet();
        CORRECTOR = new ProperCorrector(stroke, confusion);
        List<ConfusionWord> list = new ArrayList<>();
        list.add(new ConfusionWord("CM", "CRM", Level.Word));
        list.add(new ConfusionWord("C M", "CRM", Level.Word));
        list.add(new ConfusionWord("TPs", "TPM", Level.Sent));
        CONF.merge(list);
    }

    @Test
    void isNearPinyin() {
        Assertions.assertTrue(CORRECTOR.isNearPinyin("hua", "fa"));
        Assertions.assertTrue(CORRECTOR.isNearPinyin("re", "le"));
        Assertions.assertTrue(CORRECTOR.isNearPinyin("ke", "ge"));
        Assertions.assertTrue(CORRECTOR.isNearPinyin("le", "ne"));
        Assertions.assertTrue(CORRECTOR.isNearPinyin("zhong", "zong"));
        Assertions.assertTrue(CORRECTOR.isNearPinyin("chi", "ci"));
        Assertions.assertTrue(CORRECTOR.isNearPinyin("hang", "kan"));
        Assertions.assertTrue(CORRECTOR.isNearPinyin("keng", "ken"));
        Assertions.assertTrue(CORRECTOR.isNearPinyin("xing", "xin"));
        Assertions.assertTrue(CORRECTOR.isNearPinyin("liang", "lian"));
        Assertions.assertTrue(CORRECTOR.isNearPinyin("kuang", "kuan"));
        Assertions.assertFalse(CORRECTOR.isNearPinyin("jing", "xing"));
    }

    @Test
    void testGetStroke() {
        Assertions.assertEquals("szhshnhhpz", CORRECTOR.getStroke('蚖'));
        Assertions.assertEquals("phnphnszsshzznnnnpsnhhhsh", CORRECTOR.getStroke('籮'));
        Assertions.assertEquals("h", CORRECTOR.getStroke('一')); // 一 19968
        Assertions.assertEquals("pzhzhhz", CORRECTOR.getStroke('龟')); // 龟 40863
        Assertions.assertNull(CORRECTOR.getStroke('A'));
    }

    @Test
    void test1() {
        Assertions.assertEquals("我们是一家SaaS公司，提供CRM产品", CORRECTOR.correct("我们是一家SaS公司，提供CM产品", PROP, CONF));
        Assertions.assertEquals("比如说我是一个 SaaS 行业的销售，你我吧，我看真实情况，我是 SaaS 行业的一个销售", CORRECTOR.correct("比如说我是一个 saas 行业的销售，你我吧，我看真实情况，我是 SaS 行业的一个销售", PROP, CONF));
        Assertions.assertEquals("我们是一家SaaS CRM公司", CORRECTOR.correct("我们是一家SaS CM公司", PROP, CONF));
    }

    @Test
    void test2() {
        Assertions.assertEquals(
                "那这块呢，我们呢也是像我们这边这个SaaS,的这种这个一体化解决方案呢，预测性维护,我们也是在我们的高科技服务业这个领域里面。那这个呢是我们纷享销客的一个产品CRM的介绍，我们产品呢是基于营销服务一体化的一种解决方案，前端呢会有一些营销获客的一些管理，比如说线上的一些广告投放的，然后包括线上线下的一些引流，然后还有一些这个全员营销伙伴营销等等的内容，那中间呢是我们的整个LTOC的一个管理，从线索到客户到商机。再到报价交易，然后呢，这块的整个的管理在后端呢，我们也是有这个整个的项目的管理以及服务的管理内容，包括在后面呢，价值流图,还有我们的一块价值的延伸，比如说我们的客户的运营，口碑运营等等。那这些我们整个的这个产品呢，精益生产,都是基于我们的pass平台来去做这种建立的，那我们的pass平台呢，也是具备这种这个无代码，低代码，然后或者这个部分的高代码的这个，这种开发的能力，当然像我们的。一般的这种CRM的需求呢，我们都可以通过这种无代码或者低代码的形式来实现。我们的产品是离不开我们强大的技术平台的，我们可以看到我们的技术平台的这块呢，可以通过刚才也说了这种低代码的能力，通过这种拖拉拽的这种能力来去实现我们大部分的这个需求。5s,TPM，,精益生产,瓶颈工序,SaP,在制品,防错法,单件流,看板,六西格玛,循环时间,物料需求计划,柔性制造系统,统计过程控制,质量追溯系统,MTBF,MTTR,预测性维护,预防性维护,,润滑五定,设备点检,三级保养制度,设备稼动率,TPM八大支柱,故障树分析,设备生命周期成本,dead line, ,BOM,ERP,交叉理货,安全库存.待会呢，也可以给您看一下我们的后台的一个演示，那那一块呢，就是我们的集成平台，我们菲尔克的这个产品呢，也是有一个强大的集成平台的，像主流的这些SAP用友这个oracle等等这些金蝶这块呢，我们都是已经做了这种基础.供应商分级管理,循环取货，备件ABC分类,数字孪生，如果您使用这些软件需要对接的话，那我们也是可以免除这个打通的一个费用，那这个呢是我们的技术平台的一个展示，那接下来呢，是我们纷享销客的一个安全机制的保障。安灯系统,我们看到这个logo墙也合作了几十家的这种安全行业的这些企业，那他们在使用我们的产品的时候呢，也是为我们的产品做了这种。这个渗透的一些测试，这个呢是我们这个现代服务行业的一个logo墙，我们可以看到我们也是合作了很多这种这种SaaS服务的这些企业，比如说像分贝通，像美团，然后还有像这个友善可能跟咱们会相对比较像这个。都是我们合作目前合作的一些企业，那这块呢，待会我们也可以就有赞的这个我们之前做的一些内容来交流一下。",
                CORRECTOR.correct(
                        "那这块呢，我们呢也是像我们这边这个sas,的这种这个一体化解决方案呢，预侧性维护,我们也是在我们的高科技服务业这个领域里面。那这个呢是我们分享销客的一个产品CM的介绍，我们产品呢是基于营销服务一体画的一种解决方案，前端呢会有一些营销获客的一些管理，比如说线上的一些广告投放的，然后包括线上线下的一些引流，然后还有一些这个全员营销伙伴营销等等的内容，那中间呢是我们的整个LTOC的一个管理，从线索到客户到商机。再到报价交易，然后呢，这块的整个的管理在后端呢，我们也是有这个整个的项目的管理以及服务的管理内容，包括在后面呢，价值留图,还有我们的一块价值的延伸，比如说我们的客户的运营，口碑运营等等。那这些我们整个的这个产品呢，精一生产,都是基于我们的pass平台来去做这种建立的，那我们的pass平台呢，也是具备这种这个无代码，低代码，然后或者这个部分的高代码的这个，这种开发的能力，当然像我们的。一般的这种CM的需求呢，我们都可以通过这种无代码或者低代码的形式来实现。我们的产品是离不开我们强大的技术平台的，我们可以看到我们的技术平台的这块呢，可以通过刚才也说了这种低代码的能力，通过这种拖拉拽的这种能力来去实现我们大部分的这个需求。5s,TPs，,精一生产,瓶劲工序,SaP,再制品,防错发,单件流,看版,六西个玛,循环时间,物料需求计划,柔性制造系统,统计过程控制,质量追诉系统,MTBF,MTTR,预测性维护,预防星维护,,润滑五定,设备点检,三级保养制度,设备稼动率,TPM八大支柱,故障树分析,设备生命周期成本,dead lin, ,BOM,ERP,交叉离货,安全库存.待会呢，也可以给您看一下我们的后台的一个演示，那那一块呢，就是我们的集成平台，我们菲尔克的这个产品呢，也是有一个强大的集成平台的，像主流的这些SAP用有这个oracle等等这些金蝶这块呢，我们都是已经做了这种基础.供应商分级管理,循环区货，备间ABC分类,数字孪生，如果您使用这些软件需要对接的话，那我们也是可以免除这个打通的一个费用，那这个呢是我们的技术平台的一个展示，那接下来呢，是我们分享销客的一个安全机制的保障。暗灯系统,我们看到这个logo墙也合作了几十家的这种安全行业的这些企业，那他们在使用我们的产品的时候呢，也是为我们的产品做了这种。这个渗透的一些测试，这个呢是我们这个现代服务行业的一个logo墙，我们可以看到我们也是合作了很多这种这种sas服务的这些企业，比如说像分倍通，像美团，然后还有像这个友善可能跟咱们会相对比较像这个。都是我们合作目前合作的一些企业，那这块呢，待会我们也可以就有赞的这个我们之前做的一些内容来交流一下。",
                        ProperWords.withSim(Arrays.asList("纷享销客", "一体化", "SAAS", "PAAS", "CRM", "分贝通", "用友", "dead line", "预测性维护", "TPM", "数字孪生", "精益生产", "节拍时间", "瓶颈工序", "在制品", "防错法", "单件流", "看板", "OEE", "价值流图", "快速换模", "六西格玛", "安灯系统", "首件检验", "循环时间", "物料需求计划", "柔性制造系统", "统计过程控制", "预防性维护", "备件ABC分类", "交叉理货", "循环取货", "质量追溯系统", "BOM", "ERP", "安全库存", "供应商分级管理"), 0.85f), CONF)
        );
    }

    @Test
    void test3() {
        List<ConfusionWord> list = new ArrayList<>();
        list.add(new ConfusionWord("CM", "CRM", Level.Word));
        list.add(new ConfusionWord("C M", "CRM", Level.Word));
        list.add(new ConfusionWord("scm", "scrm", Level.Word));
        String correct = CORRECTOR.correct("CDM,scm,CM,C M;;MCMCP.C M", ProperWords.withSim(Collections.singletonList("捷安高科"), 0.85f), new ConfusionWords(list));
        Assertions.assertEquals("CDM,scrm,CRM,CRM;;MCMCP.CRM", correct);
    }

    @Test
    void test4() {
        List<ConfusionWord> list = new ArrayList<>();
        list.add(new ConfusionWord("吉安高科", "捷安高科", Level.Sent));
        String correct = CORRECTOR.correct("安高科吉安高吉安高科安高科吉安高-专业的虚拟仿真培训解决方案", ProperWords.withSim(Collections.singletonList("捷安高科"), 0.85f), new ConfusionWords(list));
        Assertions.assertEquals("安高科吉安高捷安高科安高科吉安高-专业的虚拟仿真培训解决方案", correct);
    }

    @Test
    void test5() {
        ProperWords properWords = ProperWords.withSim(Arrays.asList("POSM物料", "促销ROI", "VMI管理", "TIG 焊接"), 0.85f);
        String correct = CORRECTOR.correct("POSM勿料-TIG 焊结", properWords, new ConfusionWords());
        Assertions.assertEquals("POSM物料-TIG 焊接", correct);
    }

    @Test
    @Disabled
    void testBatch() throws IOException {
        File file = new File("C:/Users/" + System.getenv("USERNAME") + "/Desktop/record.txt");
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            List<String> res = CORRECTOR.batchCorrect(reader.lines().collect(Collectors.toList()), PROP, CONF);
            System.out.println(String.join("\n", res));
        }
    }

    @Test
    @Disabled
    void testBatchInLine() throws IOException {
        File file = new File("C:/Users/" + System.getenv("USERNAME") + "/Desktop/record.txt");
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String collect = reader.lines().collect(Collectors.joining("\n"));
            System.out.println(CORRECTOR.correct(collect, PROP, CONF));
        }
    }

    @AfterAll
    static void afterAll() {
        CORRECTOR = null;
    }

    private static final ProperWords PROP = ProperWords.withSim(Arrays.asList("纷享销客", "用友", "国知局", "SaaS", "B2B", "B2C", "B4B", "CAC", "CRC", "LTV", "ACV", "ARR", "MRR", "ACRC", "SDR", "BDR", "AE",
            "AM", "CSM", "FAE", "MDR", "PM", "VP Marketing", "VP Sales", "CRO", "CCO", "Suspect", "Prospect", "MQL", "SQL", "SAL", "WIN", "LIVE", "PTC", "Win Ratio", "Enterprises",
            "SME", "SMB", "VSB", "Prosumer", "CRM", "CSM", "MAS", "千禧一代", "客户旅程", "以客户为中心", "销售漏斗", "线索培育", "内容营销", "社交媒体营销", "电子邮件营销", "网络研讨会", "SEO", "SEM", "SaaS 3.0", "全能销售单元",
            "销售运营中心", "自学性组织"), 0.85f);

    private static final ConfusionWords CONF = new ConfusionWords();
}
