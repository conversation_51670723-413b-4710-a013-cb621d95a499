package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.bizfeature.constant.ParseRuleConstants;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.crm.task.sfa.bizfeature.model.RuleWhere;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Component
public class ParseCommonAggRule extends AbsParseRuleService {

    @Override
    public String getRuleType() {
        return ParseRuleConstants.CalcMethodType.COMMON_AGG.getCalcMethodType();
    }

    @Override
    protected FeatureModel.ParseValueData getValue(User user, IObjectData feature, IObjectData rule,
            IObjectData afterData, IObjectDescribe dataDescribe) {
        String ruleContent = rule.get(ParseRuleConstants.RULE_CONTENT, String.class);
        FeatureModel.AggCondition aggCondition = JSON.parseObject(ruleContent, FeatureModel.AggCondition.class);
        FeatureModel.ParseExtFilter parseExtFilter = getExtFilter(user, afterData, aggCondition);
        if (parseExtFilter.isHasErr()) {
            return null;
        }
        List<IFilter> extFilter = parseExtFilter.getFilters();

        BigDecimal ret = getAggData(user, aggCondition, extFilter, false);
        if (null == ret) {
            return null;
        }

        if (FeatureModel.AggWay.SUM_RATE.getValue().equals(aggCondition.getAggWay())
                || FeatureModel.AggWay.COUNT_RATE.getValue().equals(aggCondition.getAggWay())) {
            BigDecimal sum = getAggData(user, aggCondition, extFilter, true);
            if (null == sum) {
                return null;
            }
            if (BigDecimal.ZERO.equals(sum)) {
                ret = BigDecimal.ZERO;
            }else {
                ret = ret.divide(sum, 8, RoundingMode.HALF_UP);
            }
        }

        FeatureModel.ParseValueData data = new FeatureModel.ParseValueData();
        data.setValue(ret);
        return data;

    }

    protected BigDecimal getAggData(User user, FeatureModel.AggCondition aggCondition, List<IFilter> extFilters,
            boolean isSum) {
        SearchTemplateQueryPlus featureValueSearchQuery = SearchUtil.buildBaseSearchQuery();
        featureValueSearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");
        if (CollectionUtils.notEmpty(extFilters)) {
            featureValueSearchQuery.getFilters().addAll(extFilters);
        }

        String countField = "sum_" + aggCondition.getAggField();

        AggFunctionArg aggFunctionArg = AggFunctionArg.builder()
                .aggFunction(Count.TYPE_SUM)
                .aggField(aggCondition.getAggField())
                .build();

        if (FeatureModel.AggWay.COUNT.getValue().equals(aggCondition.getAggWay())
                || FeatureModel.AggWay.COUNT_RATE.getValue().equals(aggCondition.getAggWay())) {
            countField = "groupbycount";
            aggFunctionArg.setAggFunction(Count.TYPE_COUNT);
        }

        List<RuleWhere> where = aggCondition.getCondition();
        if (isSum) {
            where = aggCondition.getSumCondition();
        }

        featureValueSearchQuery.setWheres(RuleWhere.transformRuleWheres(where));

        //List<String> groupByList = Lists.newArrayList(aggCondition.getDimension());
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setAggFunctions(Lists.newArrayList(aggFunctionArg));
        //groupByParameter.setGroupBy(groupByList);
        featureValueSearchQuery.setGroupByParameter(groupByParameter);
        List<IObjectData> dList = serviceFacade.aggregateFindBySearchQuery(user, featureValueSearchQuery,
                aggCondition.getAggObject());
        BigDecimal ret = null;
        if (CollectionUtils.notEmpty(dList)) {
            ret = dList.get(0).get(countField, BigDecimal.class);
        }
        return ret;
    }

    protected FeatureModel.ParseExtFilter getExtFilter(User user, IObjectData afterData,
            FeatureModel.AggCondition aggCondition) {
        FeatureModel.ParseExtFilter ret = FeatureModel.ParseExtFilter.builder().hasErr(false).build();
        String dimensionData = afterData.get(aggCondition.getDimension(), String.class);
        if (StringUtils.isBlank(dimensionData)) {
            ret.setHasErr(true);
        } else {
            IFilter filter = SearchTemplateQueryPlus.getFilter(aggCondition.getDimension(), Operator.EQ, dimensionData);
            ret.setFilters(Lists.newArrayList(filter));
        }

        return ret;
    }
}
