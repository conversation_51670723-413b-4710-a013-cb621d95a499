package com.facishare.crm.task.sfa.xxl;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureTaskConstants;
import com.facishare.crm.task.sfa.bizfeature.service.FeatureEngineService;
import com.facishare.crm.task.sfa.bizfeature.service.dao.mongo.FeatureTaskDao;
import com.facishare.crm.task.sfa.bizfeature.service.dao.mongo.FeatureTaskDocument;
import com.facishare.crm.task.sfa.bizfeature.service.dao.mongo.FeatureTaskInitDao;
import com.facishare.crm.task.sfa.bizfeature.service.dao.mongo.FeatureTaskInitDocument;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
@JobHander(value = "FeatureTaskJobHandler")
public class FeatureTaskJobHandler extends IJobHandler {

    @Resource
    private FeatureEngineService featureEngineService;

    @Resource
    private FeatureTaskInitDao featureTaskInitDao;

    @Resource
    private FeatureTaskDao featureTaskDao;

    @Override
    public ReturnT<String> execute(TriggerParam params) {
        log.warn("FeatureTaskJobHandler#execute job start");
        List<FeatureTaskInitDocument> featureTaskInitDocumentList = featureTaskInitDao.findAllTasks();
        for (FeatureTaskInitDocument featureTaskInitDocument : featureTaskInitDocumentList) {
            FeatureTaskDocument featureTaskDocument = new FeatureTaskDocument();
            featureTaskDocument.setTenantId(featureTaskInitDocument.getTenantId());
            try {
                if (CollectionUtils.isNotEmpty(featureTaskDao.findExistJob(featureTaskInitDocument.getTenantId()))) {
                    continue;
                }

                featureTaskDocument.setStatus(FeatureTaskConstants.TaskStatus.RUNNING);
                featureTaskDao.saveOrUpdate(featureTaskDocument);
                featureEngineService.generateJob(featureTaskInitDocument.getTenantId());
                featureTaskDocument.setStatus(FeatureTaskConstants.TaskStatus.FINISHED);
            } catch (Exception e) {
                log.warn("FeatureTaskJobHandler#execute job fail{}{}", featureTaskInitDocument.getTenantId(), e.getMessage());
                featureTaskDocument.setFailReason(e.getMessage());
                featureTaskDocument.setStatus(FeatureTaskConstants.TaskStatus.FAIL);
            }

            try {
                featureTaskDao.saveOrUpdate(featureTaskDocument);
            } catch (Exception e) {
                log.warn("FeatureTaskJobHandler#saveOrUpdate job fail{}{}", featureTaskInitDocument.getTenantId(), e.getMessage());
            }

        }

        log.warn("FeatureTaskJobHandler#execute job end");
        return ReturnT.SUCCESS;
    }
}
