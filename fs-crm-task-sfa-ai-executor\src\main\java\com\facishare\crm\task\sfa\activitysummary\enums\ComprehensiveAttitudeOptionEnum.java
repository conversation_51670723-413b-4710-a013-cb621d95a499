package com.facishare.crm.task.sfa.activitysummary.enums;

public enum ComprehensiveAttitudeOptionEnum {
    STRONG_SUPPORT("101", "强支持"),
    WEAK_SUPPORT("102", "弱支持"),
    NEUTRALITY("103", "中立"),
    WEAK_OPPOSITION("104", "弱反对"),
    STRONGLY_OPPOSE("105", "强反对"),;

    private String code;
    private String desc;

    ComprehensiveAttitudeOptionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
