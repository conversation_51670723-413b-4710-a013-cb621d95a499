package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ProfileConstants;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileAdviceMqModel;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileScoreModel;
import com.facishare.crm.task.sfa.util.ProfileUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Component
@Slf4j
public class NomonProfileScoreService extends ProfileScoreService {

    /**
     * 定时计算
     */
    @Override
    public void scoreCalc(ProfileScoreModel profileScoreModel) {
        log.info("nomon profile scoreCalc, profileScoreModel:{}", profileScoreModel);
        User user = User.systemUser(profileScoreModel.getTenantId());
        // 查哪些方法论实例数据进行计算  --待定
        List<IObjectData> methodologyInstanceList = findNeedCalcData(user, profileScoreModel.getMethodologyInstanceIds());
        if (CollectionUtils.isEmpty(methodologyInstanceList)) {
            log.info("methodologyInstanceList is empty, tenantId:{}", user.getTenantId());
            return;
        }
        Lists.partition(methodologyInstanceList, 20).forEach(instanceList -> {
            Map<String, List<String>> methodologyToObjectApiNameMap = Maps.newHashMap();
            instanceList.forEach(methodologyInstance -> {
                List<String> objectApiNames = ProfileUtil.methodologyToObjectApiName(methodologyInstance);
                methodologyToObjectApiNameMap.put(methodologyInstance.getId(), objectApiNames);
            });
            try {
                commonScoreCalc(user, methodologyToObjectApiNameMap, instanceList);
            } catch (Exception e) {
                log.error("nomon profile scoreCalc error, tenantId:{} methodologyToObjectApiNameMap:{}", profileScoreModel.getTenantId(), methodologyToObjectApiNameMap, e);
            }
        });
    }

    public List<IObjectData> findNeedCalcData(User user, List<String> methodologyInstanceIds) {
        //待定
        SearchTemplateQueryPlus methodologyInstanceSearchQuery = SearchUtil.buildBaseSearchQuery();
        if (CollectionUtils.isNotEmpty(methodologyInstanceIds)) {
            SearchTemplateQueryExt.of(methodologyInstanceSearchQuery).addFilter(Operator.IN, DBRecord.ID, methodologyInstanceIds);
        }
        methodologyInstanceSearchQuery.setOrders(Lists.newArrayList(SearchUtil.orderByLastModifiedTime()));
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.METHODOLOGY_INSTANCE, methodologyInstanceSearchQuery).getData();
    }

    /**
     * 保存画像数据
     */
    @Override
    public void saveProfileDate(User user, List<IObjectData> addProfileList, List<IObjectData> updateProfileList,
                                List<IObjectData> addProfileItemScoreList) {
        // 定时计算，直接新增画像 和 修改历史画像的 是否最新画像 字段
        if (CollectionUtils.isNotEmpty(updateProfileList)) {
            updateProfileList.forEach(updateProfile -> updateProfile.set(ProfileConstants.IS_LATEST, false));
            serviceFacade.batchUpdateByFields(user, updateProfileList, Lists.newArrayList(ProfileConstants.IS_LATEST));
        }
        // 保存 画像
        if (CollectionUtils.isNotEmpty(addProfileList)) {
            addProfileQuotaLimit(user, addProfileList);
        }
        // 分批保存 画像各项得分
        if (CollectionUtils.isNotEmpty(addProfileItemScoreList)) {
            Lists.partition(addProfileItemScoreList, 100).forEach(profileItemScoreList -> serviceFacade.bulkSaveObjectData(profileItemScoreList, user));
        }
    }

    @Override
    public void processAddOrUpdateProfile(IObjectData profile, IObjectData existProfile, List<IObjectData> addProfileList, List<IObjectData> updateProfileList) {
        addProfileList.add(profile);
        if (existProfile != null) {
            updateProfileList.add(existProfile);
        }
    }

    @Override
    public void sendProfileAdvice(User user, List<IObjectData> addProfileList, List<IObjectData> updateProfileList) {
        addProfileList.forEach(profile -> {
            ProfileAdviceMqModel.Message profileAdviceMqModel = ProfileAdviceMqModel.Message.builder()
                    .tenantId(user.getTenantId())
                    .profileId(profile.getId())
                    .refreshType(ProfileConstants.RefreshType.TIMING.getValue())
                    .receiverId(user.getUserId())
                    .build();
            profileAdviceProducer.sendMessage(profileAdviceMqModel);
        });
    }
}
