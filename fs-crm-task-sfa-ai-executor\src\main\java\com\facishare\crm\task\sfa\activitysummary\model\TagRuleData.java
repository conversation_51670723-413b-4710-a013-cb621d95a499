package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 标签规则数据模型
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TagRuleData {
    /**
     * 标签ID
     */
    private String id;
    
    /**
     * 标签名称
     */
    private String name;
    
    /**
     * 正向判断标准
     */
    private String positiveCriteria;
    
    /**
     * 负向判断标准
     */
    private String negativeCriteria;
    
    /**
     * 正向样例
     */
    private String positiveSample;
    
    /**
     * 负向样例
     */
    private String negativeSample;
    
    /**
     * 关注方
     */
    private String follower;

    /**
     * 是否关注
     */
    private boolean focus;
}