package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 评估模型
 * 用于AI Agent的Evaluator组件，记录分段结果的评估结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationModel {
    /**
     * 总体评分
     * 对分段结果的整体质量评分，通常由各维度评分平均得出，范围0-1
     */
    private double overallScore;
    
    /**
     * 维度评分
     * 各个维度的详细评分，包括连贯性、摘要质量、分段数量合理性和段落区分度
     */
    private DimensionScores dimensionScores;
    
    /**
     * 问题列表
     * 分段结果存在的问题，如段落过长、主题混杂等
     */
    private List<String> issues;
    
    /**
     * 建议列表
     * 改进分段结果的建议，如拆分某段、提高摘要质量等
     */
    private List<String> suggestions;
    
    /**
     * 是否需要修正
     * 指示当前分段结果是否需要进一步调整
     */
    private boolean needsRevision;
    
    /**
     * 调整计划
     * 具体的调整建议，包括合并/拆分段落、理想段落数量等
     */
    private AdjustmentPlan adjustmentPlan;
}
