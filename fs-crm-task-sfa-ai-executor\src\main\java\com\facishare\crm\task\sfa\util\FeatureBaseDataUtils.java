package com.facishare.crm.task.sfa.util;

import com.facishare.crm.task.sfa.bizfeature.constant.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

@Slf4j
public class FeatureBaseDataUtils {
    protected static Map<String,List<Map<String, Object>>> jsonDataMap;
    public static List<String> vaiableApiNames = Lists.newArrayList("FeatureObj", "FeatureDimensionObj"
            , "ObjectFeatureObj", "ParseRuleObj", "ScoringRuleObj"
            , "MethodologyObj", "MethodologyRuleObj", "ObjectWorkflowObj", "WorkflowNodeObj", "WorkflowTaskObj");

    static {
        //读取json文件初始化数据
//        ClassLoader classLoader = FeatureBaseDataUtils.class.getClassLoader();
        jsonDataMap = new HashMap<>();
//        for (String apiName : vaiableApiNames) {
//            try {
//                String jsonData = IOUtils.toString(classLoader.getResource(
//                        "bizfeaturedata/init_crm_" + apiName + "_base_data.json"), "UTF-8");
//                List<Map<String, Object>> jsonDataModel = JSON.parseObject(jsonData, List.class);
//                jsonDataMap.put(apiName, jsonDataModel);
//            } catch (IOException e) {
//                log.error("FeatureBaseDataUtils loadjson" + apiName + "_base_data.json read error", e);
//            }
//        }
    }

    public static void handFeatureBaseData(String objectDescribeApiName, List<IObjectData> objectDataList) {
        if (CollectionUtils.notEmpty(objectDataList) && !jsonDataMap.isEmpty()) {
            Map<String, Map<String, Object>> jsonData = jsonDataMap.getOrDefault(objectDescribeApiName, Lists.newArrayList())
                    .stream()
                    .filter(map -> map.containsKey("id") && map.get("id") != null) // 确保每个元素包含非空的 "id"
                    .collect(Collectors.toMap(
                            map -> (String) map.get("id"), // 使用 "id" 作为键
                            map -> map                     // 值是原始的 Map
                    ));
            for (IObjectData objectData : objectDataList) {
                String id = objectData.getId();
                Map<String, Object> map = jsonData.get(id);
                if (map != null) {
                    switch (objectDescribeApiName) {
                        case FeatureConstants.FEATURE:
                            objectData.set(FeatureConstants.DATA_SOURCE_OBJECT, map.get(FeatureConstants.DATA_SOURCE_OBJECT));
                            objectData.set(FeatureConstants.DATA_SOURCE_FIELD, map.get(FeatureConstants.DATA_SOURCE_FIELD));
                            objectData.set(FeatureConstants.DATA_SOURCE_TYPE, map.get(FeatureConstants.DATA_SOURCE_TYPE));
                            objectData.set(FeatureConstants.DATA_SOURCE_THIRD, map.get(FeatureConstants.DATA_SOURCE_THIRD));
                            objectData.set(FeatureConstants.TIMER_INFO, map.get(FeatureConstants.TIMER_INFO));
                            objectData.set(FeatureConstants.STATUS, map.get(FeatureConstants.STATUS));
                            objectData.set(FeatureConstants.UPDATE_TYPE, map.get(FeatureConstants.UPDATE_TYPE));
                            objectData.set(FeatureConstants.TRIGGER_TYPE, map.get(FeatureConstants.TRIGGER_TYPE));
                            objectData.set(FeatureConstants.RULE_ID, map.get(FeatureConstants.RULE_ID));
                            objectData.set(FeatureConstants.FEATURE_DIMENSION_1, map.get(FeatureConstants.FEATURE_DIMENSION_1));
                            objectData.set(FeatureConstants.FEATURE_DIMENSION_2, map.get(FeatureConstants.FEATURE_DIMENSION_2));
                            objectData.set(FeatureConstants.FEATURE_DIMENSION_3, map.get(FeatureConstants.FEATURE_DIMENSION_3));
                            break;
                        case FeatureConstants.FEATURE_DIMENSION:
                            objectData.set(FeatureDimensionConstants.PARENT_ID, map.get(FeatureDimensionConstants.PARENT_ID));
                            objectData.set(FeatureDimensionConstants.TREE_PATH, map.get(FeatureDimensionConstants.TREE_PATH));
                            break;
                        case FeatureConstants.OBJECT_FEATURE:
                            objectData.set(ObjectFeatureConstants.OBJECT_API_NAME, map.get(ObjectFeatureConstants.OBJECT_API_NAME));
                            objectData.set(ObjectFeatureConstants.FEATURE_ID, map.get(ObjectFeatureConstants.FEATURE_ID));
                            objectData.set(ObjectFeatureConstants.WEIGHT, map.get(ObjectFeatureConstants.WEIGHT));
                            objectData.set(ObjectFeatureConstants.MUST_DO, map.get(ObjectFeatureConstants.MUST_DO));
                            objectData.set(ObjectFeatureConstants.SCORING_RULE_ID, map.get(ObjectFeatureConstants.SCORING_RULE_ID));
                            objectData.set(ObjectFeatureConstants.STATUS, map.get(ObjectFeatureConstants.STATUS));
                            break;
                        case FeatureConstants.PARSE_RULE:
                            objectData.set(ParseRuleConstants.RULE_TYPE, map.get(ParseRuleConstants.RULE_TYPE));
                            objectData.set(ParseRuleConstants.RULE_CONTENT, map.get(ParseRuleConstants.RULE_CONTENT));
                            objectData.set(ParseRuleConstants.DATA_SOURCE_TYPE, map.get(ParseRuleConstants.DATA_SOURCE_TYPE));
                            objectData.set(ParseRuleConstants.RETURN_DATA_TYPE, map.get(ParseRuleConstants.RETURN_DATA_TYPE));
                            objectData.set(ParseRuleConstants.CALC_METHOD, map.get(ParseRuleConstants.CALC_METHOD));
                            break;
                        case FeatureConstants.SCORING_RULE:
                            objectData.set(ScoringRuleConstants.SCORING_TYPE, map.get(ScoringRuleConstants.SCORING_TYPE));
                            objectData.set(ScoringRuleConstants.RULE_CONTENT, map.get(ScoringRuleConstants.RULE_CONTENT));
                            objectData.set(ScoringRuleConstants.DEFAULT_VALUE, map.get(ScoringRuleConstants.DEFAULT_VALUE));
                            objectData.set(ScoringRuleConstants.CALC_METHOD, map.get(ScoringRuleConstants.CALC_METHOD));
                            break;
                        case FeatureConstants.METHODOLOGY:
                            objectData.set(MethodologyConstants.PRIORITY, map.get(MethodologyConstants.PRIORITY));
                            break;
                        case FeatureConstants.METHODOLOGY_RULE:
                            objectData.set(MethodologyRuleConstants.METHODOLOGY_ID, map.get(MethodologyRuleConstants.METHODOLOGY_ID));
                            objectData.set(MethodologyRuleConstants.OBJECT_API_NAME, map.get(MethodologyRuleConstants.OBJECT_API_NAME));
                            objectData.set(MethodologyRuleConstants.RULE_CONTENT, map.get(MethodologyRuleConstants.RULE_CONTENT));
                            break;
                        case FeatureConstants.OBJECT_METHODOLOGY:
                            objectData.set(ObjectMethodologyConstants.OBJECT_API_NAME, map.get(ObjectMethodologyConstants.OBJECT_API_NAME));
                            objectData.set(ObjectMethodologyConstants.OBJECT_FIELD_NAME, map.get(ObjectMethodologyConstants.OBJECT_FIELD_NAME));
                            objectData.set(ObjectMethodologyConstants.OBJECT_FIELD_VALUE, map.get(ObjectMethodologyConstants.OBJECT_FIELD_VALUE));
                            objectData.set(ObjectMethodologyConstants.NODE_ID, map.get(ObjectMethodologyConstants.NODE_ID));
                            break;
                        case FeatureConstants.METHODOLOGY_NODE:
                            objectData.set(MethodologyNodeConstants.METHODOLOGY_ID, map.get(MethodologyNodeConstants.METHODOLOGY_ID));
                            objectData.set(MethodologyNodeConstants.OBJECT_API_NAME, map.get(MethodologyNodeConstants.OBJECT_API_NAME));
                            objectData.set(MethodologyNodeConstants.SUMMARY, map.get(MethodologyNodeConstants.SUMMARY));
                            objectData.set(MethodologyNodeConstants.IS_ACTION, map.get(MethodologyNodeConstants.IS_ACTION));
                            objectData.set(MethodologyNodeConstants.COMPLETION_RULE, map.get(MethodologyNodeConstants.COMPLETION_RULE));
                            objectData.set(MethodologyNodeConstants.STATUS, map.get(MethodologyNodeConstants.STATUS));
                            objectData.set(MethodologyNodeConstants.ROOT_ID, map.get(MethodologyNodeConstants.ROOT_ID));
                            objectData.set(MethodologyNodeConstants.PARENT_ID, map.get(MethodologyNodeConstants.PARENT_ID));
                            objectData.set(MethodologyNodeConstants.TREE_PATH, map.get(MethodologyNodeConstants.TREE_PATH));
                            break;
                        case FeatureConstants.METHODOLOGY_TASK:
                            objectData.set(MethodologyTaskConstants.NOTE, map.get(MethodologyTaskConstants.NOTE));
                            objectData.set(MethodologyTaskConstants.NODE_ID, map.get(MethodologyTaskConstants.NODE_ID));
                            objectData.set(MethodologyTaskConstants.MUST_DO, map.get(MethodologyTaskConstants.MUST_DO));
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    private static final Random random = new Random();
    public static String generateName() {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        LocalDateTime localDateTime = LocalDateTime.now();
        String prefix = localDateTime.format(dateTimeFormatter);
        return prefix + String.format("%04d", random.nextInt(10000));
    }
}
