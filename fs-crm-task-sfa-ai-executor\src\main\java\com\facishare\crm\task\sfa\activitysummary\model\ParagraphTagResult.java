package com.facishare.crm.task.sfa.activitysummary.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParagraphTagResult {
    
    /**
     * 标签结果列表
     */
    @JSONField(name = "tagResults")
    private List<TagResult> tagResults;
    
    /**
     * 内部标签结果类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TagResult {
        /**
         * 段落ID
         */
        @JSONField(name = "paragraphId")
        private String paragraphId;
        
        /**
         * 标签ID列表
         */
        @JSONField(name = "tagId")
        private List<String> tagId;

        /**
         * 打此标签的理由
         */
        @JSONField(name = "reason")
        private String reason;

        /**
         * 没有打标原因
         */
        @JSONField(name = "noReason")
        private String noReason;
    }
}
