package com.facishare.crm.task.sfa.correct;

import com.facishare.crm.task.sfa.correct.dict.Confusion;
import com.facishare.crm.task.sfa.correct.dict.Stroke;
import com.facishare.crm.task.sfa.correct.util.Util;
import com.github.promeg.pinyinhelper.Pinyin;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.corpus.tag.Nature;
import com.hankcs.hanlp.seg.common.Term;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ProperCorrector {
    private static final String PUNCTUATION = "[，。！、？；：,?!.:;]";
    private final String[][] pinyinConfStartsWith =
            {
                    {"h", "f"},
                    {"r", "l"},
                    {"k", "g"},
                    {"l", "n"},
                    {"zh", "z"},
                    {"ch", "c"},
                    {"sh", "s"},
            };
    private final String[][] pinyinConfEndsWith =
            {
                    {"ang", "an"},
                    {"eng", "en"},
                    {"ing", "in"},
                    {"iang", "ian"},
                    {"uang", "uan"},
            };
    private final Stroke stroke;
    private final Confusion confusion;

    public ProperCorrector(Stroke stroke, Confusion confusion) {
        this.stroke = stroke;
        this.confusion = confusion;
    }

    String getPinyin(char c) {
        return Pinyin.toPinyin(c);
    }

    String getStroke(char chr1) {
        return stroke.query(chr1);
    }

    float getCharStrokeSimilarityScore(char chr1, char chr2) {
        if (chr1 == chr2) {
            return 1F;
        }
        String stroke1;
        String stroke2;
        if ((stroke1 = getStroke(chr1)) == null || (stroke2 = getStroke(chr2)) == null) {
            return 0F;
        }
        return 1 - getLevenshteinDistanceScore(stroke1, stroke2);
    }

    float getWordStrokeSimilarityScore(String word1, String word2) {
        float totalScore = 0F;
        for (int i = 0; i < word1.length(); i++) {
            char chr1 = word1.charAt(i);
            char chr2 = word2.charAt(i);
            float chrSimScore = getCharStrokeSimilarityScore(chr1, chr2);
            if (chrSimScore <= 0.8) {
                return 0;
            }
            totalScore += chrSimScore;
        }
        return totalScore / word1.length();
    }

    boolean isNearPinyin(String p1, String p2) {
        if (p1.equals(p2)) {
            return true;
        }
        for (String[] startsWith : pinyinConfStartsWith) {
            if (p1.startsWith(startsWith[0]) && p2.startsWith(startsWith[1])) {
                return true;
            }
            if (p1.startsWith(startsWith[1]) && p2.startsWith(startsWith[0])) {
                return true;
            }
        }
        for (String[] endsWith : pinyinConfEndsWith) {
            if (p1.endsWith(endsWith[0]) && p2.endsWith(endsWith[1])) {
                return true;
            }
            if (p1.endsWith(endsWith[1]) && p2.endsWith(endsWith[0])) {
                return true;
            }
        }
        return false;
    }

    float getPinyinSimilarityScore(String pinyin1, String pinyin2) {
        return 1 - LevenshteinDistance.getDefaultInstance().apply(pinyin1, pinyin2) / (float) Math.max(pinyin1.length(), pinyin2.length());
    }

    float getWordPinyinSimilarityScore(String word1, String word2) {
        float totalScore = 0F;
        for (int i = 0; i < word1.length(); i++) {
            char cha1 = word1.charAt(i);
            char cha2 = word2.charAt(i);
            String pinyin1 = getPinyin(cha1);
            String pinyin2 = getPinyin(cha2);
            if (!isNearPinyin(pinyin1, pinyin2)) {
                return 0F;
            }
            float score = cha1 == cha2 ? 1F : getPinyinSimilarityScore(pinyin1, pinyin2);
            totalScore += score;
        }
        return totalScore / word1.length();
    }

    boolean checkChineseWordSimilarityScore(String word1, String word2, float threshold) {
        if (word1.length() != word2.length()) {
            return false;
        }
        if (StringUtils.equals(word1, word2)) {
            return true;
        }
        return getWordPinyinSimilarityScore(word1, word2) >= threshold || getWordStrokeSimilarityScore(word1, word2) >= threshold;
    }

    double getEnglishWordSimilarityScore(String word1, String word2) {
        return 1 - getLevenshteinDistanceScore(word1, word2);
    }

    float getLevenshteinDistanceScore(String word1, String word2) {
        if (StringUtils.equals(word1, word2)) {
            return 1F;
        } else {
            float max = Math.max(word1.length(), word2.length());
            return max == 0 ? 0F : LevenshteinDistance.getDefaultInstance().apply(word1, word2) / max;
        }
    }

    public List<String> batchCorrect(List<String> textList, List<ProperWord> props, ConfusionWords confusions) {
        return batchCorrect(textList, new ProperWords(props), confusions);
    }

    public List<String> batchCorrect(List<String> textList, ProperWords properWords, ConfusionWords confusionWords) {
        List<String> res = new ArrayList<>();
        for (String text : textList) {
            res.add(correct(text, properWords, confusionWords));
        }
        return res;
    }

    /**
     * @param text        原始文本
     * @param properWords 专词
     * @return 替换文本
     */
    public String correct(String text, ProperWords properWords, ConfusionWords confusions) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        List<String> sentences = splitSentence(text);
        int sentIdx = 0;
        confusions.merge(this.confusion.getSent());
        List<Mistake> mistakes = new ArrayList<>();
        boolean sortMistakes = false;
        for (String sent : sentences) {
            int sentLength = sent.length();
            if (Util.isSubMatch(PUNCTUATION, sent)) {
                sent = sent.substring(0, sent.length() - 1);
            }
            List<Term> terms = HanLP.segment(sent);
            List<Term> nxTerms = terms.stream().filter(t -> Nature.nx.equals(t.nature)).collect(Collectors.toList());
            if (!properWords.chinese4le.isEmpty()) {
                Set<String> ngrams = nGrams4le(terms);
                findMistake(ngrams, properWords.chinese4le, sent, sentIdx, mistakes);
            }
            if (!properWords.chinese5.isEmpty()) {
                Set<String> ngrams = nGrams(5, sent);
                findMistake(ngrams, properWords.chinese5, sent, sentIdx, mistakes);
            }
            if (!properWords.chinese6.isEmpty()) {
                Set<String> ngrams = nGrams(6, sent);
                findMistake(ngrams, properWords.chinese6, sent, sentIdx, mistakes);
            }
            if (!properWords.chinese7.isEmpty()) {
                Set<String> ngrams = nGrams(7, sent);
                findMistake(ngrams, properWords.chinese7, sent, sentIdx, mistakes);
            }
            if (!nxTerms.isEmpty()) {
                correctEngInSent(properWords.english, confusions, sent, nxTerms, sentIdx, mistakes);
            }
            sortMistakes |= confusions.hitSent(sentIdx, sent, mistakes);
            sentIdx += sentLength;
        }
        if (mistakes.isEmpty()) {
            return text;
        }
        StringBuilder result = new StringBuilder(text.length());
        for (String sent : sentences) {
            result.append(sent);
        }
        int offset = 0;
        if (sortMistakes) {
            mistakes.sort(Comparator.comparingInt(m -> m.start));
        }
        // 注意mistakes的顺序
        for (Mistake mistake : mistakes) {
            int start = mistake.start + offset;
            int end = mistake.end + offset;
            log.info("[{}] {} => {}->{}", mistake.type, mistake.source, result.substring(start, end), mistake.correct);
            result.replace(start, end, mistake.correct);
            offset += mistake.diff;
        }
        return result.toString();
    }

    private void findMistake(Collection<String> ngrams, Collection<ProperWord> props, String sent, Integer sentIdx, List<Mistake> mistakes) {
        for (String item : ngrams) {
            for (ProperWord prop : props) {
                if (checkChineseWordSimilarityScore(item, prop.text, prop.sim)) {
                    if (!StringUtils.equals(item, prop.text)) {
                        int curIdx = sent.indexOf(item);
                        mistakes.add(new Mistake(sentIdx + curIdx, sentIdx + curIdx + item.length(), sent, prop.text, Type.Proper));
                        break;
                    }
                }
            }
        }
    }

    private void correctEngInSent(List<ProperWord> englishProps, ConfusionWords confusionWords, String sent, List<Term> terms, int sentIdx, List<Mistake> mistakes) {
        List<Hit> hits = extractEnglishWords(sent, terms);
        nextLocation:
        for (Hit hit : hits) {
            if (!confusionWords.hitWord(sentIdx + hit.start, sent, hit.word, mistakes)) {
                for (ProperWord prop : englishProps) {
                    if (getEnglishWordSimilarityScore(hit.word, prop.text) >= prop.sim) {
                        mistakes.add(new Mistake(sentIdx + hit.start, sentIdx + hit.start + hit.word.length(), sent, prop.text, Type.Proper));
                        continue nextLocation;
                    }
                }
            }
        }
    }

    static List<String> splitSentence(String text) {
        List<String> sentences = new ArrayList<>();
        Pattern p = Pattern.compile(PUNCTUATION);
        Matcher m = p.matcher(text);
        String[] sent = p.split(text);
        int sentLen = sent.length;
        if (sentLen > 0) {
            int count = 0;
            while (count < sentLen) {
                if (m.find()) {
                    sent[count] += m.group();
                }
                count++;
            }
        }
        for (String sentence : sent) {
            sentence = sentence.replaceAll("(&rdquo;|&ldquo;|&mdash;|&lsquo;|&rsquo;|&middot;|&quot;|&darr;|&bull;)", "");
            sentences.add(sentence);
        }
        return sentences;
    }

    static List<Hit> extractEnglishWords(String text, List<Term> terms) {
        List<Hit> res = new ArrayList<>();
        if (Util.isEnglish(text)) {
            res.add(new Hit(text, 0, text.length()));
        }
        int offset = 0;
        for (Term term : terms) {
            if (term.word.equals(text)) {
                continue;
            }
            int index = text.indexOf(term.word, offset);
            if (index >= 0) {
                res.add(new Hit(term.word, index, term.word.length()));
                offset = index;
            }
        }
        return res;
    }

    static Set<String> nGrams4le(List<Term> terms) {
        Set<String> ngrams = new HashSet<>();
        for (Term term : terms) {
            if (term.word.length() > 1) {
                ngrams.add(term.word);
            }
        }
        // 2-term
        for (int i = 0; i < terms.size() - 1; i++) {
            ngrams.add(terms.get(i).word + terms.get(i + 1).word);
        }
        // 3-term
        for (int i = 0; i < terms.size() - 2; i++) {
            ngrams.add(terms.get(i).word + terms.get(i + 1).word + terms.get(i + 2).word);
        }
        // 4-term
        for (int i = 0; i < terms.size() - 3; i++) {
            ngrams.add(terms.get(i).word + terms.get(i + 1).word + terms.get(i + 2).word + terms.get(i + 3).word);
        }
        return ngrams;
    }

    static Set<String> nGrams(int n, String str) {
        Set<String> ngrams = new HashSet<>();
        for (int i = 0; i < str.length() - n + 1; i++) {
            ngrams.add(str.substring(i, i + n));
        }
        return ngrams;
    }

    static class Hit {
        String word;
        int start;
        int end;

        Hit(String word, int start, int end) {
            this.word = word;
            this.start = start;
            this.end = end;
        }
    }
}