package com.facishare.crm.task.sfa.activitysummary.enums;

import lombok.Getter;

public enum EmotionalTendencyEnum {

    /**
     * 【满意】【中立】【不满】
     *
     * satisfaction，neutrality，dissatisfied
     */

    SATISFACTION("满意", "satisfaction"),
    NEUTRALITY("中立", "neutrality"),
    DISSATISFIED("不满", "dissatisfied");

    @Getter
    private final String desc;
    @Getter
    private final String type;

    EmotionalTendencyEnum(String desc, String type) {
        this.desc = desc;
        this.type = type;
    }

    public static String ofType(String desc) {
        for (EmotionalTendencyEnum emotionalTendencyEnum : EmotionalTendencyEnum.values()) {
            if (emotionalTendencyEnum.getDesc().equals(desc)) {
                return emotionalTendencyEnum.getType();
            }
        }
        throw new IllegalArgumentException("EmotionalTendencyEnum not found");
    }

}
