package com.facishare.crm.web;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureValueConstants;
import com.facishare.crm.task.sfa.bizfeature.service.ScoringRuleCalcService;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("scoringRule")
public class ScoringRuleCalcController {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ScoringRuleCalcService scoringRuleCalcService;

    @PostMapping("featureScoreCalc")
    public String featureScoreCalc(@RequestBody ScoringRuleParam param) {
        if (StringUtils.isEmpty(param.getTenantId())) {
            return "error";
        }
        boolean hasFeatureValueId = CollectionUtils.isNotEmpty(param.getFeatureValueIds());
        boolean hasObjectParams = StringUtils.isNotEmpty(param.getObjectApiName()) && StringUtils.isNotEmpty(param.getObjectId());
        if (!hasFeatureValueId && !hasObjectParams) {
            return "error";
        }
        SearchTemplateQueryPlus featureValueQuery = SearchUtil.buildBaseSearchQuery();
        if (hasFeatureValueId) {
            SearchTemplateQueryExt.of(featureValueQuery).addFilter(Operator.IN, DBRecord.ID, param.getFeatureValueIds());
        } else {
            SearchTemplateQueryExt.of(featureValueQuery).addFilter(Operator.EQ, FeatureValueConstants.OBJECT_ID, param.getObjectId());
            SearchTemplateQueryExt.of(featureValueQuery).addFilter(Operator.EQ, FeatureValueConstants.OBJECT_API_NAME, param.getObjectApiName());
        }
        List<IObjectData> featureValueList = serviceFacade
                .findBySearchQueryIgnoreAll(User.systemUser(param.getTenantId()), FeatureConstants.FEATURE_VALUE, featureValueQuery).getData();
        scoringRuleCalcService.featureValueScoreCalc(param.getTenantId(), featureValueList);
        return "success";
    }

    @Data
    public static class ScoringRuleParam {
        List<String> featureValueIds;
        String tenantId;
        String objectApiName;
        String objectId;
    }
}
