<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息 -->
    <property name="defaultPattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                }%n"
    />

    <appender name="ROCKET_MQ_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/rocketmq.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="CRMLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/crm.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/crm.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="OSS_Trace" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/trace.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="PerfLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/perf.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <!--trace log-->
    <appender name="ASYNC_OSS_Trace" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="OSS_Trace"/>
    </appender>
    <logger name="com.github.trace" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_OSS_Trace"/>
    </logger>

    <!--perf log-->
    <appender name="ASYNC_PerfLog" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="PerfLog"/>
    </appender>
    <logger name="com.facishare.paas.appframework.common.util.StopWatch" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_PerfLog"/>
    </logger>

    <!-- rocketmq -->
    <appender name="ASYNC_ROCKET_MQ_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="ROCKET_MQ_APPENDER"/>
    </appender>
    <logger name="RocketmqClient" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_ROCKET_MQ_APPENDER"/>
    </logger>
    <logger name="RocketmqRemoting" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_ROCKET_MQ_APPENDER"/>
    </logger>
    
    <appender name="ASYNC_CRMLog" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="CRMLog"/>
    </appender>

    <!-- 元数据info日志太多 过滤掉-->
    <logger name="com.facishare.paas.metadata" level="WARN" additivity="false">
        <queueSize>512</queueSize>
        <appender-ref ref="CRMLog"/>
    </logger>

    <!-- 元数据info日志太多 过滤掉-->
    <logger name="com.facishare.paas.expression" level="WARN" additivity="false">
        <queueSize>512</queueSize>
        <appender-ref ref="CRMLog"/>
    </logger>

    <!-- 元数据info日志太多 过滤掉-->
    <logger name="com.facishare.paas.appframework" level="WARN" additivity="false">
        <queueSize>512</queueSize>
        <appender-ref ref="CRMLog"/>
    </logger>

    <!-- 组织架构 redis info日志太多 过滤掉-->
    <logger name="com.facishare.organization.api" level="WARN" additivity="false">
        <queueSize>512</queueSize>
        <appender-ref ref="CRMLog"/>
    </logger>

    <!-- 打印 config 就离谱，干掉了-->
    <logger name="com.facishare.paas.appframework.metadata.config" level="ERROR" additivity="false">
        <queueSize>512</queueSize>
        <appender-ref ref="CRMLog"/>
    </logger>
    <root level="INFO">
        <appender-ref ref="ASYNC_CRMLog"/>
    </root>
    <conversionRule conversionWord="msg" converterClass="com.fxiaoke.metrics.logback.MaskMessageConverter"/>
</configuration>
