{"fields": {"name": {"is_index": true, "is_active": true, "is_unique": true, "default_value": "", "label": "标注名称", "type": "text", "is_required": true, "api_name": "name", "define_type": "package", "max_length": 100, "status": "released", "is_extend": false}, "active_status": {"is_index": true, "is_active": true, "description": "启用状态", "is_unique": false, "default_value": "enable", "label": "启用状态", "type": "select_one", "is_need_convert": false, "is_required": true, "api_name": "active_status", "options": [{"label": "启用", "value": "enable"}, {"label": "禁用", "value": "disable"}], "define_type": "package", "status": "released", "is_extend": false}, "related_object_api_name": {"is_index": true, "is_active": true, "description": "关联对象", "is_unique": false, "label": "关联对象", "type": "select_many", "is_need_convert": false, "is_required": true, "api_name": "related_object_api_name", "options": [{"label": "线索", "value": "LeadsObj"}, {"label": "客户", "value": "AccountObj"}, {"label": "商机", "value": "NewOpportunityObj"}], "define_type": "package", "status": "released", "is_extend": false}, "follower": {"is_index": true, "is_active": true, "description": "关注方", "is_unique": false, "label": "关注方", "type": "select_one", "is_need_convert": false, "is_required": true, "api_name": "follower", "options": [{"label": "我方", "value": "our_side"}, {"label": "客方", "value": "your_side"}, {"label": "双方", "value": "both_parties"}], "define_type": "package", "status": "released", "is_extend": false}, "positive_criteria": {"is_index": true, "is_active": true, "description": "正向判断标准", "is_unique": false, "label": "正向判断标准", "type": "long_text", "max_length": 2000, "is_required": true, "api_name": "positive_criteria", "define_type": "package", "status": "released", "is_extend": false}, "positive_sample": {"is_index": true, "is_active": true, "description": "正向样例", "is_unique": false, "label": "正向样例", "type": "long_text", "max_length": 2000, "is_need_convert": false, "is_required": false, "api_name": "positive_sample", "define_type": "package", "status": "released", "is_extend": false}, "negative_criteria": {"is_index": true, "is_active": true, "description": "负向判断标准", "is_unique": false, "label": "负向判断标准", "type": "long_text", "max_length": 2000, "is_need_convert": false, "is_required": true, "api_name": "negative_criteria", "define_type": "package", "status": "released", "is_extend": false}, "negative_sample": {"is_index": true, "is_active": true, "description": "负向样例", "is_unique": false, "label": "负向样例", "type": "long_text", "max_length": 2000, "is_need_convert": false, "is_required": false, "api_name": "negative_sample", "define_type": "package", "status": "released", "is_extend": false}, "focus_on_precision": {"is_index": true, "is_active": true, "description": "是否更关注精准度", "is_unique": false, "default_value": false, "label": "是否更关注精准度", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "focus_on_precision", "define_type": "package", "status": "released", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "is_extend": false}, "system_type": {"is_index": true, "is_active": true, "description": "数据类型", "is_unique": false, "default_value": "udef", "label": "数据类型", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "system_type", "options": [{"label": "系统", "value": "system"}, {"label": "自定义", "value": "udef"}], "define_type": "package", "status": "released", "is_extend": false}, "owner": {"is_index": true, "is_active": true, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "status": "released", "is_extend": false}, "lock_status": {"is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "status": "released", "is_extend": false}, "lock_rule": {"is_index": true, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_extend": false}, "lock_user": {"is_index": true, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "is_extend": false}, "life_status": {"is_index": true, "is_active": true, "description": "生命状态", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "status": "released", "is_extend": false}, "life_status_before_invalid": {"is_index": true, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "max_length": 256, "is_extend": false}, "owner_department": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "负责人所在部门", "type": "text", "default_to_zero": false, "is_need_convert": false, "is_required": false, "api_name": "owner_department", "define_type": "package", "is_single": true, "max_length": 100, "status": "released", "is_extend": false}, "relevant_team": {"embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one"}}, "is_index": true, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_extend": false}, "record_type": {"is_index": false, "is_active": true, "description": "业务类型", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "status": "released", "is_extend": false}, "created_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "创建人", "api_name": "created_by", "description": "创建人", "status": "released", "is_extend": false}, "last_modified_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "最后修改人", "api_name": "last_modified_by", "description": "最后修改人", "status": "released", "is_extend": false}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "api_name": "package", "description": "package", "status": "released", "is_extend": false}, "object_describe_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_id", "api_name": "object_describe_id", "description": "object_describe_id", "status": "released", "is_extend": false}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "is_extend": false}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released", "is_extend": false}, "create_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "创建时间", "status": "released", "is_extend": false}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "最后修改时间", "status": "released", "is_extend": false}, "extend_obj_data_id": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "default_value": "", "type": "text", "label": "extend_obj_data_id", "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "max_length": 100, "status": "released", "is_extend": false}}, "index_version": 1, "api_name": "FeatureTagRuleObj", "display_name": "标注规则", "package": "CRM", "define_type": "package", "is_active": true, "store_table_name": "biz_feature_tag_rule", "is_deleted": false}