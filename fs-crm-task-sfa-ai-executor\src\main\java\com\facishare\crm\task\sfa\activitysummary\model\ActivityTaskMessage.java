package com.facishare.crm.task.sfa.activitysummary.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/10 15:48
 * @description:
 */
public interface ActivityTaskMessage {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Rec2TextTask{
        private String tenantId;
        private String objectId;
        private String objectApiName;
        private String interactiveTypes;
        private String sourceId;
        private String opId;
        private String actionCode;
        private String stage;
        
        private String taskId;
        private String ossPath;
        private Long createTime;
        private String language;
    }
    
}

