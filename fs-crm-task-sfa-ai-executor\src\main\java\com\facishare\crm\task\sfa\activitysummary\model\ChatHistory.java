package com.facishare.crm.task.sfa.activitysummary.model;

import com.facishare.ai.api.model.Message;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
@Data
public class ChatHistory {
    private final List<Message> messages = new ArrayList<>();

    public void addMessage(String role, String content) {
        Message message = new Message();
        message.setRole(role);
        message.setContent(content);
        messages.add(message);
    }

    public void addSystemMessage(String content) {
        addMessage("system", content);
    }

    public void addUserMessage(String content) {
        addMessage("user", content);
    }

    public void addAssistantMessage(String content) {
        addMessage("assistant", content);
    }

    public String getLastMessage() {
        if (messages.isEmpty()) {
            return null;
        }
        return messages.get(messages.size() - 1).getContent();
    }

    public String getLastMessageByRole(String role) {
        for (int i = messages.size() - 1; i >= 0; i--) {
            Message message = messages.get(i);
            if (role.equals(message.getRole())) {
                return message.getContent();
            }
        }
        return null;
    }

    public String getLastAssistantMessage() {
        return getLastMessageByRole("assistant");
    }

    public String getLastUserMessage() {
        return getLastMessageByRole("user");
    }

    public String getLastSystemMessage() {
        return getLastMessageByRole("system");
    }

    public boolean isEmpty() {
        return messages.isEmpty();
    }

    public int size() {
        return messages.size();
    }

    public void clear() {
        messages.clear();
    }

    public List<Message> getMessagesByRole(String role) {
        List<Message> result = new ArrayList<>();
        for (Message message : messages) {
            if (role.equals(message.getRole())) {
                result.add(message);
            }
        }
        return result;
    }
}
