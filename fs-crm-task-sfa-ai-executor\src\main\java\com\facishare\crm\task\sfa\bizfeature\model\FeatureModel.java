package com.facishare.crm.task.sfa.bizfeature.model;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface FeatureModel {
    @Data
    class FeatureData {
        private String returnDataType;
        private String valueText;
        private Boolean valueBoolean;
        private Double valueNumber;
        private String masterId;
        private String masterApiName;
        private Map<String, Object> triggerValue;

    }

    @Data
    class ParseLLMRule {
        private String promptApiName;
        /**
         * 入参参数
         */
        private String fieldName;
        /**
         * 对比字段入参参数
         */
        private String comparison;

    }

    @Data
    class ParseBaseRule {
        private String rule;
    }

    @Data
    class ParseTagData {
        private String id;
        private String tag;
    }

    @Data
    class ParseValueData {
        private Object value;
        private Map<String, Object> triggerValue;
        private String type;
        private String objectApiName;
        private String objectId;
    }

    @Data
    class ParseExtData {
        private Map<String, List<FeatureModel.ParseTagData>> tagMap;
        private String objectApiName;
        private Map<String, List<IObjectData>> objectDataList;
        private String type;
        private String objectId;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    class ParseExtFilter {
        private boolean hasErr;
        private List<IFilter> filters;
    }

    @Data
    class AggCondition {
        @JSONField(name = "agg_object")
        @JsonProperty("agg_object")
        private String aggObject;

        @JSONField(name = "agg_way")
        @JsonProperty("agg_way")
        private String aggWay;

        @JSONField(name = "agg_type")
        @JsonProperty("agg_type")
        private String aggType;

        @JSONField(name = "dimension")
        @JsonProperty("dimension")
        private String dimension;

        @JSONField(name = "agg_field")
        @JsonProperty("agg_field")
        private String aggField;

        @JSONField(name = "date_field_filter")
        @JsonProperty("date_field_filter")
        private String dateFieldFilter;

        @JSONField(name = "date_range")
        @JsonProperty("date_range")
        private String dateRange;

        @JSONField(name = "condition")
        @JsonProperty("condition")
        private List<RuleWhere> condition;

        @JSONField(name = "sum_condition")
        @JsonProperty("sum_condition")
        private List<RuleWhere> sumCondition;
    }

    enum AggType {
        /**
         * 当前activity
         */
        CURRENT("current"),
        /**
         * 历史
         */
        HISTORY("history");

        private final String value;

        public String getValue() {
            return value;
        }

        AggType(String value) {
            this.value = value;
        }
    }

    enum AggWay {
        /**
         * 汇总
         */
        SUM("sum"),
        /**
         * 总数
         */
        COUNT("count"),
        /**
         * 总数比例
         */
        COUNT_RATE("count_rate"),
        /**
         * 汇总比例
         */
        SUM_RATE("sum_rate");

        private final String value;

        public String getValue() {
            return value;
        }

        AggWay(String value) {
            this.value = value;
        }
    }

    enum DateRange {
        /**
         * 一周
         */
        WEEK("week"),
        /**
         * 双周
         */
        TWO_WEEK("two_week"),
        /**
         * 一月
         */
        MONTH("month"),
        /**
         * 一个季度
         */
        QUARTER("quarter"),
        /**
         * 一年
         */
        YEAR("year"),
        /**
         * 最近5次activity
         */
        LIMIT5("limit5"),
        /**
         * 最近10次activity
         */
        LIMIT10("limit10");

        private final String value;

        public String getValue() {
            return value;
        }

        DateRange(String value) {
            this.value = value;
        }
    }

    enum Aggdimension {
        /**
         * 客户
         */
        ACCOUNT_ID("account_id"),
        /**
         * 商机
         */
        NEW_OPPORTUNITY_ID("new_opportunity_id"),
        /**
         * 线索
         */
        LEADS_ID("leads_id");

        private final String value;

        public String getValue() {
            return value;
        }

        Aggdimension(String value) {
            this.value = value;
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public class InitHistoryData {

        private String tenantId;

        private String objectApiName;

        private List<String> objectIds;

    }
}
