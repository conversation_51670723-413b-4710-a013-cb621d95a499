package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.task.sfa.rest.dto.KnowledgeModel;
import com.facishare.crm.task.sfa.rest.dto.RoleModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

/**
 *PAAS角色
 */
@RestResource(value = "KNOWLEDGE-SPACE", desc = "知识库空间服务", contentType = "application/json")
public interface KnowledgeSpaceProxy {
    @POST(value = "/com.facishare.eservice.cases.api.service.knowledge.KnowledgeSpaceRestService/createKnowledgeSpace/1.0", desc = "增加知识库分类")
    KnowledgeModel.CreateKnowledgeSpaceResult createKnowledgeSpace(@Body KnowledgeModel.CreateKnowledgeSpaceArg arg, @HeaderMap Map<String, String> header);
}
