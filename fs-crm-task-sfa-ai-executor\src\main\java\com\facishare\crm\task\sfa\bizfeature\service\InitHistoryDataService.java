package com.facishare.crm.task.sfa.bizfeature.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.activitysummary.service.strategy.RefreshDataTagService;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.MethodologyInstanceConstants;
import com.facishare.crm.task.sfa.bizfeature.model.ProfileScoreModel;
import com.facishare.crm.task.sfa.model.ObjectData;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;

import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureMqModel;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;

import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InitHistoryDataService {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private MethodologyService methodologyService;
    @Resource
    private FeatureEngineService featureEngineService;
    @Resource
    private RealTimeProfileScoreService realTimeProfileScoreService;

    @Resource
    private RefreshDataTagService refreshDataTagService;

    public void initHistoryData(FeatureModel.InitHistoryData initData) {
        log.info("initHistoryData, initData:{}", initData);
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsIgnoreAll(initData.getTenantId(),
                initData.getObjectIds(),
                initData.getObjectApiName());
        User user = User.systemUser(initData.getTenantId());
        for (IObjectData data : dataList) {
            IObjectData instance = getInstance(initData, data, user);
            if (null != instance) {
                continue;
            }
            try {
                ObjectData.ObjectChange.Context context = new ObjectData.ObjectChange.Context();
                context.setTenantId(initData.getTenantId());
                ObjectData.ObjectChange message = makeMessageData(data, context);

                // 生成方法论实例相关数据
                methodologyService.consumer(message);

                // 生成本对象特征
                featureEngineService.generate(message);
                if (FeatureConstants.NEW_OPPORTUNITY_OBJ.equals(initData.getObjectApiName())) {
                    // 生成客户特征
                    featureEngineService.generate(message);

                    // 生成商机相关对象特征
                    String[][] featureObjInfos = new String[][]{
                            {"NewOpportunityContactsObj", "new_opportunity_id"},
                            {"CompetitiveLinesObj", "new_opportunity_id"}, {"SalesOrderObj", "new_opportunity_id"},
                            {"QuoteObj", "new_opportunity_id"},
                            {"NewOpportunityContactRelationshipObj", "opportunity_id"}
                    };
                    for (String[] objInfo : featureObjInfos) {
                        try {
                            IObjectData dataObjectData = findDataByFeatureData(user, objInfo[0], objInfo[1],
                                    Lists.newArrayList(data.getId()), null);
                            if (dataObjectData != null) {
                                ObjectData.ObjectChange messageData = makeMessageData(dataObjectData, context);
                                featureEngineService.generate(messageData);
                            }
                        } catch (Exception e) {
                            log.error("generate object feature error, apiName:{} id{}", objInfo[0], objInfo[1], e);
                        }
                    }
                }
                // 生成互动特征
                generateActivityFeature(initData, data, user, context);

                // 生成画像信息
                ProfileScoreModel profileScoreModel = new ProfileScoreModel();
                profileScoreModel.setTenantId(initData.getTenantId());
                profileScoreModel.setObjectApiName(initData.getObjectApiName());
                profileScoreModel.setObjectId(data.getId());
                try {
                    realTimeProfileScoreService.scoreCalc(profileScoreModel);
                } catch (Exception e) {
                    log.error("realtime profile score error, profileScoreModel:{}", profileScoreModel, e);
                }

            } catch (Exception e) {
                log.error("initHistoryData error, Id:{}, apiName:{}", data.getId(), data.getDescribeApiName());
            }
            log.warn("initHistoryData success, Id:{}, apiName:{}", data.getId(), data.getDescribeApiName());
        }

    }

    private IObjectData getInstance(FeatureModel.InitHistoryData initData, IObjectData data, User user) {
        String field = MethodologyInstanceConstants.LEAD_ID;
        if (FeatureConstants.NEW_OPPORTUNITY_OBJ.equals(initData.getObjectApiName())) {
            field = MethodologyInstanceConstants.OPPORTUNITY_ID;
        } else if (FeatureConstants.ACCOUNT_OBJ.equals(initData.getObjectApiName())) {
            field = MethodologyInstanceConstants.ACCOUNT_ID;
        }
        IFilter filter = SearchUtil.filter(MethodologyInstanceConstants.STATUS, Operator.EQ, "1");
        return findDataByFeatureData(user, FeatureConstants.METHODOLOGY_INSTANCE, field,
                Lists.newArrayList(data.getId()), Lists.newArrayList( filter));
    }

    private void generateActivityFeature(FeatureModel.InitHistoryData initData, IObjectData data, User user,
            ObjectData.ObjectChange.Context context) {
        List<IObjectData> salesRecords = refreshDataTagService.querySalesRecordsByObjectApiName(user, data.getId(),
                initData.getObjectApiName());
        if (CollectionUtils.notEmpty(salesRecords)) {
            // 生成互动特征
            ObjectData.ObjectChange messageData = makeMessageData(salesRecords.get(0), context);
            featureEngineService.generate(messageData);

            List<String> activeRecordIds = salesRecords.stream().map(DBRecord::getId).collect(Collectors.toList());

            // 生成联系人洞察特征
            IFilter filter = SearchUtil.filter("user_api_name", Operator.ISN, "");
            IObjectData dataObjectData = findDataByFeatureData(user, "ActivityUserObj", "active_record_id",
                    activeRecordIds, Lists.newArrayList(filter));
            if (null != dataObjectData) {
                FeatureMqModel.Message messageMq = new FeatureMqModel.Message();
                messageMq.setTenantId(initData.getTenantId());
                messageMq.setObjectId(dataObjectData.get("active_record_id", String.class));
                messageMq.setType(FeatureMqModel.MessageType.PARTICIPANT.getMessageType());
                messageMq.setObjectApiName("ActiveRecordObj");
                featureEngineService.generateActivity(messageMq);
            }

            // 生成需求洞察特征
            IObjectData dataObjectData1 = findDataByFeatureData(user, "RequirementObj", "active_record_id",
                    activeRecordIds, null);
            if (null != dataObjectData1) {
                FeatureMqModel.Message messageMq = new FeatureMqModel.Message();
                messageMq.setTenantId(initData.getTenantId());
                messageMq.setObjectId(dataObjectData.get("active_record_id", String.class));
                messageMq.setType(FeatureMqModel.MessageType.REQUIREMENT.getMessageType());
                messageMq.setObjectApiName("ActiveRecordObj");
                featureEngineService.generateActivity(messageMq);
            }

            // 生成原文标注特征
            refreshDataTagService.refreshData(initData.getTenantId(), data.getId(), initData.getObjectApiName());
        }
    }

    private ObjectData.ObjectChange makeMessageData(IObjectData data, ObjectData.ObjectChange.Context context) {
        ObjectData.ObjectChange messageData = new ObjectData.ObjectChange();
        messageData.setContext(context);
        messageData.setObjectId(data.getId());
        messageData.setEntityId(data.getDescribeApiName());
        messageData.setTriggerType(ObjectData.ObjectChange.TRIGGER_TYPE_INSERT);
        messageData.setAfterTriggerData((JSONObject) JSONObject.toJSON(data));
        return messageData;
    }

    public IObjectData findDataByFeatureData(User user, String apiName, String field, List<String> dataIds,
            List<IFilter> filters) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        if (dataIds.size() == 1) {
            query.addFilter(field, Operator.EQ, dataIds.get(0));
        } else if (dataIds.size() > 1) {
            query.addFilter(field, Operator.IN, dataIds);
        }

        query.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");
        query.setLimit(1);
        if (CollectionUtils.notEmpty(filters)) {
            query.addFilters(filters);
        }
        OrderBy orderBy = new OrderBy(DBRecord.CREATE_TIME, false);
        query.setOrders(Lists.newArrayList(orderBy));
        List<IObjectData> dataList = serviceFacade.findBySearchQueryIgnoreAll(user, apiName, query).getData();
        if (CollectionUtils.notEmpty(dataList)) {
            return dataList.get(0);
        } else {
            return null;
        }
    }

}
