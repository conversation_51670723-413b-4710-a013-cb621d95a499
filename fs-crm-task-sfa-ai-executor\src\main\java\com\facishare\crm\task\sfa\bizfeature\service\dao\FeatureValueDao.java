package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.FeatureValueConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FeatureValueDao {

    @Autowired
    private ServiceFacade serviceFacade;
    public List<IObjectData> fetchFeatureValuesByFeatureIds(User user, List<String> featureIds,List<String> objectIds) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, FeatureValueConstants.FEATURE_ID, featureIds);
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, FeatureValueConstants.OBJECT_ID, objectIds);
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.FEATURE_VALUE, query).getData();
    }
    public List<IObjectData> fetchFeatureValuesByFeatureIds(User user, List<String> featureIds) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, FeatureValueConstants.FEATURE_ID, featureIds);
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.FEATURE_VALUE, query).getData();
    }

    public Object getFeatureValue(IObjectData featureValue) {
        String type=featureValue.get(FeatureValueConstants.RETURN_DATA_TYPE, String.class);
        switch (type) {
            case "text":
                return featureValue.get(FeatureValueConstants.ORIGINAL_VALUE_TEXT, String.class);
            case "numeric":
                return featureValue.get(FeatureValueConstants.ORIGINAL_VALUE_NUMBER, String.class);
            case "bool":
                return featureValue.get(FeatureValueConstants.ORIGINAL_VALUE_BOOL, String.class);
            default:
                return null;
        }
    }
    /**
     *   trigger_value说明：
     *   type:field 字段,feature_value 特征值
     *   field:字段名
     *   trigger_value:原始值
     *   示例数据
     *   {"type":"field","trigger_value":{"field1":"制造行业","field2":"VIP"}}
     *   {"type":"feature_value ","feature_value":"true"}
     */
    public Object getFeatureTriggerValue(String tenantId,IObjectData featureValue, Map<String, IObjectDescribe> describeMap) {
        Map<String, Object> triggerValue = featureValue.get(FeatureValueConstants.TRIGGER_VALUE, Map.class);
        if (Objects.isNull(triggerValue) || triggerValue.isEmpty()) {
            return null;
        }

        if (triggerValue.containsKey("feature_value")) {
            return triggerValue.get("feature_value");
        }
        if (triggerValue.containsKey("trigger_value")) {
            Map<String, Object> triggerValueMap = (Map<String, Object>) triggerValue.get("trigger_value");
            if (triggerValueMap == null || triggerValueMap.isEmpty()) {
                return null;
            }
            String objectApiName = triggerValue.get(FeatureValueConstants.TRIGGER_OBJECT_API_NAME).toString();
            IObjectDescribe objectDescribe = describeMap.get(objectApiName);
            if (objectDescribe == null) {
                objectDescribe = serviceFacade.findObject(tenantId, objectApiName);
            }
            if (objectDescribe == null) {
                return null;
            }
            Map<String, Object> result = new HashMap<>();
            for (String fieldName : triggerValueMap.keySet()) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
                if (fieldDescribe == null) {
                    return null;
                }
                if (IFieldType.SELECT_ONE.equals(fieldDescribe.getType())) {
                    List<Map> optionsList = fieldDescribe.get("options", List.class);
                    for (Map option : optionsList) {
                        if (option.get("value").toString().equals(triggerValueMap.get(fieldName).toString())) {
                            result.put(fieldDescribe.getLabel(), option.get("label").toString());
                            break;
                        }
                    }
                } else if (IFieldType.DATE_TIME.equals(fieldDescribe.getType())) {
                    result.put(fieldDescribe.getLabel(), getDatetimeStr(triggerValueMap.get(fieldName).toString(), "yyyy-MM-dd HH:mm"));
                } else if (IFieldType.SELECT_MANY.equals(fieldDescribe.getType())) {
                    List<Map> optionsList = fieldDescribe.get("options", List.class);
                    List<String> values = (List<String>) triggerValueMap.get(fieldName);
                    List<String> valuesLabel = new ArrayList<>();
                    for (Map option : optionsList) {
                        if (values.contains(option.get("value").toString())) {
                            valuesLabel.add(option.get("label").toString());
                        }
                    }
                    result.put(fieldDescribe.getLabel(), valuesLabel);
                } else {
                    result.put(fieldDescribe.getLabel(), triggerValueMap.get(fieldName));
                }
            }
            return result;
        }
        return null;
    }

    public String getDatetimeStr(String timestamp, String pattern) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        java.util.Date date = new Date(Long.parseLong(timestamp));
        return simpleDateFormat.format(date);
    }

    public List<IObjectData> fetchFeatureValuesByObjApiName(User user, String objApiName, String triggerObjApiName, List<String> featureIds) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        query.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");
        query.addFilter(FeatureValueConstants.OBJECT_API_NAME, Operator.EQ, objApiName);
        query.addFilter(FeatureValueConstants.TRIGGER_OBJECT_API_NAME, Operator.EQ, triggerObjApiName);
        query.addFilter(FeatureValueConstants.FEATURE_ID, Operator.IN, featureIds);
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList(FeatureValueConstants.OBJECT_API_NAME
                , FeatureValueConstants.OBJECT_ID
                , FeatureValueConstants.TRIGGER_OBJECT_API_NAME));
        query.setGroupByParameter(groupByParameter);

        return serviceFacade.aggregateFindBySearchQuery(user, query, FeatureConstants.FEATURE_VALUE);
    }
}
