package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.constant.*;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureMqModel;
import com.facishare.crm.task.sfa.bizfeature.service.dao.FeatureDao;
import com.facishare.crm.task.sfa.bizfeature.service.dao.FeatureValueDao;
import com.facishare.crm.task.sfa.bizfeature.util.ScheduledUtil;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.model.ObjectData;
import com.facishare.crm.task.sfa.services.SFALicenseService;
import com.facishare.crm.task.sfa.util.FeatureBaseDataUtils;
import com.facishare.crm.task.sfa.util.SFAConfigUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;

import com.facishare.crm.sfa.lto.activity.mongo.ParagraphDocument;
import com.facishare.crm.sfa.lto.activity.mongo.ParagraphMongoDao;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 特征引擎服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FeatureEngineService {
    static final int MAX_LIMIT = 1000;
    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ParseServiceManager parseServiceManager;

    @Resource
    private SFALicenseService sfaLicenseService;

    @Resource
    private BaseDataMergeManager baseDataMergeManager;

    @Resource
    private ScoringRuleCalcService scoringRuleCalcService;

    @Resource
    private ParagraphMongoDao paragraphMongoDao;

    @Resource
    private FeatureDao featureDao;

    @Resource
    private FeatureValueDao featureValueDao;

    public void generate(ObjectData.ObjectChange message) {
        generate(message, null);
    }

    /**
     * 生成特征
     *
     * @param message 活动消息
     */
    public void generate(ObjectData.ObjectChange message, FeatureModel.ParseExtData parseExtData) {
        StopWatch stopWatch = StopWatch.createStarted("generate" + message.getEntityId() + message.getObjectId());
        // log.info("featureEngineService generate{}", message);
        User user = User.systemUser(message.getContext().getTenantId());
        try {
            // 对象数据过滤 license 校验
            String aiLicense = "ai_interactive_assistant_app";
            if (!sfaLicenseService.checkModuleLicenseExist(user.getTenantId(), aiLicense)) {
                return;
            }
            if (!SFAConfigUtil.isOpenCustomerProfileAgent(user.getTenantId())) {
                return;
            }

            stopWatch.lap("checkLicense");

            // 对象特征查询
            List<IObjectData> featureList = getFeatureListData(message, user, parseExtData);
            stopWatch.lap("getFeatureListData");
            calcAndSave(message, parseExtData, user, featureList, stopWatch);

            stopWatch.logSlow(500);

        } catch (Exception e) {
            log.error("featureEngineService generate error{}", message, e);
        }
    }

    private void calcAndSave(ObjectData.ObjectChange message, FeatureModel.ParseExtData parseExtData, User user,
            List<IObjectData> featureList, StopWatch stopWatch) {
        // 特征值计算
        List<IObjectData> featureValueList = calcFeatureValue(message, user, featureList, stopWatch, parseExtData);
        if (featureValueList.isEmpty()) {
            return;
        }
        stopWatch.lap("calcFeatureValue");
        // 特征值存储
        List<IObjectData> saveFeatureValueList = saveFeature(message, user, featureValueList);
        stopWatch.lap("saveFeature");
        // 使用量计数等处理

        // 促发计算特征分数
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(
                () -> scoringRuleCalcService.featureValueScoreCalc(user.getTenantId(), saveFeatureValueList)).run();
        stopWatch.lap("scoringRuleCalc");
    }

    private List<IObjectData> saveFeature(ObjectData.ObjectChange message, User user,
            List<IObjectData> featureValueList) {
        String objectApiName = featureValueList.get(0).get(FeatureValueConstants.OBJECT_API_NAME, String.class);
        String objectId = featureValueList.get(0).get(FeatureValueConstants.OBJECT_ID, String.class);
        List<String> featureIds = featureValueList.stream()
                .map(x -> x.get(FeatureValueConstants.FEATURE_ID, String.class)).collect(Collectors.toList());
        SearchTemplateQueryPlus featureValueSearchQuery = SearchUtil.buildBaseSearchQuery();
        featureValueSearchQuery.addFilter(FeatureValueConstants.OBJECT_API_NAME, Operator.EQ,
                objectApiName);
        featureValueSearchQuery.addFilter(FeatureValueConstants.OBJECT_ID, Operator.EQ, objectId);
        featureValueSearchQuery.addFilter(FeatureValueConstants.FEATURE_ID, Operator.IN, featureIds);
        featureValueSearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");

        List<IObjectData> oldFeatureValueList = serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.FEATURE_VALUE, featureValueSearchQuery).getData();
        List<IObjectData> newFeatureValueList = new ArrayList<>();
        List<IObjectData> upFeatureValueList = new ArrayList<>();
        List<IObjectData> featureValueHistoryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(oldFeatureValueList)) {
            Map<String, IObjectData> oldFeatureValueMap = oldFeatureValueList.stream()
                    .collect(Collectors.toMap(
                            featureValue -> featureValue.get(FeatureValueConstants.FEATURE_ID, String.class),
                            Function.identity(),
                            (oldValue, newValue) -> oldValue));
            splitUpData(message, featureValueList, oldFeatureValueMap, featureValueHistoryList, upFeatureValueList,
                    newFeatureValueList);
        } else {
            newFeatureValueList.addAll(featureValueList);
        }

        if (CollectionUtils.isNotEmpty(upFeatureValueList)) {
            upFeatureValueList = serviceFacade.batchUpdate(upFeatureValueList, user);
        }

        if (CollectionUtils.isNotEmpty(featureValueHistoryList)) {
            serviceFacade.bulkSaveObjectData(featureValueHistoryList, user);
        }
        if (CollectionUtils.isNotEmpty(newFeatureValueList)) {
            newFeatureValueList = serviceFacade.bulkSaveObjectData(newFeatureValueList, user);
            upFeatureValueList.addAll(newFeatureValueList);
        }

        return upFeatureValueList;
    }

    private void splitUpData(ObjectData.ObjectChange message, List<IObjectData> featureValueList,
            Map<String, IObjectData> oldFeatureValueMap, List<IObjectData> featureValueHistoryList,
            List<IObjectData> upFeatureValueList, List<IObjectData> newFeatureValueList) {
        for (IObjectData featureValue : featureValueList) {
            IObjectData oldFeatureValue = oldFeatureValueMap
                    .get(featureValue.get(FeatureValueConstants.FEATURE_ID, String.class));
            if (oldFeatureValue != null) {
                if (!featureChange(featureValue, oldFeatureValue)) {
                    continue;
                }

                IObjectData featureValueHistory = new com.facishare.paas.metadata.impl.ObjectData();
                featureValueHistory.setTenantId(message.getContext().getTenantId());
                featureValueHistory.setDescribeApiName(FeatureConstants.FEATURE_VALUE_HISTORY);
                featureValueHistory.setOwner(Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
                featureValueHistory.setName(FeatureBaseDataUtils.generateName());
                featureValueHistory.set(FeatureValueHistoryConstants.VALUE_ID, oldFeatureValue.getId());
                setCalcData(oldFeatureValue, featureValueHistory);
                featureValueHistoryList.add(featureValueHistory);

                setCalcData(featureValue, oldFeatureValue);
                upFeatureValueList.add(oldFeatureValue);
            } else {
                newFeatureValueList.add(featureValue);
            }
        }
    }

    private boolean featureChange(IObjectData featureValue, IObjectData oldFeatureValue) {
        Object triggerValue = featureValue.get(FeatureValueConstants.TRIGGER_VALUE);

        if (valueSame(featureValue, oldFeatureValue) && triggerValue != null
                && triggerValue.equals(oldFeatureValue.get(FeatureValueConstants.TRIGGER_VALUE))) {
            return false;
        }
        return true;
    }

    private boolean valueSame(IObjectData featureValue, IObjectData oldFeatureValue) {
        String returnDataType = featureValue.get(FeatureValueConstants.RETURN_DATA_TYPE, String.class);
        if (ParseRuleConstants.ReturnDataType.TEXT.getReturnDataType().equals(returnDataType)) {
            String oldValue = oldFeatureValue.get(FeatureValueConstants.ORIGINAL_VALUE_TEXT, String.class);
            String value = featureValue.get(FeatureValueConstants.ORIGINAL_VALUE_TEXT, String.class);
            return Objects.equals(oldValue, value);
        } else if (ParseRuleConstants.ReturnDataType.BOOL.getReturnDataType().equals(returnDataType)) {
            Boolean oldValue = oldFeatureValue.get(FeatureValueConstants.ORIGINAL_VALUE_BOOL, Boolean.class);
            Boolean value = featureValue.get(FeatureValueConstants.ORIGINAL_VALUE_BOOL, Boolean.class);
            return Objects.equals(oldValue, value);
        } else if (ParseRuleConstants.ReturnDataType.NUMERIC.getReturnDataType().equals(returnDataType)) {
            Double oldValue = oldFeatureValue.get(FeatureValueConstants.ORIGINAL_VALUE_NUMBER, Double.class);
            Double value = featureValue.get(FeatureValueConstants.ORIGINAL_VALUE_NUMBER, Double.class);
            return Objects.equals(oldValue, value);
        }
        return false;
    }

    private void setCalcData(IObjectData oldFeatureValue, IObjectData featureValueHistory) {
        featureValueHistory.set(FeatureValueConstants.CALC_TIME,
                oldFeatureValue.get(FeatureValueConstants.CALC_TIME));
        featureValueHistory.set(FeatureValueConstants.ORIGINAL_VALUE_BOOL,
                oldFeatureValue.get(FeatureValueConstants.ORIGINAL_VALUE_BOOL));
        featureValueHistory.set(FeatureValueConstants.ORIGINAL_VALUE_NUMBER,
                oldFeatureValue.get(FeatureValueConstants.ORIGINAL_VALUE_NUMBER));
        featureValueHistory.set(FeatureValueConstants.ORIGINAL_VALUE_TEXT,
                oldFeatureValue.get(FeatureValueConstants.ORIGINAL_VALUE_TEXT));
        featureValueHistory.set(FeatureValueConstants.TRIGGER_VALUE,
                oldFeatureValue.get(FeatureValueConstants.TRIGGER_VALUE));
    }

    private List<IObjectData> calcFeatureValue(ObjectData.ObjectChange message, User user,
            List<IObjectData> featureList, StopWatch stopWatch, FeatureModel.ParseExtData parseExtData) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        searchTemplateQueryPlus.setLimit(MAX_LIMIT);

        List<IObjectData> rules = baseDataMergeManager.findBySearchQueryIgnoreAll(user,
                FeatureConstants.PARSE_RULE, searchTemplateQueryPlus);
        stopWatch.lap("findRule");
        Map<String, IObjectData> rulesMap = rules.stream()
                .collect(Collectors.toMap(
                        rule -> rule.getId(),
                        rule -> rule));
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), message.getEntityId());
        stopWatch.lap("finDescribe");
        IObjectData data = serviceFacade.findObjectData(user.getTenantId(), message.getObjectId(), describe);
        data.set(FeatureConstants.PARSE_EXT_DATA, parseExtData);
        stopWatch.lap("finData");
        List<IObjectData> featureValueList = new ArrayList<>();
        for (IObjectData feature : featureList) {
            // 特征值计算
            String featureValue = feature.get(FeatureConstants.RULE_ID, String.class);
            IObjectData rule = rulesMap.get(featureValue);
            if (rule == null) {
                continue;
            }
            String ruleType = rule.get(ParseRuleConstants.CALC_METHOD, String.class);
            ParseRuleService parseRuleService = parseServiceManager.getActionService(ruleType);
            FeatureModel.FeatureData featureData = parseRuleService.parse(user, feature, rule, data, describe);
            if (featureData == null) {
                continue;
            }
            // 特征值对象生成
            IObjectData dataValue = makeFeatureValue(message, feature, featureData);
            featureValueList.add(dataValue);
            stopWatch.lap("parseRule" + rule.getName());
        }
        return featureValueList;
    }

    private IObjectData makeFeatureValue(ObjectData.ObjectChange message, IObjectData feature,
            FeatureModel.FeatureData featureData) {
        IObjectData data = new com.facishare.paas.metadata.impl.ObjectData();
        data.setTenantId(message.getContext().getTenantId());
        data.setDescribeApiName(FeatureConstants.FEATURE_VALUE);
        data.set(FeatureValueConstants.OBJECT_API_NAME,
                StringUtils.isNotBlank(featureData.getMasterApiName()) ? featureData.getMasterApiName()
                        : message.getEntityId());
        data.set(FeatureValueConstants.OBJECT_ID,
                StringUtils.isNotBlank(featureData.getMasterId()) ? featureData.getMasterId() : message.getObjectId());
        data.set(FeatureValueConstants.FEATURE_ID, feature.getId());
        data.set(FeatureValueConstants.RETURN_DATA_TYPE, featureData.getReturnDataType());
        data.set(FeatureValueConstants.ORIGINAL_VALUE_TEXT, featureData.getValueText());
        data.set(FeatureValueConstants.ORIGINAL_VALUE_BOOL, featureData.getValueBoolean());
        data.set(FeatureValueConstants.ORIGINAL_VALUE_NUMBER, featureData.getValueNumber());
        data.set(FeatureValueConstants.TRIGGER_OBJECT_API_NAME, message.getEntityId());
        data.set(FeatureValueConstants.TRIGGER_OBJECT_ID, message.getObjectId());
        data.set(FeatureValueConstants.CALC_TIME, System.currentTimeMillis());
        data.set(FeatureValueConstants.TRIGGER_VALUE, featureData.getTriggerValue());
        data.setOwner(Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
        try {
            // TODO 暂时为了看多个实例是哪个ip生成的
            InetAddress ip = InetAddress.getLocalHost();
            data.set(FeatureValueConstants.MASTER_OBJECT_ID, ip.getHostAddress());
        } catch (UnknownHostException e) {
        }

        data.setName(FeatureBaseDataUtils.generateName());
        return data;
    }

    private List<IObjectData> getFeatureListData(ObjectData.ObjectChange message, User user,
            FeatureModel.ParseExtData parseExtData) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, "0")
                .addFilter(FeatureConstants.STATUS, Operator.EQ,
                        FeatureConstants.StatusType.ENABLED.getStatusType());
        if (null != parseExtData) {
            addExtFilter(parseExtData, searchTemplateQueryPlus);
        } else {
            searchTemplateQueryPlus.addFilter(FeatureConstants.DATA_SOURCE_OBJECT, Operator.EQ, message.getEntityId());
        }

        searchTemplateQueryPlus.setLimit(MAX_LIMIT);

        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.FEATURE, searchTemplateQueryPlus);
        List<IObjectData> objectDataList = queryResult.getData();
        if (ObjectData.ObjectChange.TRIGGER_TYPE_MQ.equals(message.getTriggerType())) {
            return objectDataList;
        }
        String triggerTypeMsg = message.getTriggerType();
        String triggerType = "";
        if (ObjectData.ObjectChange.TRIGGER_TYPE_INSERT.equals(triggerTypeMsg)) {
            triggerType = FeatureConstants.TriggerType.ADD.getTriggerType();
        } else if (ObjectData.ObjectChange.TRIGGER_TYPE_UPDATE.equals(triggerTypeMsg)) {
            triggerType = FeatureConstants.TriggerType.EDIT.getTriggerType();
        }
        List<IObjectData> objectDataList1 = Lists.newArrayList();
        for (IObjectData objectData : objectDataList) {
            String triggerTypeStr = objectData.get(FeatureConstants.TRIGGER_TYPE, String.class);
            if (triggerTypeStr.contains(triggerType)) {
                objectDataList1.add(objectData);
            }
        }

        // 特征字段过滤数据
        List<IObjectData> objectDataListData = new ArrayList<>();
        if (FeatureConstants.TriggerType.EDIT.getTriggerType().equals(triggerType)) {
            for (IObjectData objectData : objectDataList1) {
                addChangeData(message, objectData, objectDataListData);
            }
        } else {
            objectDataListData = objectDataList1;
        }

        return objectDataListData;
    }

    private void addChangeData(ObjectData.ObjectChange message, IObjectData objectData,
            List<IObjectData> objectDataListData) {
        String fieldInfo = objectData.get(FeatureConstants.DATA_SOURCE_FIELD, String.class);
        String[] fields = new String[] { fieldInfo };
        if (fieldInfo.startsWith(FeatureConstants.FIELDS)) {
            fieldInfo = fieldInfo.substring(FeatureConstants.FIELDS.length());
            fields = fieldInfo.split(",");
        }
        for (String field : fields) {
            if (message.changedFields().contains(field)) {
                objectDataListData.add(objectData);
            }
        }
    }

    private void addExtFilter(FeatureModel.ParseExtData parseExtData,
            SearchTemplateQueryPlus searchTemplateQueryPlus) {
        if (isTagFeature(parseExtData)) {
            searchTemplateQueryPlus.addFilter(FeatureConstants.DATA_SOURCE_TYPE, Operator.EQ,
                    FeatureConstants.DataSourceType.TAG.getDataSourceType());
        } else if (FeatureMqModel.MessageType.PARTICIPANT.getMessageType().equals(parseExtData.getType())
                || FeatureMqModel.MessageType.REQUIREMENT.getMessageType().equals(parseExtData.getType())) {
            searchTemplateQueryPlus.addFilter(FeatureConstants.DATA_SOURCE_TYPE, Operator.EQ,
                    FeatureConstants.DataSourceType.ACTIVITY_AGG.getDataSourceType());
        }

        String objctApiName = null;
        if (FeatureMqModel.MessageType.REQUIREMENT.getMessageType().equals(parseExtData.getType())) {
            objctApiName = CommonConstant.REQUIREMENT_OBJ;
        } else if (FeatureMqModel.MessageType.PARTICIPANT.getMessageType().equals(parseExtData.getType())) {
            objctApiName = CommonConstant.ActivityUserObj;
        }
        if (null != objctApiName) {
            searchTemplateQueryPlus.addFilter(FeatureConstants.DATA_SOURCE_OBJECT, Operator.EQ, objctApiName);
        }

    }

    private static boolean isTagFeature(FeatureModel.ParseExtData parseExtData) {
        return FeatureMqModel.MessageType.TAG.getMessageType().equals(parseExtData.getType());
    }

    /**
     * 根据标注生成特征
     *
     * @param message 标注消息
     */
    public void generateTag(FeatureMqModel.Message message) {
        ObjectData.ObjectChange objectChange = getObjectChange(message);

        List<String> queryArr = Lists.newArrayList("_id", "tags");
        List<ParagraphDocument> paragraphList = paragraphMongoDao.queryByIdsWithFields(message.getTenantId(),
                message.getObjectId(), queryArr);
        if (StringUtils.isEmpty(message.getObjectId())) {
            log.warn("generateTag ObjectId is empty{}", message.getObjectId());
            return;
        }

        Map<String, List<FeatureModel.ParseTagData>> tagMap = Maps.newHashMap();
        for (ParagraphDocument paragraphDocument : paragraphList) {
            List<String> tags = paragraphDocument.getTags();
            if (CollectionUtils.isEmpty(tags)) {
                continue;
            }
            for (String tag : tags) {
                FeatureModel.ParseTagData parseTagData = new FeatureModel.ParseTagData();
                parseTagData.setId(paragraphDocument.getId().toString());
                parseTagData.setTag(tag);
                tagMap.computeIfAbsent(tag, k -> new ArrayList<>()).add(parseTagData);
            }
        }

        FeatureModel.ParseExtData parseExtData = new FeatureModel.ParseExtData();
        parseExtData.setTagMap(tagMap);
        parseExtData.setType(message.getType());
        generate(objectChange, parseExtData);
    }

    @NotNull
    private ObjectData.ObjectChange getObjectChange(FeatureMqModel.Message message) {
        ObjectData.ObjectChange objectChange = new ObjectData.ObjectChange();
        objectChange.setEntityId(message.getObjectApiName());
        objectChange.setObjectId(message.getObjectId());
        objectChange.setTriggerType(ObjectData.ObjectChange.TRIGGER_TYPE_MQ);
        ObjectData.ObjectChange.Context context = new ObjectData.ObjectChange.Context();
        context.setTenantId(message.getTenantId());
        objectChange.setContext(context);
        return objectChange;
    }

    /**
     * 根据活动生成特征
     *
     * @param message 活动消息
     */
    public void generateActivity(FeatureMqModel.Message message) {
        ObjectData.ObjectChange objectChange = getObjectChange(message);

        FeatureModel.ParseExtData parseExtData = new FeatureModel.ParseExtData();
        parseExtData.setType(message.getType());
        parseExtData.setObjectApiName(message.getObjectApiName());
        parseExtData.setObjectId(message.getObjectId());
        generate(objectChange, parseExtData);
    }

    /**
     * 根据定时任务生成特征
     *
     * @param tenantId 活动消息
     */
    public void generateJob(String tenantId) {
        if (!SFAConfigUtil.isOpenCustomerProfileAgent(tenantId)) {
            return;
        }
        StopWatch stopWatch = StopWatch.createStarted("generateJob" + tenantId);
        User user = User.systemUser(tenantId);

        List<IObjectData> allFeatures = featureDao.fetchScheduledFeature(user);
        Map<String, List<IObjectData>> featureMap = Maps.newHashMap();
        // 过滤分组有效的特征
        for (IObjectData feature : allFeatures) {
            String timerInfo = feature.get(FeatureConstants.TIMER_INFO, String.class);

            if (ScheduledUtil.isScheduledExecDate(timerInfo)) {
                String obj = feature.get(FeatureConstants.DATA_SOURCE_OBJECT, String.class);
                String mObj = feature.get(FeatureConstants.MASTER_OBJECT_API_NAME, String.class);
                featureMap.computeIfAbsent(obj + mObj, k -> new ArrayList<>()).add(feature);
            }
        }

        for (Map.Entry<String, List<IObjectData>> entry : featureMap.entrySet()) {
            List<IObjectData> features = entry.getValue();
            String obj = features.get(0).get(FeatureConstants.DATA_SOURCE_OBJECT, String.class);
            String mObj = features.get(0).get(FeatureConstants.MASTER_OBJECT_API_NAME, String.class);
            String field = features.get(0).get(FeatureConstants.MASTER_FIELD_API_NAME, String.class);

            List<String> featureIds = features.stream().map(IObjectData::getId).collect(Collectors.toList());

            List<IObjectData> featureValues = featureValueDao.fetchFeatureValuesByObjApiName(user, mObj, obj,
                    featureIds);

            for (IObjectData featureValue : featureValues) {
                List<IObjectData> objectDataList;
                if (obj.equals(mObj)) {
                    objectDataList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId,
                            Lists.newArrayList(featureValue.get(FeatureValueConstants.OBJECT_ID, String.class)),
                            featureValue.get(FeatureValueConstants.OBJECT_API_NAME, String.class));

                } else {
                    SearchTemplateQueryPlus searchTemplateQueryPlus = SearchUtil.buildBaseSearchQuery();
                    searchTemplateQueryPlus.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");
                    searchTemplateQueryPlus.addFilter(field, Operator.EQ, featureValue.get(FeatureValueConstants.OBJECT_ID, String.class));
                    OrderBy orderBy = new OrderBy();
                    orderBy.setFieldName(DBRecord.CREATE_TIME);
                    orderBy.setIsAsc(false);
                    searchTemplateQueryPlus.setLimit(2);
                    searchTemplateQueryPlus.setOrders(Lists.newArrayList(orderBy));
                    objectDataList =  serviceFacade.findBySearchQueryIgnoreAll(user,
                            obj, searchTemplateQueryPlus).getData();
                }
                if (CollectionUtils.isNotEmpty(objectDataList)) {
                    IObjectData  objectData = objectDataList.get(0);
                    ObjectData.ObjectChange message = new ObjectData.ObjectChange();
                    ObjectData.ObjectChange.Context context = new ObjectData.ObjectChange.Context();
                    context.setTenantId(tenantId);
                    message.setContext(context);
                    message.setObjectId(objectData.getId());
                    message.setEntityId(objectData.getDescribeApiName());
                    calcAndSave(message, null, user, features, stopWatch);
                }
            }

        }

    }

}