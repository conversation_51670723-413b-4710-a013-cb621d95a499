package com.facishare.crm.task.sfa.correct.dict;

import com.facishare.crm.task.sfa.correct.ConfusionWord;
import com.facishare.crm.task.sfa.correct.Level;
import com.facishare.crm.task.sfa.correct.SpringConfig;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

@Component
@Slf4j
public class Confusion implements InitializingBean {
    @Qualifier("com.facishare.crm.task.sfa.correct.SpringConfig")
    private final SpringConfig springConfig;
    @Getter
    private final List<ConfusionWord> sent = new ArrayList<>();

    public Confusion(SpringConfig springConfig) {
        this.springConfig = springConfig;
    }

    @Override
    public void afterPropertiesSet() {
        InputStream resource = Confusion.class.getClassLoader().getResourceAsStream(springConfig.getConfusionEnDictPath());
        if (resource == null) {
            log.warn("resource is null, path={}", springConfig.getStrokeDictPath());
            return;
        }
        try (Stream<String> stream = new BufferedReader(new InputStreamReader(resource)).lines()) {
            stream.forEach(line -> {
                String[] split = line.split("\\|");
                if (split.length == 2) {
                    sent.add(new ConfusionWord(split[0], split[1], Level.Sent));
                }
            });
        }
    }
}