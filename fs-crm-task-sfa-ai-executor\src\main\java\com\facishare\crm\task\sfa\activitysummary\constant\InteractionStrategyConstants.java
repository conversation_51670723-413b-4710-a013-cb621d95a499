package com.facishare.crm.task.sfa.activitysummary.constant;

import com.google.common.collect.ImmutableMap;

import java.util.Map;

/**
 * 互动策略常量
 *
 * <AUTHOR>
 * @IgnoreI18nFile
 */
public interface InteractionStrategyConstants {
	String INTERACTION_STRATEGY = "InteractionStrategyObj";
	String INTERACTION_STRATEGY_LABEL = "互动策略";

	//使用map初始化对象名和字段的映射，使用guava
	Map<String, String> USED_OBJECT_FIELD_MAP = ImmutableMap.<String, String>builder()
			.put("AccountObj", "last_followed_time")
			.put("NewOpportunityObj", "last_followed_time")
			.build();

	/**
     * 使用对象
     */
	String USED_OBJECT_API_NAME = "used_object_api_name";

    enum UsedObjectApiNameType {
		/**
         * 客户
         */
		ACCOUNTOBJ("AccountObj"),
		/**
         * 商机
         */
		NEWOPPORTUNITYOBJ("NewOpportunityObj");
		private final String usedObjectApiName;

		public String getUsedObjectApiNameType() {
            return usedObjectApiName;
        }


		UsedObjectApiNameType(String usedObjectApiName) {
            this.usedObjectApiName = usedObjectApiName;
        }
	}
	/**
     * 使用条件
     */
	String CONDITION = "condition";
	/**
     * 优先级
     */
	String PRIORITY = "priority";
	/**
     * 备注
     */
	String REMARK = "remark";
	/**
     * 计算状态
     */
	String CALCULATE_STATUS = "calculate_status";
	enum CalculateStatusType {
		/**
         * 计算中
         */
		ING("ing") ,
		/**
         * 生效
         */
		COMPLETE("complete") ;
		private final String calculateStatus;

		public String getCalculateStatusType() {
            return calculateStatus;
        }


		CalculateStatusType(String calculateStatus) {
            this.calculateStatus = calculateStatus;
        }
	}

}