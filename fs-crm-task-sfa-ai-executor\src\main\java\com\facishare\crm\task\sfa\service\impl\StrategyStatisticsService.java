package com.facishare.crm.task.sfa.service.impl;

import com.facishare.crm.task.sfa.activitysummary.constant.InteractionStrategyConstants;
import com.facishare.crm.task.sfa.util.InteractionStrategyUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 策略统计服务
 * 负责策略统计相关功能
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class StrategyStatisticsService {

    @Autowired
    private ObjectDataProxy dataProxy;

    @Autowired
    private ServiceFacade serviceFacade;

    private static Long MAX_COUNT;

    static {
        ConfigFactory.getConfig("fs-sfa-ai", config -> {
            MAX_COUNT = config.getLong("def_strategy_count", 10000L);
        });
    }

    /**
     * 统计符合策略条件的客户数量
     *
     * @param user        用户信息
     * @param strategyData 策略数据
     * @return 客户数量
     */
    public Integer countCustomersByStrategy(User user, IObjectData strategyData) {
        // 获取一年前的午夜时间
        long oneYearAgoMidnight = getOneYearAgoMidnight();
        SearchTemplateQueryPlus searchAccountTemplateQuery = getSearchAccountTemplateQuery(strategyData,
                oneYearAgoMidnight);
        CountFieldDescribe countFieldDescribe = getCountFieldDescribe(
                strategyData.get(InteractionStrategyConstants.USED_OBJECT_API_NAME, String.class));
        try {
            Integer count = getCount(User.systemUser(user.getTenantId()), searchAccountTemplateQuery,
                    countFieldDescribe).orElse(0);
            Long strategyCountMax = Optional.ofNullable(serviceFacade.findTenantConfig(user, "strategy_count_max"))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).orElse(MAX_COUNT);
            if (count > strategyCountMax) {
                throw new ValidateException(MessageFormat.format("应用历史最大生效数据为{0}条，当前数据量为{1}", strategyCountMax, count));
            }
            return count;
        } catch (MetadataServiceException e) {
            throw new ValidateException("查询条件总数量失败");
        }
    }

    /**
     * 获取查询账户的模板查询
     */
    private SearchTemplateQueryPlus getSearchAccountTemplateQuery(IObjectData strategy, long lastFollowedStartTime) {
        List<com.facishare.crm.task.sfa.bizfeature.model.RuleWhere> ruleWhereList = InteractionStrategyUtil.getRuleWhereListByUseRange(strategy);
        String gtTimeFieldName = InteractionStrategyConstants.USED_OBJECT_FIELD_MAP
                .get(strategy.get(InteractionStrategyConstants.USED_OBJECT_API_NAME, String.class));
        if (CollectionUtils.isEmpty(ruleWhereList)) {
            SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                    .addFilter(gtTimeFieldName, Operator.GT, String.valueOf(lastFollowedStartTime))
                    .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
            searchTemplateQueryPlus.setLimit(1);
            searchTemplateQueryPlus.setOrders(Lists.newArrayList(new OrderBy(DBRecord.CREATE_TIME, true)));
            return searchTemplateQueryPlus;
        }
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus();
        List<Wheres> wheres = searchTemplateQueryPlus.getWheres();
        for (com.facishare.crm.task.sfa.bizfeature.model.RuleWhere ruleWhere : ruleWhereList) {
            List<IFilter> filters = Lists.newArrayList();
            List<com.facishare.crm.task.sfa.bizfeature.model.RuleWhere.FiltersBean> innerFilter = ruleWhere.getFilters();
            if (CollectionUtils.isEmpty(innerFilter)) {
                continue;
            }
            innerFilter.forEach(filter -> filters.add(SearchTemplateQueryPlus.getFilter(filter.getFieldName(),
                    Operator.valueOf(filter.getOperator()), filter.getFieldValues())));
            Wheres wheres1 = new Wheres();
            wheres1.setFilters(filters);
            wheres.add(wheres1);
        }
        searchTemplateQueryPlus.setWheres(wheres);
        searchTemplateQueryPlus.addFilter(DBRecord.IS_DELETED, Operator.EQ,
                String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        searchTemplateQueryPlus.addFilter(gtTimeFieldName, Operator.GT, String.valueOf(lastFollowedStartTime));
        searchTemplateQueryPlus.setLimit(1);
        return searchTemplateQueryPlus;
    }

    /**
     * 获取计数字段描述
     */
    public CountFieldDescribe getCountFieldDescribe(String objectApiName) {
        CountFieldDescribe count = new CountFieldDescribe();
        count.setApiName(objectApiName);
        count.setFieldApiName("count0");
        count.setCountFieldApiName("id");
        count.setCountType(Count.TYPE_COUNT);
        count.setReturnType("number");
        count.setDecimalPlaces(0);
        count.setSubObjectDescribeApiName(objectApiName);
        return count;
    }

    /**
     * 获取计数结果
     */
    public Optional<Integer> getCount(User user, SearchTemplateQuery query, CountFieldDescribe count)
            throws MetadataServiceException {
        return Optional.ofNullable(dataProxy.getCountResult(user.getTenantId(), count.getApiName(), count, query))
                .map(Objects::toString).map(Integer::parseInt);
    }

    /**
     * 计算一年前的午夜时间（毫秒值）
     *
     * @return 一年前的午夜时间戳
     */
    private long getOneYearAgoMidnight() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.YEAR, -1);
        return calendar.getTimeInMillis();
    }
} 