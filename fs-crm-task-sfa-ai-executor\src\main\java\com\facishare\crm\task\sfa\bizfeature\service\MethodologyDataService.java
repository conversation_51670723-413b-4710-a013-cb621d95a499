package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.constant.*;
import com.facishare.crm.task.sfa.bizfeature.model.MethodologyModel;
import com.facishare.crm.task.sfa.bizfeature.model.RuleWhere;
import com.facishare.crm.task.sfa.util.ActionContextUtil;
import com.facishare.crm.task.sfa.util.RuleWhereUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 方法论数据服务
 * 负责方法论相关数据的查询和保存
 */
@Service
@Slf4j
public class MethodologyDataService {
    private static final int MAX_METHODOLOGY_LIMIT = 2000;
    private static final String SEARCH_SOURCE_DB = "db";
    private static final String IS_DELETED_VALUE = "0";

    @Resource
    private ServiceFacade serviceFacade;

    /**
     * 保存所有实例数据（带事务控制）
     *
     * @param allInstanceData 方法论实例
     * @param user            用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAllInstanceData(MethodologyModel.AllInstanceData allInstanceData, User user) {
        try {
            List<IObjectData> nodeInstanceList = allInstanceData.getNodeInstanceList();
            List<IObjectData> methodologyInstance = allInstanceData.getMethodologyInstance();

            List<IObjectData> taskInstanceList = allInstanceData.getTaskInstanceList();
            List<IObjectData> instanceFeatureList = allInstanceData.getInstanceFeatureList();
            log.info("Start saving instance data: methodology={}, nodes={}, tasks={}, instanceFeatures={}",
                    nodeInstanceList.get(0).get(NodeInstanceConstants.METHODOLOGY_ID, String.class),
                    nodeInstanceList.size(),
                    taskInstanceList.size(),
                    instanceFeatureList.size());

            // 保存方法论实例
            List<IObjectData> updateMethodologyInstance = allInstanceData.getUpMethodologyInstance();
            if (!CollectionUtils.isEmpty(updateMethodologyInstance)) {
                serviceFacade.batchUpdate(updateMethodologyInstance, user);
            }
            if (!CollectionUtils.isEmpty(methodologyInstance)) {
                serviceFacade.bulkSaveObjectData(methodologyInstance, user);
            }

            // 批量保存节点实例
            if (!CollectionUtils.isEmpty(nodeInstanceList)) {
                serviceFacade.bulkSaveObjectData(nodeInstanceList, user);
            }

            // 批量保存任务实例
            if (!CollectionUtils.isEmpty(taskInstanceList)) {
                serviceFacade.bulkSaveObjectData(taskInstanceList, user);
            }

            // 批量保存实例特征关联
            if (!CollectionUtils.isEmpty(instanceFeatureList)) {
                serviceFacade.bulkSaveObjectData(instanceFeatureList, user,false,false, x -> ActionContextUtil.getSkipActionContext(user));
                log.info("Successfully saved {} instance features", instanceFeatureList.size());
            }

            log.info("Successfully saved all instance data");
        } catch (Exception e) {
            log.error("Failed to save instance data", e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 统一方法论处理
     * 根据不同对象类型处理方法论选择逻辑
     *
     * @param objectApiName   对象API名称
     * @param objectId        对象ID
     * @param user            用户信息
     * @param methodologyList 方法论列表
     * @return 匹配的方法论对象
     */
    public MethodologyModel.MatchMethodology unifyMethodology(String objectApiName, String objectId, User user,
                                                              List<IObjectData> methodologyList, List<IObjectData> detailRuleList) {

        if (CollectionUtils.isEmpty(methodologyList)) {
            log.info("Empty methodology list provided");
            return null;
        }
        Map<String, String> methodologyTypeMap = new HashMap<>();
        Map<String, List<IObjectData>> methodologyMap = new HashMap<>();
        for (IObjectData methodology : methodologyList) {
            String type = methodology.get(MethodologyConstants.TYPE, String.class);
            methodologyTypeMap.put(methodology.getId(), type);
            methodologyMap.computeIfAbsent(type, k -> new ArrayList<>()).add(methodology);
        }

        Map<String, List<IObjectData>> typeRuleMap = new HashMap<>();
        for (IObjectData detail : detailRuleList) {
            String methodologyId = detail.get(MethodologyRuleConstants.METHODOLOGY_ID, String.class);
            String type = methodologyTypeMap.get(methodologyId);
            typeRuleMap.computeIfAbsent(type, k -> new ArrayList<>()).add(detail);
        }

        String type = MethodologyConstants.Type.FLOW.getType();
        IObjectData matchFlow = matchFlow(objectApiName, objectId, user, methodologyMap.get(type),
                typeRuleMap.get(type));

        type = MethodologyConstants.Type.PROFILE.getType();
        List<IObjectData> matchMethodologyList = matchMethodology(objectApiName, objectId, user,
                methodologyMap.get(type), typeRuleMap.get(type));
        return MethodologyModel.MatchMethodology.builder()
                .matchFlow(matchFlow)
                .matchProfileList(matchMethodologyList)
                .build();
    }


    /**
     * 查询工作流任务
     *
     * @param nodeInstanceList 节点实例列表
     * @param matchMethodology 方法论
     * @param user             用户信息
     * @return 工作流任务列表
     */
    public List<IObjectData> queryNodeTask(List<IObjectData> nodeInstanceList, IObjectData matchMethodology, User user) {
        if (CollectionUtils.isEmpty(nodeInstanceList)) {
            return Lists.newArrayList();
        }

        List<String> nodeIdList = nodeInstanceList.stream()
                .map(node -> node.get(NodeInstanceConstants.NODE_ID).toString())
                .collect(Collectors.toList());

        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, "0")
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(NodeTaskConstants.METHODOLOGY_ID, Operator.EQ, matchMethodology.getId())
                .addFilter(NodeTaskConstants.NODE_ID, Operator.IN, nodeIdList);

        searchTemplateQueryPlus.setLimit(MAX_METHODOLOGY_LIMIT);

        return Optional.ofNullable(serviceFacade.findBySearchQueryIgnoreAll(
                        user,
                        NodeTaskConstants.API_NAME,
                        searchTemplateQueryPlus))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }

    /**
     * 获取工作流节点
     *
     * @param matchMethodology 匹配的方法论
     * @param user             用户信息
     * @param objectApiName    对象API名称
     * @param objectId         对象ID
     * @return 工作流节点列表
     */
    public List<IObjectData> getWorkFlowNode(IObjectData matchMethodology, User user, String objectApiName,
                                             String objectId) {
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(),
                Lists.newArrayList(objectId), objectApiName);
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("No object data found for object: {},objectApiName: {}", objectId, objectApiName);
            return Lists.newArrayList();
        }

        IObjectData objectData = dataList.get(0);
        List<IObjectData> objectWorkflowList = queryObjectWorkflow(matchMethodology, objectData, user);

        if (CollectionUtils.isEmpty(objectWorkflowList)) {
            log.info("No object workflow found for object: {},objectApiName: {}", objectId, objectApiName);
            return queryWorkFlowNode(matchMethodology, user, objectApiName, Lists.newArrayList());
        }

        List<String> nodeIdList = objectWorkflowList.stream()
                .map(DBRecord::getId)
                .collect(Collectors.toList());

        List<IObjectData> workFlowNodeList = queryWorkFlowNode(matchMethodology, user, objectApiName, nodeIdList);

        List<IObjectData> workFlowNodeListByObjectWorkflow = serviceFacade
                .findObjectDataByIdsIgnoreAll(user.getTenantId(), nodeIdList, MethodologyNodeConstants.API_NAME);

        if (!CollectionUtils.isEmpty(workFlowNodeListByObjectWorkflow)) {
            workFlowNodeList.addAll(workFlowNodeListByObjectWorkflow);
        }

        return workFlowNodeList;
    }

    /**
     * 查询对象工作流
     */
    public List<IObjectData> queryObjectWorkflow(IObjectData matchMethodology, IObjectData objectData, User user) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, "0")
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(ObjectMethodologyConstants.OBJECT_API_NAME, Operator.EQ, objectData.getDescribeApiName())
                .addFilter(ObjectMethodologyConstants.METHODOLOGY_ID, Operator.EQ, matchMethodology.getId());

        searchTemplateQueryPlus.setLimit(MAX_METHODOLOGY_LIMIT);
        searchTemplateQueryPlus.setOffset(0);

        return Optional.ofNullable(serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                        user, ObjectMethodologyConstants.API_NAME,
                        searchTemplateQueryPlus,
                        Lists.newArrayList(DBRecord.ID, ObjectMethodologyConstants.NODE_ID)))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }

    /**
     * 查询工作流节点
     */
    public List<IObjectData> queryWorkFlowNode(IObjectData matchMethodology, User user, String objectApiName,
                                               List<String> nodeIdList) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, "0")
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(MethodologyNodeConstants.OBJECT_API_NAME, Operator.EQ, objectApiName)
                .addFilter(MethodologyNodeConstants.STATUS, Operator.EQ,
                        MethodologyNodeConstants.StatusType.ENABLE.getStatusType())
                .addFilter(MethodologyNodeConstants.METHODOLOGY_ID, Operator.EQ, matchMethodology.getId());

        if (!CollectionUtils.isEmpty(nodeIdList)) {
            searchTemplateQueryPlus.addFilter(DBRecord.ID, Operator.NIN, nodeIdList);
        }

        searchTemplateQueryPlus.setLimit(MAX_METHODOLOGY_LIMIT);
        searchTemplateQueryPlus.setOffset(0);

        return Optional.ofNullable(serviceFacade.findBySearchQueryIgnoreAll(
                        user,
                        MethodologyNodeConstants.API_NAME,
                        searchTemplateQueryPlus))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }

    /**
     * 匹配方法论
     */
    public IObjectData matchFlow(String objectApiName, String objectId, User user,
                                 List<IObjectData> methodologyList, List<IObjectData> detailRuleList) {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(),
                Lists.newArrayList(objectId), objectApiName);

        if (CollectionUtils.isEmpty(methodologyList) || CollectionUtils.isEmpty(detailRuleList)
                || CollectionUtils.isEmpty(objectDataList)) {
            return null;
        }
        Map<String, List<IObjectData>> detailRuleMap = detailRuleList.stream()
                .collect(Collectors.groupingBy(x -> x.get(MethodologyRuleConstants.METHODOLOGY_ID, String.class)));

        IObjectData objectData = objectDataList.get(0);
        for (IObjectData methodology : methodologyList) {
            List<IObjectData> ruleList = detailRuleMap.get(methodology.getId());
            if (CollectionUtils.isEmpty(ruleList)) {
                continue;
            }
            // 遍历明细数据
            for (IObjectData detail : ruleList) {
                boolean isMatch = canMatch(objectApiName, objectId, user, detail, objectData, methodology);
                if (isMatch) {
                    return methodology;
                }
            }
        }
        return null;
    }

    private boolean canMatch(String objectApiName, String objectId, User user, IObjectData detailRule,
                             IObjectData objectData, IObjectData methodology) {
        boolean isMatch = false;
        List<RuleWhere> ruleWheres = RuleWhereUtil.getRuleWhereListByUseRange(detailRule,
                MethodologyRuleConstants.RULE_CONTENT);
        if (CollectionUtils.isEmpty(ruleWheres)) {
            log.info("No ruleWhere data found match Methodology");
            isMatch = true;
        } else {
            List<IObjectData> actualData = queryByRuleWhere(ruleWheres, objectApiName, objectId, user);
            if (!CollectionUtils.isEmpty(actualData)) {
                isMatch = true;
            }
        }

        if (isMatch) {
            String preObjectApiName = detailRule.get(MethodologyRuleConstants.PRE_OBJECT_API_NAME, String.class);
            if (StringUtils.isBlank(preObjectApiName)) {
                isMatch = true;
            } else {
                String preObjectFieldApiName = detailRule.get(MethodologyRuleConstants.PRE_FIELD_API_NAME,
                        String.class);
                String preObjectId = objectData.get(preObjectFieldApiName, String.class);
                if (StringUtils.isBlank(preObjectId)) {
                    isMatch = false;
                    return isMatch;
                }

                SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus();
                if (preObjectApiName.equals(FeatureConstants.LEADS_OBJ)) {
                    searchTemplateQueryPlus.addFilter(MethodologyInstanceConstants.LEAD_ID, Operator.EQ, preObjectId);
                } else if (preObjectApiName.equals(FeatureConstants.ACCOUNT_OBJ)) {
                    searchTemplateQueryPlus.addFilter(MethodologyInstanceConstants.ACCOUNT_ID, Operator.EQ,
                            preObjectId);
                } else if (preObjectApiName.equals(FeatureConstants.NEW_OPPORTUNITY_OBJ)) {
                    searchTemplateQueryPlus.addFilter(MethodologyInstanceConstants.OPPORTUNITY_ID, Operator.EQ,
                            preObjectId);
                }
                searchTemplateQueryPlus.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");
                searchTemplateQueryPlus.addFilter(MethodologyInstanceConstants.METHODOLOGY_ID, Operator.EQ,
                        methodology.getId());
                searchTemplateQueryPlus.addFilter(MethodologyInstanceConstants.STATUS,
                        Operator.EQ, MethodologyInstanceConstants.StatusType.ENABLE.getStatusType());

                searchTemplateQueryPlus.setLimit(1);
                searchTemplateQueryPlus.setSearchSource("db");

                List<IObjectData> preObjectDataList = Optional
                        .ofNullable(serviceFacade.findBySearchQueryIgnoreAll(
                                user,
                                MethodologyInstanceConstants.API_NAME,
                                searchTemplateQueryPlus))
                        .map(QueryResult::getData)
                        .orElse(Lists.newArrayList());

                if (!CollectionUtils.isEmpty(preObjectDataList)) {
                    IObjectData tempObjectData = preObjectDataList.get(0);
                    String tempId = null;
                    if (FeatureConstants.NEW_OPPORTUNITY_OBJ.equals(objectApiName)) {
                        tempId = tempObjectData.get(MethodologyInstanceConstants.OPPORTUNITY_ID, String.class);
                    } else if (FeatureConstants.LEADS_OBJ.equals(objectApiName)) {
                        tempId = tempObjectData.get(MethodologyInstanceConstants.LEAD_ID, String.class);
                    } else if (FeatureConstants.ACCOUNT_OBJ.equals(objectApiName)) {
                        tempId = tempObjectData.get(MethodologyInstanceConstants.ACCOUNT_ID, String.class);
                    }
                    if (StringUtils.isBlank(tempId) || objectId.equals(tempId)) {
                        methodology.set(MethodologyConstants.TEMP_INSTANCE, tempObjectData);
                    } else {
                        isMatch = false;
                    }
                } else {
                    isMatch = false;
                }
            }
        }

        return isMatch;
    }

    /**
     * 匹配方法论
     */
    public List<IObjectData> matchMethodology(String objectApiName, String objectId, User user,
                                              List<IObjectData> methodologyList, List<IObjectData> detailRuleList) {
        List<IObjectData> matchMethodologyList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(methodologyList) || CollectionUtils.isEmpty(detailRuleList)) {
            return matchMethodologyList;
        }

        Map<String, IObjectData> methodologyMap = methodologyList.stream()
                .collect(Collectors.toMap(
                        IObjectData::getId,
                        methodology -> methodology,
                        (existing, replacement) -> replacement));

        // 遍历明细数据
        for (IObjectData detail : detailRuleList) {
            String methodologyId = detail.get(MethodologyRuleConstants.METHODOLOGY_ID, String.class);
            IObjectData methodology = methodologyMap.get(methodologyId);
            if (methodology == null) {
                continue;
            }

            List<RuleWhere> ruleWheres = RuleWhereUtil.getRuleWhereListByUseRange(detail,
                    MethodologyRuleConstants.RULE_CONTENT);
            if (CollectionUtils.isEmpty(ruleWheres)) {
                matchMethodologyList.add(methodology);
            } else {
                List<IObjectData> actualData = queryByRuleWhere(ruleWheres, objectApiName, objectId, user);
                if (!CollectionUtils.isEmpty(actualData)) {
                    matchMethodologyList.add(methodology);
                }
            }

        }
        return matchMethodologyList;
    }

    /**
     * 根据规则匹配查询数据
     */
    public List<IObjectData> queryByRuleWhere(List<RuleWhere> ruleWheres, String objectApiName, String objectId,
                                              User user) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus();
        searchTemplateQueryPlus.setWheres(RuleWhere.transformRuleWheres(ruleWheres));
        searchTemplateQueryPlus.addFilter(DBRecord.ID, Operator.EQ, objectId);
        searchTemplateQueryPlus.setLimit(1);

        return Optional.ofNullable(serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                        user,
                        objectApiName,
                        searchTemplateQueryPlus,
                        Lists.newArrayList(DBRecord.ID)))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }

    /**
     * 查询明细列表
     */
    public List<IObjectData> queryRuleList(User user, String objectApiName) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, IS_DELETED_VALUE) // 删除状态 = 正常
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(MethodologyRuleConstants.OBJECT_API_NAME, Operator.EQ, objectApiName);

        searchTemplateQueryPlus.setLimit(MAX_METHODOLOGY_LIMIT);
        searchTemplateQueryPlus.setOffset(0);
        searchTemplateQueryPlus.setSearchSource(SEARCH_SOURCE_DB);

        return Optional.ofNullable(serviceFacade.findBySearchQueryIgnoreAll(
                        user,
                        MethodologyRuleConstants.API_NAME,
                        searchTemplateQueryPlus))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }

    /**
     * 查询所有已发布的方法论对象
     *
     * @param user 用户信息
     * @return 方法论对象列表
     */
    public List<IObjectData> queryAllMethodology(User user) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, "0")
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(MethodologyConstants.STATUS, Operator.EQ, MethodologyConstants.STATUS_PUBLISHED);

        searchTemplateQueryPlus.setLimit(MAX_METHODOLOGY_LIMIT);
        searchTemplateQueryPlus.setOffset(0);
        searchTemplateQueryPlus.setOrders(Lists.newArrayList(new OrderBy(MethodologyConstants.PRIORITY, true)));

        List<String> queryFieldList = Lists.newArrayList(
                DBRecord.ID,
                MethodologyConstants.TYPE,
                MethodologyConstants.STAGE_LEVEL,
                MethodologyConstants.REMARK,
                MethodologyConstants.METHODOLOGY_IDS,
                MethodologyConstants.PRIORITY);

        return Optional.ofNullable(serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                        user,
                        MethodologyConstants.API_NAME,
                        searchTemplateQueryPlus,
                        queryFieldList))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }
}