package com.facishare.crm.task.sfa.activitysummary.enums;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/25 11:06
 * @description:
 */
public enum ActivitySummaryTypeEnum {
    // business, follow,customer_group
    BUSINESS("business", "业务动态"),
    FOLLOW("follow", "跟进动态"),
    CUSTOMER_GROUP("customer_group", "客户群动态");

    private String type;
    private String desc;

    ActivitySummaryTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
