package com.facishare.crm.task.sfa.bizfeature.constant;

/**
 * 任务特征关联常量
 *
 * <AUTHOR>
 */
public interface TaskFeatureConstants {
	/**
     * 任务
     */
	String TASK_ID = "task_id";
	/**
     * 特征
     */
	String FEATURE_ID = "feature_id";
	/**
     * 权重
     */
	String WEIGHT = "weight";
	/**
     * 是否必做
     */
	String MUST_DO = "must_do";
	/**
     * 评分规则
     */
	String SCORING_RULE_ID = "scoring_rule_id";
	/**
     * 状态
     */
	String STATUS = "status";
     enum StatusType {
		/**
         * 禁用
         */
		DISABLE("0") ,
		/**
         * 启用
         */
		ENABLE("1") ;
		private final String status;

		public String getStatusType() {
            return status;
        }


		StatusType(String status) {
            this.status = status;
        }
	}
	/**
     * 负责人所在部门
     */
	String OWNER_DEPARTMENT = "owner_department";
	/**
     * 相关团队
     */
	String RELEVANT_TEAM = "relevant_team";
     /**
      * 方法论
      */
	String METHODOLOGY_ID = "methodology_id";
     /**
      * 特征对象
      */
     String FEATURE_OBJECT_API_NAME = "feature_object_api_name";
     /**
      * 节点对象
      */
     String NODE_OBJECT_API_NAME = "node_object_api_name";
     /**
      * 关联字段
      */
     String RELATED_FIELD = "related_field";

	/**
	 * 关联对象
	 */
	String RELATED_OBJECT_API_NAME = "related_object_api_name";
}