package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.activitysummary.constant.AttendeesInsightConstants;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.CompletionsService;
import com.facishare.crm.task.sfa.activitysummary.service.FixJSONFormatService;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.crm.task.sfa.model.AiRestProxyModel;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractInsightHandler<T extends AttendeesInsightModel.InsightResult> implements InsightHandler {

    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected CompletionsService completionsService;
    @Autowired
    protected FixJSONFormatService fixJSONFormatService;

    @Override
    public void insight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        if (existsInsightTypeData(attendeesInsightMessage)) {
            log.warn("Insight type data already exists, skipping insight. tenantId:{}, activeRecordId:{}", attendeesInsightMessage.getTenantId(), attendeesInsightMessage.getActiveRecordId());
            return;
        }
        doInsight(attendeesInsightMessage);
    }

    private boolean existsInsightTypeData(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        String tenantId = attendeesInsightMessage.getTenantId();
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), AttendeesInsightConstants.ACTIVE_RECORD_ID, attendeesInsightMessage.getActiveRecordId());
        SearchUtil.fillFilterEq(query.getFilters(), AttendeesInsightConstants.INSIGHT_TYPE, getInsightType());
        if (Safes.isNotEmpty(attendeesInsightMessage.getActivityUserId())) {
            SearchUtil.fillFilterEq(query.getFilters(), AttendeesInsightConstants.ACTIVITY_USER_ID, attendeesInsightMessage.getActivityUserId());
        }
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        query.setLimit(1);
        List<IObjectData> exist = serviceFacade.findBySearchQuery(User.systemUser(tenantId), AttendeesInsightConstants.INSIGHT_RECORD_API_NAME, query).getData();
        return !exist.isEmpty();
    }

    protected IObjectData buildBaseInsightRecord(String tenantId, String activeRecordId) {
        IObjectData insightRecord = new ObjectData();
        insightRecord.setTenantId(tenantId);
        insightRecord.setDescribeApiName(AttendeesInsightConstants.INSIGHT_RECORD_API_NAME);
        insightRecord.setOwner(Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
        insightRecord.set(AttendeesInsightConstants.ACTIVE_RECORD_ID, activeRecordId);
        insightRecord.set(AttendeesInsightConstants.INSIGHT_TYPE, getInsightType());
        return insightRecord;
    }

    protected String getUserNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        return "";
    }

    protected String getTheirSideNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        List<IObjectData> activityUserList = insightMessage.getExtendData().getActivityUserList();
        if (Safes.isNotEmpty(insightMessage.getActivityUserId())) {
            activityUserList = activityUserList.stream().filter(d -> d.getId().equals(insightMessage.getActivityUserId())).collect(Collectors.toList());
        }
        return activityUserList.stream()
                .filter(d -> "their_side".equals(d.get("participant_types", String.class)))
                .map(d -> d.get("user_name", String.class)).collect(Collectors.joining("、"));
    }

    protected String getOurSideNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        List<IObjectData> activityUserList = insightMessage.getExtendData().getActivityUserList();
        if (Safes.isNotEmpty(insightMessage.getActivityUserId())) {
            activityUserList = activityUserList.stream().filter(d -> d.getId().equals(insightMessage.getActivityUserId())).collect(Collectors.toList());
        }
        return activityUserList.stream()
                .filter(d -> "our_side".equals(d.get("participant_types", String.class)))
                .map(d -> d.get("user_name", String.class)).collect(Collectors.joining("、"));
    }

    protected List<IObjectData> insightByOriginCorpus(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, String prompt) {
        StopWatch stopWatch = new StopWatch("insightByOriginCorpus_" + getInsightType());
        String tenantId = attendeesInsightMessage.getTenantId();
        String completionRst = completion(attendeesInsightMessage, prompt);
        completionRst = fixJSONFormatService.fixJSON(completionRst, true);
        List<T> insightResultList = fixJSONFormatService.getDataListFixedInvalidJSON(User.systemUser(tenantId), "", completionRst, getInsightResultClass(), true);
        stopWatch.lap("completions");
        if (Safes.isEmpty(insightResultList)) {
            log.warn("insightResultList is empty, tenantId:{}, activeRecordId:{}, prompt:{}, completionRst:{}", tenantId, attendeesInsightMessage.getActiveRecordId(), prompt, completionRst);
            return Collections.emptyList();
        }

        insightResultList = mergeByUserName(insightResultList);
        List<IObjectData> activityUserList = attendeesInsightMessage.getExtendData().getActivityUserList();
        Map<String, String> userNameMap = getUserNameMap(activityUserList);
        List<IObjectData> addRecordList = Lists.newArrayList();
        for (T insightResult : Safes.of(insightResultList)) {
            IObjectData insightRecord = buildBaseInsightRecord(tenantId, attendeesInsightMessage.getActiveRecordId());
            String userId = userNameMap.get(insightResult.getUserName());
            insightRecord.set(AttendeesInsightConstants.ACTIVITY_USER_ID, userId);
            fillDataByOriginRst(insightRecord, insightResult);
            addRecordList.add(insightRecord);
        }

        List<IObjectData> dataList = serviceFacade.bulkSaveObjectData(addRecordList, User.systemUser(tenantId));
        stopWatch.lap("bulkSaveObjectData");
        stopWatch.logSlow(10, TimeUnit.SECONDS);
        return dataList;
    }

    /**
     * 获取InsightResult的具体Class类型
     */
    @SuppressWarnings("unchecked")
    protected Class<T> getInsightResultClass() {
        return (Class<T>) AttendeesInsightModel.InsightResult.class;
    }

    /**
     * 将InsightResult转换为IObjectData
     * @param insightRecord 目标IObjectData对象
     * @param insightResult 源InsightResult对象
     */
    protected void fillDataByOriginRst(IObjectData insightRecord, T insightResult) {
        insightRecord.set(AttendeesInsightConstants.INSIGHT_RESULT, JSON.toJSONString(insightResult.getInsightList()));
    }

    /**
     * 当AI返回的数据有某个人返回了多个InsightResult时进行合并处理
     */
    protected List<T> mergeByUserName(List<T> insightResultList) {
        Map<String, T> insightResultMap = Maps.newHashMap();
        for (T insightResult : Safes.of(insightResultList)) {
            String userName = insightResult.getUserName();
            if (insightResultMap.containsKey(userName)) {
                T existingResult = insightResultMap.get(userName);
                Safes.of(existingResult.getInsightList()).addAll(insightResult.getInsightList());
            } else {
                insightResultMap.put(userName, insightResult);
            }
        }

        return Lists.newArrayList(insightResultMap.values());
    }

    protected void insightByQuestionCorpus(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, String prompt) {
        StopWatch stopWatch = new StopWatch("insightByQuestionCorpus_" + getInsightType());
        String tenantId = attendeesInsightMessage.getTenantId();
        String completionRst = completion(attendeesInsightMessage, prompt);
        completionRst = fixJSONFormatService.fixJSON(completionRst, true);
        List<T> insightResultList = fixJSONFormatService.getDataListFixedInvalidJSON(User.systemUser(tenantId), "", completionRst, getInsightResultClass(), true);
        stopWatch.lap("completions");

        List<IObjectData> activityUserList = attendeesInsightMessage.getExtendData().getActivityUserList();
        List<IObjectData> questionList = attendeesInsightMessage.getExtendData().getQuestionList();
        Map<String, String> userNameMap = getUserNameMap(activityUserList);

        List<IObjectData> addRecordList = Lists.newArrayList();
        insightResultList = mergeByUserName(insightResultList);
        for (AttendeesInsightModel.InsightResult insightResult : Safes.of(insightResultList)) {
            IObjectData insightRecord = buildBaseInsightRecord(tenantId, attendeesInsightMessage.getActiveRecordId());
            for (AttendeesInsightModel.Insight insight : insightResult.getInsightList()) {
                Integer questionSeq = insight.getQuestionSeq();
                if (questionSeq == null) {
                    continue;
                }
                IObjectData question = questionList.get(questionSeq);
                String answerSeq = question.get("answer_seq", String.class);
                if (Safes.isNotEmpty(answerSeq)) {
                    insight.setQuotaSeqList(JSON.parseArray(answerSeq, String.class));
                }
            }
            String userId = userNameMap.get(insightResult.getUserName());
            insightRecord.set(AttendeesInsightConstants.ACTIVITY_USER_ID, userId);
            insightRecord.set(AttendeesInsightConstants.INSIGHT_RESULT, JSON.toJSONString(insightResult.getInsightList()));
            addRecordList.add(insightRecord);
        }
        if (!addRecordList.isEmpty()) {
            serviceFacade.bulkSaveObjectData(addRecordList, User.systemUser(tenantId));
        }
        stopWatch.lap("bulkSaveObjectData");
        stopWatch.logSlow(10, TimeUnit.SECONDS);
    }


    protected String completion(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, String prompt) {
        AttendeesInsightModel.AttendeesInsightExtendData extendData = attendeesInsightMessage.getExtendData();
        String userNames = getUserNames(attendeesInsightMessage);
        String corpusOriginText = getCorpus(extendData);
        if (Safes.isEmpty(userNames) || Safes.isEmpty(corpusOriginText)) {
            log.warn("userNames or corpusOriginText is empty, userNames:{}, prompt:{}", userNames, prompt);
            return "";
        }

        Map<String, Object> sceneVariables = Maps.newHashMap();
        sceneVariables.put("userNames", userNames);
        sceneVariables.put("corpus", corpusOriginText);
        addSceneVariables(attendeesInsightMessage, sceneVariables);

        AiRestProxyModel.Arg arg = AiRestProxyModel.Arg.builder()
                .apiName(prompt)
                .bingObjectDataId(attendeesInsightMessage.getActiveRecordId())
                .sceneVariables(sceneVariables)
                .build();

        return completionsService.requestCompletion(User.systemUser(attendeesInsightMessage.getTenantId()), arg);
    }

    protected void addSceneVariables(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage,
                                     Map<String, Object> sceneVariables) {
    }

    protected String getCorpus(AttendeesInsightModel.AttendeesInsightExtendData extendData) {
        return extendData.getCorpusOriginText();
    }

    protected Map<String, String> getUserNameMap(List<IObjectData> activityUserList) {
        return activityUserList.stream().collect(Collectors.toMap(
                d -> d.get("user_name", String.class), IObjectData::getId, (a, b) -> a
        ));
    }

    protected String getQuestionCorpus(AttendeesInsightModel.AttendeesInsightExtendData extendData) {
        List<IObjectData> questionList = extendData.getQuestionList();
        Map<String, String> userIdNameMap = extendData.getUserIdNameMap();
        return getQuestionCorpus(questionList, userIdNameMap);
    }

    private String getQuestionCorpus(List<IObjectData> questionList, Map<String, String> userIdNameMap) {
        StringBuilder sb = new StringBuilder();

        int seq = 0;
        for (IObjectData question : questionList) {
            String questionContent = question.get("question_content__o", String.class);
            String answerSummary = question.get("answer_summary__o", String.class);
            List<String> questionUserIds = question.get("question_user", List.class);
            List<String> answerUserIds = question.get("answer_user", List.class);

            if (Safes.isEmpty(questionUserIds) || Safes.isEmpty(answerUserIds)) {
                log.warn("questionUserIds or answerUserIds is empty, question id:{}", question.getId());
                continue;
            }

            String questionUserNames = questionUserIds.stream().map(userIdNameMap::get).filter(Safes::isNotEmpty).collect(Collectors.joining("、"));
            String answerUserNames = answerUserIds.stream().map(userIdNameMap::get).filter(Safes::isNotEmpty).collect(Collectors.joining("、"));
            if (Safes.isEmpty(questionUserNames) || Safes.isEmpty(answerUserNames)) {
                log.warn("questionUserNames or answerUserNames is empty, question id:{}", question.getId());
                continue;
            }

            sb.append(seq).append(".").append(questionUserNames).append(" 问题：").append(questionContent).append("\n"); // ignoreI18n
            sb.append(seq).append(".").append(answerUserNames).append(" 回答：").append(answerSummary).append("\n"); // ignoreI18n

            seq++;
        }
        return sb.toString();
    }

    protected void addMeetingObjectives(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, Map<String, Object> sceneVariables) {
        IObjectData activeRecord = attendeesInsightMessage.getExtendData().getActiveRecord();
        String meetingObjectives = activeRecord.get("meeting_objectives", String.class);
        meetingObjectives = Optional.ofNullable(meetingObjectives).orElse("暂无会议目标");// ignoreI18n
        sceneVariables.put("meetingObjectives", meetingObjectives);
    }

    protected void featureInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage, String prompt) {
        StopWatch stopWatch = StopWatch.createStarted("featureInsight_" + getInsightType());
        String completion = completion(attendeesInsightMessage, prompt);
        List<AttendeesInsightModel.FeatureInsightResult> featureInsightResultList = fixJSONFormatService.getDataListFixedInvalidJSON(User.systemUser(attendeesInsightMessage.getTenantId()), "", completion, AttendeesInsightModel.FeatureInsightResult.class, false);
        stopWatch.lap("completions");
        if (Safes.isEmpty(featureInsightResultList)) {
            log.warn("featureInsightResultList is empty, tenantId:{}, activeRecordId:{}, prompt:{}, completion:{}", attendeesInsightMessage.getTenantId(), attendeesInsightMessage.getActiveRecordId(), prompt, completion);
            return;
        }
        List<IObjectData> activityUserList = attendeesInsightMessage.getExtendData().getActivityUserList();
        if (Safes.isNotEmpty(getParticipantTypes())) {
            // 只处理他方/我方参会人，以防AI返回了他方+我方混乱的情况
            activityUserList = activityUserList.stream().filter(d -> getParticipantTypes().equals(d.get("participant_types", String.class))).collect(Collectors.toList());
        }
        Map<String, IObjectData> userMap = activityUserList.stream().collect(Collectors.toMap(IObjectData::getName, Function.identity()));

        List<IObjectData> updateList = Lists.newArrayList();
        for (AttendeesInsightModel.FeatureInsightResult featureInsightResult : featureInsightResultList) {
            String userName = featureInsightResult.getUserName();
            IObjectData activityUser = userMap.get(userName);
            if (activityUser != null && Safes.isNotEmpty(featureInsightResult.getInsightText())) {
                activityUser.set(getFeatureFieldName(), featureInsightResult.getInsightText());
                updateList.add(activityUser);
            }
        }

        if (!updateList.isEmpty()) {
            serviceFacade.batchUpdateByFields(User.systemUser(attendeesInsightMessage.getTenantId()), updateList, Lists.newArrayList(getFeatureFieldName()));
            log.info("featureInsight: {} updateList size:{}", getInsightType(), updateList.size());
        }
        stopWatch.logSlow(10, TimeUnit.SECONDS);
    }

    // 用于过滤参会人角色方
    protected String getParticipantTypes() {
        return null;
    }

    protected String getFeatureFieldName() {
        throw new IllegalArgumentException("getFeatureFieldName() must be implemented");
    }
}
