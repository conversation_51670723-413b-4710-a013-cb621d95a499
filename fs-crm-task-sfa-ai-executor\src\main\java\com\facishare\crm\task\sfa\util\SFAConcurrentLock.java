package com.facishare.crm.task.sfa.util;

import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SFAConcurrentLock {

	@Autowired
	@Qualifier("SFAJedisCmd")
	private MergeJedisCmd mergeJedisCmd;


	public void runWithinWaitTryLock(String lockKey, int lockTime, Runnable runnable) {
		try (SFAJedisLock lock = new SFAJedisLock(mergeJedisCmd, lockKey, lockTime)) {
			if (!lock.waitTryLock(3000)) {
				log.error("tryLock failed! key:{}", lockKey);
				return;
			}
			runnable.run();
		} catch (Exception e){
			throw new RuntimeException(e);
		}
	}

	public void runIfGetLock(String lockKey, int lockTime, Runnable runnable) {
		try (SFAJedisLock lock = new SFAJedisLock(mergeJedisCmd, lockKey, lockTime)) {
			if (!lock.tryLock()) {
				log.warn("tryLock failed! key:{}", lockKey);
				return;
			}
			runnable.run();
		} catch (Exception e){
			throw new RuntimeException(e);
		}
	}
}
