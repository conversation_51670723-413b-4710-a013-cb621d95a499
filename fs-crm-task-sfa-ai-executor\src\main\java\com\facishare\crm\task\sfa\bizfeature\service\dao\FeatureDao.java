package com.facishare.crm.task.sfa.bizfeature.service.dao;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据访问类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FeatureDao {

    @Autowired
    private ServiceFacade serviceFacade;
    /**
     * 查询特征by dimension
     */
    public List<IObjectData> fetchFeaturesByDimensionId(User user, String dimensionId) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, FeatureConstants.FEATURE_DIMENSION_1, dimensionId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, FeatureConstants.STATUS, FeatureConstants.StatusType.ENABLED.getStatusType());
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.FEATURE, query).getData();
    }
    public List<IObjectData> fetchFeaturesByDimensionIds(User user, List<String> dimensionIds) {
        SearchTemplateQueryPlus query = SearchUtil.buildBaseSearchQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, FeatureConstants.FEATURE_DIMENSION_1, dimensionIds);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, FeatureConstants.STATUS, FeatureConstants.StatusType.ENABLED.getStatusType());
        return serviceFacade.findBySearchQueryIgnoreAll(user, FeatureConstants.FEATURE, query).getData();
    }

    public  List<IObjectData> fetchScheduledFeature(User user){
        SearchTemplateQueryPlus searchTemplateQueryPlus = SearchUtil.buildBaseSearchQuery();
        searchTemplateQueryPlus.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");
        searchTemplateQueryPlus.addFilter(FeatureConstants.UPDATE_TYPE, Operator.HASANYOF, FeatureConstants.UpdateType.SCHEDULED.getUpdateType());
        searchTemplateQueryPlus.addFilter(FeatureConstants.STATUS, Operator.EQ, FeatureConstants.StatusType.ENABLED.getStatusType());

        return serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.FEATURE, searchTemplateQueryPlus).getData();
    }
}
