package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureTagRuleConstants;
import com.facishare.crm.task.sfa.bizfeature.model.ObjectReferenceFieldDescribeBuilder;
import com.facishare.crm.task.sfa.service.impl.EnterpriseInitService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TagRuleInitService {

    @Resource
    private ObjectDescribeServiceImpl objectDescribeService;
    @Resource
    private EnterpriseInitService enterpriseInitService;
    @Resource
    private FeatureInitService featureInitService;
    @Resource
    private ServiceFacade serviceFacade;

    /**
     * 初始化标注规则描述和数据
     *
     * @param user 用户信息
     * @throws MetadataServiceException 元数据服务异常
     * @throws IOException              IO异常
     */
    public void initTagRuleBaseDataByApiNames(User user) throws MetadataServiceException, IOException {
        String[] initApiNames = {FeatureTagRuleConstants.OBJECT_API_NAME};

        // 1. 先刷新描述
        initDescribe(user, initApiNames);
        log.info("initDescribe for FeatureTagRuleObj finish");

        // 2. 再刷新数据
        featureInitService.initBaseDataByApiNames(user, initApiNames);
        log.info("initBaseDataByApiNames for FeatureTagRuleObj finish");

        // 3. 检查并添加FeatureObj的tag_id字段
        ensureFeatureObjTagIdField(user);
        log.info("ensureFeatureObjTagIdField finish");

        // 4. 更新FeatureObj数据，将data_source_third赋值给tag_id
        updateFeatureObjTagIdData(user);
        log.info("updateFeatureObjTagIdData finish");
    }

    /**
     * 初始化描述信息
     *
     * @param user         用户信息
     * @param initApiNames API名称数组
     * @throws MetadataServiceException 元数据服务异常
     */
    private void initDescribe(User user, String[] initApiNames) throws MetadataServiceException {
        List<IObjectDescribe> describeList = objectDescribeService.findDescribeListByApiNames(user.getTenantId(), Lists.newArrayList(initApiNames));
        Map<String, IObjectDescribe> describeMap = describeList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, o -> o));

        for (String apiName : initApiNames) {
            IObjectDescribe describe = describeMap.get(apiName);
            if (describe == null) {
                enterpriseInitService.initDescribeForTenant(user.getTenantId(), apiName);
            }
        }
    }

    /**
     * 确保FeatureObj对象有tag_id字段，如果没有则添加
     *
     * @param user 用户信息
     * @throws MetadataServiceException 元数据服务异常
     */
    private void ensureFeatureObjTagIdField(User user) throws MetadataServiceException {
        String featureObjApiName = "FeatureObj";
        List<IObjectDescribe> describeList = objectDescribeService.findDescribeListByApiNames(user.getTenantId(), Lists.newArrayList(featureObjApiName));

        if (describeList == null || describeList.isEmpty()) {
            log.warn("FeatureObj describe not found for tenant: {}", user.getTenantId());
            return;
        }
        IObjectDescribe featureObjDescribe = describeList.get(0);
        IFieldDescribe tagId = featureObjDescribe.getFieldDescribe("tag_id");
        if (tagId != null && Boolean.TRUE.equals(tagId.isActive())) {
            log.info("tag_id field already exists in FeatureObj");
            return;
        }

        // 添加tag_id字段到FeatureObj
        addTagIdFieldToFeatureObj(user, featureObjDescribe);
    }

    /**
     * 更新FeatureObj数据，将data_source_third的值赋值给tag_id
     *
     * @param user 用户信息
     * @throws IOException IO异常
     */
    private void updateFeatureObjTagIdData(User user) throws IOException {
        String featureObjApiName = "FeatureObj";

        // 查询data_source_type=tag的FeatureObj数据
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(2000);
        searchQuery.setOffset(0);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);

        Filter filter = new Filter();
        filter.setFieldName("data_source_type");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("tag"));
        searchQuery.setFilters(Lists.newArrayList(filter));

        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, featureObjApiName, searchQuery);

        if (queryResult == null || queryResult.getData() == null || queryResult.getData().isEmpty()) {
            log.info("No FeatureObj data found with data_source_type=tag");
            return;
        }

        List<IObjectData> updateList = Lists.newArrayList();

        for (IObjectData featureObj : queryResult.getData()) {
            String dataSourceThird = featureObj.get("data_source_third", String.class);
            if (dataSourceThird != null && !dataSourceThird.trim().isEmpty()) {
                // 只有当data_source_third有值时才更新
                featureObj.set("tag_id", dataSourceThird);
                updateList.add(featureObj);
                log.debug("Updating FeatureObj id={}, setting tag_id={}", featureObj.getId(), dataSourceThird);
            }
        }

        if (!updateList.isEmpty()) {
            serviceFacade.bulkUpsertObjectData(updateList, user);
            log.info("Successfully updated {} FeatureObj records with tag_id from data_source_third", updateList.size());
        } else {
            log.info("No FeatureObj records need to be updated");
        }
    }

    /**
     * 添加tag_id字段到FeatureObj对象
     *
     * @param user 用户信息
     * @param featureObjDescribe FeatureObj对象描述
     */
    private void addTagIdFieldToFeatureObj(User user, IObjectDescribe featureObjDescribe) {
        ObjectReferenceFieldDescribe tagIdField = ObjectReferenceFieldDescribeBuilder.builder()
                .apiName("tag_id")
                .label("标注规则")
                .targetApiName("FeatureTagRuleObj")
                .targetRelatedListLabel("特征")
                .targetRelatedListName("feature_tag_rule_feature_list")
                .unique(false)
                .required(false)
                .build();
        
        try {
            objectDescribeService.addCustomFieldDescribe(featureObjDescribe, Lists.newArrayList(tagIdField));
            log.info("Successfully added tag_id field to FeatureObj");
        } catch (Exception e) {
            log.error("TagRuleInitService#addTagIdFieldToFeatureObj error, tenant:{}", user.getTenantId(), e);
        }
    }
}
