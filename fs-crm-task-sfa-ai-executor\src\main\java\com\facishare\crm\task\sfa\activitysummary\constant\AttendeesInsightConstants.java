package com.facishare.crm.task.sfa.activitysummary.constant;

public interface AttendeesInsightConstants {

    String ATTITUDE = "attitude";
    String ACTIVE_RECORD_ID = "active_record_id";
    String ACTIVITY_USER_ID = "activity_user_id";
    String SEGMENT_ID = "segment_id";
    String QUOTA_SEQ = "quota_seq";
    String INSIGHT_TYPE = "insight_type";
    String INSIGHT_RESULT = "insight_result";
    String SOP_COVERAGE_RATE = "sop_coverage_rate";

    String INSIGHT_RECORD_API_NAME = "AttendeeInsightRecordObj";



    String FOCUS_BUDGET = "focus_budget"; // 预算关注点
    String FOCUS_DECISION_MAKER = "focus_decision_maker"; // 决策者关注点
    String FOCUS_REQUIREMENT = "focus_requirement"; // 需求关注点
    String FOCUS_TIME = "focus_time"; // 时间关注点


    /**
        决策风格	果断型	犹豫型
        沟通偏好	直接型	间接型
        风险偏好	风险回避型	风险偏好型
        合作导向	关系型	任务型
        专业驱动	专家型	新手型
        驱动模式	感性型	理性型
     */
    String DECISION_STYLE = "decision_style"; //决策风格
    String COMMUNICATION_PREFERENCE = "communication_preference"; //沟通偏好
    String RISK_PREFERENCE = "risk_preference"; //风险偏好
    String COOPERATION_ORIENTATION = "cooperation_orientation"; //合作导向
    String PROFESSIONAL_DRIVE = "professional_drive"; //专业驱动
    String MOTIVATION_PATTERN = "motivation_pattern"; //驱动模式

    String DECISIVE = "decisive"; //果断型
    String HESITANT = "hesitant"; //犹豫型
    String DIRECT = "direct"; //直接型
    String INDIRECT = "indirect"; //间接型
    String AVOIDANCE = "avoidance"; //风险回避型
    String PREFERENCE = "preference"; //风险偏好型
    String RELATION = "relation"; //关系型
    String TASK = "task"; //任务型
    String EXPERT = "expert"; //专家型
    String NEWBIE = "newbie"; //新手型
    String SENSITIVE = "sensitive"; //感性型
    String PATTERN_RATIONAL = "rational"; //理性型

}
