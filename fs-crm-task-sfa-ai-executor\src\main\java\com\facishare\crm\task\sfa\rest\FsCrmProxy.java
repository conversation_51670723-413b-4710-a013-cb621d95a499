package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.task.sfa.rest.dto.KnowledgeModel;
import com.facishare.crm.task.sfa.rest.dto.RoleModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

/**
 *PAAS角色
 */
@RestResource(value = "FS_CRM", desc = "fs crm 相关服务", contentType = "application/json")
public interface FsCrmProxy {
    @POST(value = "/knowledge_category/service/add", desc = "增加知识库分类")
    KnowledgeModel.AddKnowledgeCategoryResult addKnowledgeCategory(@Body KnowledgeModel.AddKnowledgeCategoryArg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/knowledge_category/service/list_new", desc = "获取知识库分类")
    KnowledgeModel.ListKnowledgeCategoryResult list(@Body KnowledgeModel.ListKnowledgeCategoryArg arg, @HeaderMap Map<String, String> header);
}
