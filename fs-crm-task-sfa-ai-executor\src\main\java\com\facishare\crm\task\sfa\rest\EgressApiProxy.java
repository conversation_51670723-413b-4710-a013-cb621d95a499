package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.task.sfa.model.proxy.EgressGeoEncodeModel;
import com.facishare.crm.task.sfa.rest.dto.EgressApiModels;
import com.facishare.rest.core.annotation.*;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.facishare.rest.core.util.JsonUtil;
import org.apache.http.HttpStatus;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.nio.charset.StandardCharsets.UTF_8;

@RestResource(value = "EGRESS_API_SERVICE", desc = "egress新地址", contentType = "application/json",
			codec = "com.facishare.crm.task.sfa.rest.EgressApiProxy$EgressCodec")
public interface EgressApiProxy {


	@GET(value = "/egress-api-service/api/v2/geocode/encode?address={address}", desc = "获取经纬度")
	EgressGeoEncodeModel.Result geoEncode(@HeaderMap Map<String, String> headers, @PathParams Map<String, String> pathParams);

	@POST(value = "/egress-api-service/api/v1/asr/rec-task", desc = "创建语音识别任务")
	EgressApiModels.AsrRecTask.Response createAsrRecTask(@HeaderMap Map<String, String> headers, @Body EgressApiModels.AsrRecTask.Request request);

	@GET(value = "/egress-api-service/api/v1/asr/rec-task?taskId={taskId}", desc = "获取语音识别任务结果")
	EgressApiModels.AsrRecTask.TaskResult getAsrRecTaskResult(@HeaderMap Map<String, String> headers, @PathParams Map<String, String> pathParams);

	class EgressCodec implements IRestCodeC {

		@Override
		public <T> byte[] encodeArg(T obj) {
			if(Objects.isNull(obj)){
				return null;
			}

			if(obj instanceof String){
				return ((String) obj).getBytes(UTF_8);
			}
			return JsonUtil.toJsonWithNull(obj).getBytes(UTF_8);
		}

		@Override
		public <T> T decodeResult(int statusCode, Map<String, List<String>> map, byte[] bytes, Class<T> clazz) {
			String bodyString = new String(bytes, UTF_8);
			// geo的这个接口解析异常会返回 http status  404。是正常的调用返回
			if(statusCode >= HttpStatus.SC_MULTIPLE_CHOICES && statusCode != HttpStatus.SC_NOT_FOUND){
				throw new RestProxyRuntimeException(statusCode,bodyString);
			}
			if(clazz==String.class){
				return (T)bodyString;
			}
			if(clazz==void.class){
				return null;
			}
			T ret = JsonUtil.fromJson(new String(bytes, UTF_8), clazz);
			return ret;
		}
	}
}

