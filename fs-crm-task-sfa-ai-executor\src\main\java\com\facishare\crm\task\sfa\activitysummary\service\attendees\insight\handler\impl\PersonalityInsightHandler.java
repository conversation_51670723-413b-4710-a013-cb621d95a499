package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl;

import com.facishare.crm.task.sfa.activitysummary.constant.AttendeesInsightConstants;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.stereotype.Component;

@Component
public class PersonalityInsightHandler extends AbstractInsightHandler<AttendeesInsightModel.InsightResult> {

    private static final String PROMPT = "prompt_attendee_insight_personality";

    @Override
    public String getInsightType() {
        return "personality";
    }

    @Override
    public void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        super.insightByOriginCorpus(attendeesInsightMessage, PROMPT);
    }

    @Override
    protected String getUserNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        return super.getTheirSideNames(insightMessage);
    }


    @Override
    protected void fillDataByOriginRst(IObjectData insightRecord, AttendeesInsightModel.InsightResult insightResult) {
        super.fillDataByOriginRst(insightRecord, insightResult);


        for (AttendeesInsightModel.Insight insight : insightResult.getInsightList()) {
            if (AttendeesInsightConstants.DECISION_STYLE.equals(insight.getInsightType())) {
                insightRecord.set(AttendeesInsightConstants.DECISION_STYLE, insight.getInsightText());
            }
            if (AttendeesInsightConstants.COMMUNICATION_PREFERENCE.equals(insight.getInsightType())) {
                insightRecord.set(AttendeesInsightConstants.COMMUNICATION_PREFERENCE, insight.getInsightText());
            }
            if (AttendeesInsightConstants.RISK_PREFERENCE.equals(insight.getInsightType())) {
                insightRecord.set(AttendeesInsightConstants.RISK_PREFERENCE, insight.getInsightText());
            }
            if (AttendeesInsightConstants.COOPERATION_ORIENTATION.equals(insight.getInsightType())) {
                insightRecord.set(AttendeesInsightConstants.COOPERATION_ORIENTATION, insight.getInsightText());
            }
            if (AttendeesInsightConstants.PROFESSIONAL_DRIVE.equals(insight.getInsightType())) {
                insightRecord.set(AttendeesInsightConstants.PROFESSIONAL_DRIVE, insight.getInsightText());
            }
            if (AttendeesInsightConstants.MOTIVATION_PATTERN.equals(insight.getInsightType())) {
                insightRecord.set(AttendeesInsightConstants.MOTIVATION_PATTERN, insight.getInsightText());
            }
            
        }
    }
}
