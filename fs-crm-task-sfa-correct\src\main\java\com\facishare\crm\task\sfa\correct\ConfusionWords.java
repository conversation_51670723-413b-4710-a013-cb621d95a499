package com.facishare.crm.task.sfa.correct;

import com.hankcs.hanlp.collection.AhoCorasick.AhoCorasickDoubleArrayTrie;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public class ConfusionWords {
    @Getter
    final List<ConfusionWord> inner;
    Map<String, String> hashWord;
    AhoCorasickDoubleArrayTrie<String> trieSent;

    public ConfusionWords() {
        this(new ArrayList<>());
    }

    public ConfusionWords(List<ConfusionWord> inner) {
        this.inner = inner;
    }

    public void merge(List<ConfusionWord> dict) {
        this.inner.addAll(dict);
    }

    public void reset() {
        this.inner.clear();
        hashWord = null;
        trieSent = null;
    }

    public boolean hitWord(int sentIdx, String sent, String word, List<Mistake> mistakes) {
        if (hashWord == null) {
            hashWord = new HashMap<>();
            for (ConfusionWord conf : this.inner) {
                if (conf.level == Level.Word) {
                    hashWord.put(conf.conf, conf.word);
                }
            }
        }
        String hit = hashWord.get(word);
        if (hit != null) {
            mistakes.add(new Mistake(sentIdx, word.length() + sentIdx, sent, hit, Type.Confusion));
            return true;
        }
        return false;
    }

    public boolean hitSent(int sentIdx, String sent, List<Mistake> mistakes) {
        if (trieSent == null) {
            trieSent = new AhoCorasickDoubleArrayTrie<>();
            Map<String, String> inner = new TreeMap<>();
            for (ConfusionWord word : this.inner) {
                if (word.level == Level.Sent) {
                    inner.put(word.conf, word.word);
                }
            }
            trieSent.build(new TreeMap<>(inner));
        }
        int size = mistakes.size();
        trieSent.parseText(sent, (start, end, value)
                -> mistakes.add(new Mistake(start + sentIdx, end + sentIdx, sent, value, Type.Confusion)));
        return size < mistakes.size();
    }
}
