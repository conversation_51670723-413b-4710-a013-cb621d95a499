package com.facishare.crm.task.sfa.xxl;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.xxl.job.core.executor.XxlJobExecutor;

public class MyXxlJobExecutor extends XxlJobExecutor {

	private String configName;

	@Override
	public void start() throws Exception {
		ConfigFactory.getConfig(configName, (IConfig config) -> {
			super.setIp(config.get("xxl.job.executor.ip"));
			super.setPort(config.getInt("xxl.job.executor.port"));
			super.setAdminAddresses(config.get("xxl.job.admin.addresses"));
			super.setAppName(config.get("xxl.job.task-sfa-ai.app.name"));
			super.setAccessToken(config.get("xxl.job.accessToken"));
			super.setLogPath(config.get("xxl.job.task-sfa-ai.executor.logpath"));
		});
		super.start();
	}

	public void setConfigName(String configName) {
		this.configName = configName;
	}
}
