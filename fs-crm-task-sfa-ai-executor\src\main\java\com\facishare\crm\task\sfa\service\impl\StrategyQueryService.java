package com.facishare.crm.task.sfa.service.impl;

import com.facishare.crm.task.sfa.activitysummary.constant.InteractionStrategyConstants;
import com.facishare.crm.task.sfa.activitysummary.constant.InteractionStrategyDetailConstants;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


/**
 * 策略查询服务
 * 负责策略和策略明细的查询
 */
@Service
@Slf4j
public class StrategyQueryService {

    @Autowired
    private MetaDataFindService metaDataFindService;

    /**
     * 查询策略
     *
     * @param user 用户
     * @param strategyId 策略ID
     * @return 策略对象
     */
    public IObjectData queryStrategy(User user, String strategyId) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(DBRecord.ID, Operator.EQ, strategyId);
        searchTemplateQueryPlus.setLimit(1);
        searchTemplateQueryPlus.setOffset(0);
        searchTemplateQueryPlus.setSearchSource("db");

        List<String> queryFieldList = Lists.newArrayList(
                DBRecord.ID,
                IObjectData.NAME,
                InteractionStrategyConstants.USED_OBJECT_API_NAME,
                InteractionStrategyConstants.CONDITION);

        List<IObjectData> strategies = Optional.ofNullable(metaDataFindService.findBySearchQueryWithFieldsIgnoreAll(
                user,
                InteractionStrategyConstants.INTERACTION_STRATEGY,
                searchTemplateQueryPlus,
                queryFieldList))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());

        return CollectionUtils.isEmpty(strategies) ? null : strategies.get(0);
    }

    /**
     * 查询策略明细
     *
     * @param user 用户
     * @param strategyId 策略ID
     * @return 策略明细列表
     */
    public List<IObjectData> queryStrategyDetails(User user, String strategyId) {
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus()
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                .addFilter(ObjectLifeStatus.LIFE_STATUS_API_NAME, Operator.EQ, ObjectLifeStatus.NORMAL.getCode())
                .addFilter(InteractionStrategyDetailConstants.STRATEGY_ID, Operator.EQ, strategyId);
        searchTemplateQueryPlus.setLimit(2000);
        searchTemplateQueryPlus.setOffset(0);
        searchTemplateQueryPlus.setOrders(Lists.newArrayList(
                new OrderBy(InteractionStrategyDetailConstants.ORDER_FIELD, true)));
        searchTemplateQueryPlus.setSearchSource("db");

        List<String> queryFieldList = Lists.newArrayList(
                DBRecord.ID,
                InteractionStrategyDetailConstants.STRATEGY_ID,
                InteractionStrategyDetailConstants.LIBRARY_ID,
                InteractionStrategyDetailConstants.ORDER_FIELD);

        return Optional.ofNullable(metaDataFindService.findBySearchQueryWithFieldsIgnoreAll(
                user,
                InteractionStrategyDetailConstants.API_NAME,
                searchTemplateQueryPlus,
                queryFieldList))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }
} 