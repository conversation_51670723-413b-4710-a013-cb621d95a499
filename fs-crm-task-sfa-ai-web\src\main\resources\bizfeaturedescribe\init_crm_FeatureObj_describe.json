{"fields": {"name": {"api_name": "name", "label": "特征名称", "type": "text", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "max_length": 100, "status": "released", "is_extend": false}, "warning_message": {"is_unique": false, "type": "long_text", "is_required": false, "define_type": "package", "is_single": false, "is_extend": false, "max_length": 1000, "is_index": true, "is_active": true, "default_value": "", "label": "警告信息", "api_name": "warning_message", "is_index_field": false, "status": "released"}, "master_object_api_name": {"api_name": "master_object_api_name", "label": "特征主对象", "type": "text", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "max_length": 100, "status": "released", "is_extend": false}, "master_field_api_name": {"api_name": "master_field_api_name", "label": "特征主对象字段", "type": "text", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "max_length": 200, "status": "released", "is_extend": false}, "comparison_field": {"api_name": "comparison_field", "label": "对比字段", "type": "text", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "max_length": 100, "status": "released", "is_extend": false}, "data_source_object": {"api_name": "data_source_object", "label": "数据源对象", "type": "text", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "max_length": 100, "status": "released", "is_extend": false}, "data_source_field": {"api_name": "data_source_field", "label": "数据源字段", "type": "text", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "max_length": 100, "status": "released", "is_extend": false}, "data_source_third": {"api_name": "data_source_third", "label": "数据源外部", "type": "text", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "max_length": 100, "status": "released", "is_extend": false}, "tag_id": {"api_name": "tag_id", "label": "标注规则", "type": "object_reference", "target_api_name": "FeatureTagRuleObj", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "status": "released", "action_on_target_delete": "cascade_delete", "target_related_list_label": "特征", "target_related_list_name": "feature_tag_rule_feature_list", "is_extend": false}, "data_source_type": {"api_name": "data_source_type", "label": "数据源类型", "type": "select_one", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "options": [{"label": "内部", "value": "internal"}, {"label": "原文标注", "value": "tag"}, {"label": "Activity聚合", "value": "activity_agg"}, {"label": "外部", "value": "external"}], "define_type": "package", "status": "released", "is_extend": false}, "update_type": {"api_name": "update_type", "label": "更新方式", "type": "select_many", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "options": [{"label": "实时", "value": "real-time"}, {"label": "定时", "value": "scheduled"}], "define_type": "package", "status": "released", "is_extend": false}, "timer_info": {"api_name": "timer_info", "label": "定时设置", "type": "long_text", "expression_type": "json", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "status": "released", "is_extend": false}, "trigger_type": {"api_name": "trigger_type", "label": "触发方式", "type": "select_many", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "options": [{"label": "新建", "value": "add"}, {"label": "变更", "value": "edit"}, {"label": "消息", "value": "mq"}], "define_type": "package", "status": "released", "is_extend": false}, "status": {"api_name": "status", "label": "状态", "type": "select_one", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "options": [{"label": "禁用", "value": "0"}, {"label": "启用", "value": "1"}, {"label": "维护中", "value": "2"}], "define_type": "package", "status": "released", "is_extend": false}, "rule_id": {"api_name": "rule_id", "label": "解析规则", "type": "object_reference", "target_api_name": "ParseRuleObj", "description": "解析规则", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "status": "released", "action_on_target_delete": "cascade_delete", "target_related_list_label": "解析规则", "target_related_list_name": "parse_rule", "is_extend": false}, "feature_dimension_1": {"api_name": "feature_dimension_1", "label": "特征维度1", "type": "object_reference", "description": "特征维度", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "status": "released", "is_extend": false, "target_api_name": "FeatureDimensionObj", "target_related_list_label": "特征维度1", "target_related_list_name": "knowledge_documents", "action_on_target_delete": "cascade_delete"}, "feature_dimension_2": {"api_name": "feature_dimension_2", "label": "特征维度2", "type": "object_reference", "description": "特征维度", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "status": "released", "is_extend": false, "target_api_name": "FeatureDimensionObj", "target_related_list_label": "特征维度2", "target_related_list_name": "knowledge_documents_two", "action_on_target_delete": "cascade_delete"}, "feature_dimension_3": {"api_name": "feature_dimension_3", "label": "特征维度3", "type": "object_reference", "description": "特征维度", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "status": "released", "is_extend": false, "target_api_name": "FeatureDimensionObj", "target_related_list_label": "特征维度3", "target_related_list_name": "knowledge_documents_three", "action_on_target_delete": "cascade_delete"}, "tags": {"api_name": "tags", "label": "标注", "type": "array", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "status": "released", "is_extend": false}, "reverse_tags": {"api_name": "reverse_tags", "label": "反向标注", "type": "array", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "status": "released", "is_extend": false}, "system_type": {"api_name": "system_type", "label": "数据系统类型", "type": "select_one", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "options": [{"label": "系统", "value": "system"}, {"label": "自定义", "value": "udef"}], "define_type": "system", "status": "released"}, "switch_feature": {"api_name": "switch_feature", "label": "维度开关特征", "type": "true_or_false", "is_required": false, "is_active": true, "is_index": true, "default_value": "false", "is_unique": false, "define_type": "package", "status": "released", "is_extend": false}, "node_switch_feature": {"api_name": "node_switch_feature", "label": "节点开关特征", "type": "true_or_false", "is_required": false, "is_active": true, "is_index": true, "default_value": "false", "is_unique": false, "define_type": "package", "status": "released", "is_extend": false}, "default_score": {"api_name": "default_score", "label": "默认特征分", "type": "number", "is_required": false, "is_active": true, "is_index": true, "is_unique": false, "define_type": "package", "status": "released", "is_extend": false, "decimal_places": 2}, "owner": {"is_index": true, "is_active": true, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "released", "is_extend": false}, "lock_status": {"is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "help_text": "锁定状态", "status": "released", "is_extend": false}, "lock_rule": {"is_index": true, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "help_text": "锁定规则", "is_extend": false}, "lock_user": {"is_index": true, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "help_text": "加锁人", "is_extend": false}, "life_status": {"is_index": true, "is_active": true, "description": "生命状态", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "help_text": "生命状态", "status": "released", "is_extend": false}, "life_status_before_invalid": {"is_index": true, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "help_text": "作废前生命状态", "max_length": 256, "is_extend": false}, "owner_department": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "负责人所在部门", "type": "text", "default_to_zero": false, "is_need_convert": false, "is_required": false, "api_name": "owner_department", "define_type": "package", "is_single": true, "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "relevant_team": {"embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "help_text": "相关团队", "is_extend": false}, "record_type": {"is_index": false, "is_active": true, "description": "业务类型", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "help_text": "", "status": "released", "is_extend": false}, "created_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "创建人", "api_name": "created_by", "description": "创建人", "status": "released", "is_extend": false}, "last_modified_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "最后修改人", "api_name": "last_modified_by", "description": "最后修改人", "status": "released", "is_extend": false}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "api_name": "package", "description": "package", "status": "released", "is_extend": false}, "object_describe_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_id", "api_name": "object_describe_id", "description": "object_describe_id", "status": "released", "is_extend": false}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "is_extend": false}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released", "is_extend": false}, "create_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "创建时间", "status": "released", "is_extend": false}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "最后修改时间", "status": "released", "is_extend": false}, "extend_obj_data_id": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "default_value": "", "type": "text", "label": "extend_obj_data_id", "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}}, "validate_rules": {}, "triggers": {}, "actions": {}, "index_version": 1, "api_name": "FeatureObj", "display_name": "特征", "package": "CRM", "define_type": "package", "is_active": true, "store_table_name": "biz_feature", "is_deleted": false}