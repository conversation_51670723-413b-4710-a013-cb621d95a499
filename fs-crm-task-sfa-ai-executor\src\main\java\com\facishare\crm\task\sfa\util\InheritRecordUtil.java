package com.facishare.crm.task.sfa.util;


import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.utils.Pair;
import com.facishare.crm.sfa.lto.utils.StringUtil;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/07/20 17:44
 * @Version 1.0
 **/
public class InheritRecordUtil {
  private static final String MERGE_USER_ID_TAG = "@";
  public static void fillUserSet(Set<String> userSet, String handover, String takeover){
    String merge = mergeUserId(handover , takeover);
    if(!userSet.contains(merge)){
      userSet.add(merge);
    }
  }

  public static String mergeUserId(String handover, String takeover){
    StringBuilder sb = new StringBuilder();
    sb.append(handover).append(MERGE_USER_ID_TAG).append(takeover);
    return sb.toString();
  }

  public static Pair<String, String> parserMergeUserId(String mergeId){
    if(mergeId.contains(MERGE_USER_ID_TAG)){
      List<String> line = StringUtil.split(mergeId, MERGE_USER_ID_TAG);
      return Pair.build(line.get(0), line.get(1));
    }else{
      return Pair.build(mergeId, mergeId);
    }
  }

  public static String getStringValue(IObjectData objectData, String key, String defaultValue) {
    if (objectData == null || StringUtils.isEmpty(key)) {
      return defaultValue;
    }
    Object tempValue = objectData.get(key);
    if (tempValue != null) {
      return tempValue.toString();
    }
    return defaultValue;
  }

  public static Long getLongValue(IObjectData objectData, String key, Long defaultValue) {
    if (objectData == null || StringUtils.isEmpty(key)) {
      return defaultValue;
    }
    Object tempValue = objectData.get(key);
    if (tempValue != null) {
      try {
        return Long.valueOf(tempValue.toString());
      } catch (Exception e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  public static Integer getIntegerValue(IObjectData objectData, String key, Integer defaultValue) {
    if (objectData == null || StringUtils.isEmpty(key)) {
      return defaultValue;
    }
    Object tempValue = objectData.get(key);
    if (tempValue != null) {
      try {
        return Integer.valueOf(tempValue.toString());
      } catch (Exception e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  public static List<String> getListValue(IObjectData objectData, String key, List<String> defaultValue) {
    Object value = objectData.get(key);
    if (null == value) {
      return defaultValue;
    } else {
      String str;
      if (value instanceof String) {
        str = (String) value;
      } else {
        str = JSON.toJSONString(value);
      }
      return JSON.parseObject(str, List.class);
    }
  }

  public static String getStringValue(Map objectData, String key, String defaultValue) {
    if (objectData == null || StringUtils.isEmpty(key)) {
      return defaultValue;
    }
    Object tempValue = objectData.get(key);
    if (tempValue != null) {
      return tempValue.toString();
    }
    return defaultValue;
  }

  public static Long getLongValue(Map objectData, String key, Long defaultValue) {
    if (objectData == null || StringUtils.isEmpty(key)) {
      return defaultValue;
    }
    Object tempValue = objectData.get(key);
    if (tempValue != null) {
      try {
        return Long.valueOf(tempValue.toString());
      } catch (Exception e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  public static Integer getIntegerValue(Map objectData, String key, Integer defaultValue) {
    if (objectData == null || StringUtils.isEmpty(key)) {
      return defaultValue;
    }
    Object tempValue = objectData.get(key);
    if (tempValue != null) {
      try {
        return Integer.valueOf(tempValue.toString());
      } catch (Exception e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  public static List<String> getListValue(Map objectData, String key, List<String> defaultValue) {
    Object value = objectData.get(key);
    if (null == value) {
      return defaultValue;
    } else {
      String str;
      if (value instanceof String) {
        str = (String) value;
      } else {
        str = JSON.toJSONString(value);
      }
      return JSON.parseObject(str, List.class);
    }
  }
  public static boolean getBooleanValue(IObjectData objectData, String key, boolean defaultValue) {
    if (objectData == null || StringUtils.isEmpty(key)) {
      return defaultValue;
    }
    Object tempValue = objectData.get(key);
    if (tempValue != null) {
      try {
        return Boolean.parseBoolean(tempValue.toString());
      } catch (Exception e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

}
