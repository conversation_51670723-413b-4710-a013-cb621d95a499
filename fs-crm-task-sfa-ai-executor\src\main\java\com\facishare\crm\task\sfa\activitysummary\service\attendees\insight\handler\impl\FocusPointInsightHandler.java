package com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.impl;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.lto.activity.enums.AttendeesInsightType;
import com.facishare.crm.task.sfa.activitysummary.model.AttendeesInsightModel;
import com.facishare.crm.task.sfa.activitysummary.service.attendees.insight.handler.AbstractInsightHandler;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.activitysummary.constant.AttendeesInsightConstants.*;

@Component
public class FocusPointInsightHandler extends AbstractInsightHandler<AttendeesInsightModel.InsightResult> {

    private static final String PROMPT = "prompt_attendee_insight_focus_point";

    @Override
    public String getInsightType() {
        return AttendeesInsightType.FOCUS_POINT;
    }

    @Override
    protected String getUserNames(AttendeesInsightModel.AttendeesInsightMessage insightMessage) {
        return super.getTheirSideNames(insightMessage);
    }


    @Override
    public void doInsight(AttendeesInsightModel.AttendeesInsightMessage attendeesInsightMessage) {
        List<IObjectData> dataList = super.insightByOriginCorpus(attendeesInsightMessage, PROMPT);


        if (Safes.isNotEmpty(dataList)) {
            Map<String, IObjectData> userFocusList = dataList.stream().collect(Collectors.toMap(d -> d.get(ACTIVITY_USER_ID, String.class), Function.identity(), (a, b) -> a));
            List<IObjectData> activityUserList = attendeesInsightMessage.getExtendData().getActivityUserList();
            List<IObjectData> updateList = Lists.newArrayList();
            for (IObjectData activityUser : Safes.of(activityUserList)) {
                if (userFocusList.containsKey(activityUser.getId())) {
                    activityUser.set("has_focus_point", true);
                    updateList.add(activityUser);
                }
            }
            if (!updateList.isEmpty()) {
                serviceFacade.batchUpdateByFields(User.systemUser(attendeesInsightMessage.getTenantId()), dataList, Lists.newArrayList("has_focus_point"));
            }
        }
    }

    @Override
    protected void fillDataByOriginRst(IObjectData insightRecord, AttendeesInsightModel.InsightResult insightResult) {
        super.fillDataByOriginRst(insightRecord, insightResult);
        
        for (AttendeesInsightModel.Insight insight : insightResult.getInsightList()) {
            if (FOCUS_BUDGET.equals(insight.getInsightType())) {
                insightRecord.set(FOCUS_BUDGET, insight.getInsightText());
            }
            if (FOCUS_DECISION_MAKER.equals(insight.getInsightType())) {
                insightRecord.set(FOCUS_DECISION_MAKER, insight.getInsightText());
            }
            if (FOCUS_REQUIREMENT.equals(insight.getInsightType())) {
                insightRecord.set(FOCUS_REQUIREMENT, insight.getInsightText());
            }
            if (FOCUS_TIME.equals(insight.getInsightType())) {
                insightRecord.set(FOCUS_TIME, insight.getInsightText());
            }
        }
    }
        
}
