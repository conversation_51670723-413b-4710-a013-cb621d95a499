package com.facishare.crm.task.sfa.bizfeature.service;

import com.facishare.crm.task.sfa.bizfeature.constant.*;
import com.facishare.crm.task.sfa.bizfeature.model.FeatureNode;
import com.facishare.crm.task.sfa.bizfeature.util.NodeFinishUtil;
import com.facishare.crm.task.sfa.util.SearchTemplateQueryPlus;
import com.facishare.crm.task.sfa.util.SearchUtil;

import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FeatureNodeFinishService {
    static double NODE_FINISH_SCORE = 0.8;
    static double TASK_FINISH_SCORE = 0.6;
    static BigDecimal standardScore = BigDecimal.valueOf(6);

    @Resource
    private ServiceFacade serviceFacade;

    public void finish(FeatureNode featureNode) {
        List<IObjectData> featureScoreList = serviceFacade.findObjectDataByIdsIgnoreAll(featureNode.getTenantId(),
                featureNode.getObjectIds(), FeatureConstants.FEATURE_SCORE);
        if (CollectionUtils.empty(featureScoreList)) {
            log.warn("featureScoreList is empty");
            return;
        }
        User user = User.systemUser(featureNode.getTenantId());
        List<IObjectData> mtdinkList = Lists.newArrayList();
        List<IObjectData> nodeInstanceList = Lists.newArrayList();
        List<IObjectData> taskInstanceList = getTaskInstanceList(user, mtdinkList, nodeInstanceList, featureScoreList);
        if (CollectionUtils.empty(mtdinkList) || (CollectionUtils.empty(taskInstanceList) && CollectionUtils.empty(nodeInstanceList))) {
            log.warn("getMethodologyInstance is empty");
            return;
        }

        Set<String> objectIds = getFeatureObjectIds(featureScoreList, mtdinkList);

        Set<String> taskIdList = taskInstanceList.stream()
                .map(x -> x.get(TaskFeatureConstants.TASK_ID, String.class))
                .collect(Collectors.toSet());

        SearchTemplateQueryPlus featureTaskSearchQuery = SearchUtil.buildBaseSearchQuery();
        featureTaskSearchQuery.addFilter(TaskFeatureConstants.TASK_ID, Operator.IN, Lists.newArrayList(taskIdList));
        featureTaskSearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");
        List<IObjectData> taskFeatureList = serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.TASK_FEATURE, featureTaskSearchQuery).getData();

        List<String> allFeatureIds = taskFeatureList.stream()
                .map(x -> x.get(TaskFeatureConstants.FEATURE_ID, String.class))
                .collect(Collectors.toList());

        Map<String, IObjectData> featureScoreMap = getFeatureScoreMap(user, Lists.newArrayList(objectIds),
                allFeatureIds);

        Map<String, List<IObjectData>> taskFeatureMap = taskFeatureList.stream()
                .collect(Collectors.groupingBy(x -> x.get(TaskFeatureConstants.TASK_ID, String.class)));

        List<IObjectData> finishTask = calcFinishTask(taskInstanceList, featureScoreMap, taskFeatureMap);

        if (CollectionUtils.notEmpty(finishTask)) {
            // 任务完成
            serviceFacade.batchUpdateByFields(user, finishTask,
                    Lists.newArrayList(TaskFeatureConstants.STATUS));

            finishNode(user, finishTask);
        }
    }

    @NotNull
    private Set<String> getFeatureObjectIds(List<IObjectData> featureScoreList, List<IObjectData> mtdinkList) {
        Set<String> objectIds = featureScoreList.stream().map(x -> x.get(FeatureScoreConstants.OBJECT_ID, String.class)).collect(Collectors.toSet());
        for (IObjectData objectData : mtdinkList) {
            String accountId = objectData.get(MethodologyInstanceConstants.ACCOUNT_ID, String.class);
            if (StringUtils.isNotBlank(accountId)) {
                objectIds.add(accountId);
            }
            String lead = objectData.get(MethodologyInstanceConstants.LEAD_ID, String.class);
            if (StringUtils.isNotBlank(lead)) {
                objectIds.add(lead);
            }
            String opportunityId = objectData.get(MethodologyInstanceConstants.OPPORTUNITY_ID, String.class);
            if (StringUtils.isNotBlank(opportunityId)) {
                objectIds.add(opportunityId);
            }
        }
        return objectIds;
    }

    private void finishNode(User user, List<IObjectData> finishTask) {
        List<String> nodeInstanceIds = finishTask.stream()
                .map(x -> x.get(TaskInstanceConstants.NODE_INSTANCE_ID, String.class))
                .collect(Collectors.toList());

        SearchTemplateQueryPlus nodeInstanceSearchQuery = SearchUtil.buildBaseSearchQuery();
        nodeInstanceSearchQuery.addFilter(DBRecord.ID, Operator.IN, nodeInstanceIds);
        nodeInstanceSearchQuery.addFilter(NodeInstanceConstants.STATUS, Operator.NEQ,
                NodeInstanceConstants.StatusType.COMPLETED.getStatusType());
        nodeInstanceSearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");

        List<IObjectData> nodeInstanceList = serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.NODE_INSTANCE, nodeInstanceSearchQuery).getData();

        if (CollectionUtils.empty(nodeInstanceList)) {
            log.warn("nodeInstanceList is empty");
            return;
        }

        nodeInstanceIds = nodeInstanceList.stream()
                .map(DBRecord::getId)
                .collect(Collectors.toList());
        SearchTemplateQueryPlus taskInstanceSearchQuery = SearchUtil.buildBaseSearchQuery();
        taskInstanceSearchQuery.addFilter(TaskInstanceConstants.NODE_INSTANCE_ID, Operator.IN, nodeInstanceIds);
        taskInstanceSearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");

        List<IObjectData> allTaskInstanceList = serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.TASK_INSTANCE, taskInstanceSearchQuery).getData();

        Map<String, List<IObjectData>> taskInstanceMap = allTaskInstanceList.stream()
                .collect(Collectors.groupingBy(x -> x.get(TaskInstanceConstants.NODE_INSTANCE_ID, String.class)));

        List<IObjectData> finishNodeInstance = new ArrayList<>();
        for (IObjectData nodeIObjectData : nodeInstanceList) {
            String nodeInstanceId = nodeIObjectData.getId();
            List<IObjectData> taskInstanceList = taskInstanceMap.get(nodeInstanceId);
            if (CollectionUtils.empty(taskInstanceList)) {
                continue;
            }
            int count = 0;
            for (IObjectData taskInstance : taskInstanceList) {
                if (taskInstance.get(TaskInstanceConstants.STATUS, String.class).equals(
                        TaskInstanceConstants.StatusType.COMPLETED.getStatusType())) {
                    count++;
                }
            }
            if ((double) count / taskInstanceList.size() >= NODE_FINISH_SCORE) {
                finishNodeInstance.add(nodeIObjectData);
                nodeIObjectData.set(NodeInstanceConstants.STATUS,
                        NodeInstanceConstants.StatusType.COMPLETED.getStatusType());
            }
        }

        if (CollectionUtils.notEmpty(finishNodeInstance)) {
            // 节点完成
            serviceFacade.batchUpdateByFields(user, finishNodeInstance,
                    Lists.newArrayList(NodeInstanceConstants.STATUS));
        }
    }

    @NotNull
    private List<IObjectData> calcFinishTask(List<IObjectData> taskInstanceList,
                                             Map<String, IObjectData> featureScoreMap, Map<String, List<IObjectData>> taskFeatureMap) {

        List<IObjectData> finishTask = new ArrayList<>();
        for (IObjectData taskInstance : taskInstanceList) {
            String taskId = taskInstance.get(TaskFeatureConstants.TASK_ID, String.class);
            List<IObjectData> featureList = taskFeatureMap.get(taskId);
            if (NodeFinishUtil.finishNode(NodeFinishUtil.TASK_FINISH_SCORE, featureList, featureScoreMap)) {
                // 任务完成
                taskInstance.set(TaskInstanceConstants.STATUS,
                        TaskInstanceConstants.StatusType.COMPLETED.getStatusType());
                finishTask.add(taskInstance);
            }
        }
        return finishTask;
    }

    @NotNull
    private Map<String, IObjectData> getFeatureScoreMap(User user, List<String> featureObjectIds,
                                                        List<String> allFeatureIds) {
        SearchTemplateQueryPlus featureScoreSearchQuery = SearchUtil.buildBaseSearchQuery();
        featureScoreSearchQuery.addFilter(FeatureScoreConstants.FEATURE_ID, Operator.IN, allFeatureIds);
        featureScoreSearchQuery.addFilter(FeatureScoreConstants.OBJECT_ID, Operator.IN, featureObjectIds);
        featureScoreSearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");

        List<IObjectData> allFeatureScoreList = serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.FEATURE_SCORE, featureScoreSearchQuery).getData();

        return allFeatureScoreList.stream()
                .collect(Collectors.toMap(x -> x.get(FeatureScoreConstants.FEATURE_ID, String.class),
                        Function.identity(), (existing, replacement) -> existing));
    }

    private List<IObjectData> getTaskInstanceList(User user, List<IObjectData> methodology, List<IObjectData> nodeInstanceList,
                                                  List<IObjectData> featureScoreList) {
        Set<String> featureIds = featureScoreList.stream()
                .map(x -> x.get(FeatureScoreConstants.FEATURE_ID, String.class))
                .collect(Collectors.toSet());

        SearchTemplateQueryPlus instanceFeatureSearchQuery = SearchUtil.buildBaseSearchQuery();
        instanceFeatureSearchQuery.addFilter(TaskFeatureConstants.FEATURE_ID, Operator.IN, Lists.newArrayList(featureIds));
        instanceFeatureSearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");

        List<IObjectData> instanceFeatureList = serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.INSTANCE_FEATURE, instanceFeatureSearchQuery).getData();
        if (CollectionUtils.notEmpty(instanceFeatureList)) {
            return Lists.newArrayList();
        }

        //Set<String> taskIds = Sets.newHashSet();
        Set<String> taskInsIds = Sets.newHashSet();
        Set<String> methodologyInstanceIds = Sets.newHashSet();
        Set<String> profileNodeIds = Sets.newHashSet();
        //Map<String, String> featureField = new HashMap<>();
        for (IObjectData nodeInstance : nodeInstanceList) {
            methodologyInstanceIds.add(nodeInstance.get(InstanceFeatureConstants.METHODOLOGY_INSTANCE_ID, String.class));
            String taskId = nodeInstance.get(InstanceFeatureConstants.TASK_ID, String.class);
            String nodeId = nodeInstance.get(InstanceFeatureConstants.NODE_ID, String.class);
            if (StringUtils.isNotBlank(taskId)) {
                //taskIds.add(taskId);
                taskInsIds.add(nodeInstance.get(InstanceFeatureConstants.TASK_INSTANCE_ID, String.class));
            } else {
                profileNodeIds.add(nodeId);
                profileNodeIds.add(nodeInstance.get(InstanceFeatureConstants.NODE_INSTANCE_ID, String.class));
            }

//            String relatedField = nodeInstance.get(InstanceFeatureConstants.RELATED_FIELD, String.class);
//            if (StringUtils.isNotBlank(relatedField)) {
//                featureField.put(relatedField,
//                        nodeInstance.get(InstanceFeatureConstants.FEATURE_OBJECT_API_NAME, String.class));
//            }

        }

        if (CollectionUtils.notEmpty(profileNodeIds)) {
            nodeInstanceList.addAll(this.queryNodeInstance(user, Lists.newArrayList(profileNodeIds)));
        }

//
//        SearchTemplateQueryPlus featureTaskSearchQuery = SearchUtil.buildBaseSearchQuery();
//        featureTaskSearchQuery.addFilter(TaskFeatureConstants.FEATURE_ID, Operator.IN, Lists.newArrayList(featureIds));
//        featureTaskSearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");
//        featureTaskSearchQuery.addFilter(TaskFeatureConstants.STATUS, Operator.EQ,
//                TaskFeatureConstants.StatusType.ENABLE.getStatusType());
//
//        List<IObjectData> featureTaskList = serviceFacade.findBySearchQueryIgnoreAll(user,
//                FeatureConstants.TASK_FEATURE, featureTaskSearchQuery).getData();

        List<String> methodologyInstanceList = Lists.newArrayList(methodologyInstanceIds);

        List<IObjectData> miniList1 = getMethodologyInstance(methodologyInstanceList, user);
        if (CollectionUtils.notEmpty(miniList1)) {
            methodology.addAll(miniList1);
        }

        return queryTaskInstance(user, taskInsIds);
    }

    private List<IObjectData> queryTaskInstance(User user, Set<String> taskInsIds) {
        SearchTemplateQueryPlus taskInstanceSearchQuery = SearchUtil.buildBaseSearchQuery();
        taskInstanceSearchQuery.addFilter(DBRecord.ID, Operator.IN, Lists.newArrayList(taskInsIds));
        taskInstanceSearchQuery.addFilter(TaskInstanceConstants.STATUS, Operator.NEQ,
                TaskInstanceConstants.StatusType.COMPLETED.getStatusType());
        taskInstanceSearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");

        return serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.TASK_INSTANCE, taskInstanceSearchQuery).getData();
    }

    private List<IObjectData> queryNodeInstance(User user, List<String> nodeInsIds) {
        SearchTemplateQueryPlus taskInstanceSearchQuery = SearchUtil.buildBaseSearchQuery();
        taskInstanceSearchQuery.addFilter(DBRecord.ID, Operator.IN, nodeInsIds);
        taskInstanceSearchQuery.addFilter(TaskInstanceConstants.STATUS, Operator.NEQ,
                TaskInstanceConstants.StatusType.COMPLETED.getStatusType());
        taskInstanceSearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");

        return serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.NODE_INSTANCE, taskInstanceSearchQuery).getData();
    }

    @Nullable
    private List<IObjectData> getMethodologyInstance(List<String> ids, User user) {

        SearchTemplateQueryPlus methodologySearchQuery = SearchUtil.buildBaseSearchQuery();

        methodologySearchQuery.addFilter(DBRecord.IS_DELETED, Operator.EQ, "0");
        methodologySearchQuery.addFilter(DBRecord.ID, Operator.IN, ids);
        methodologySearchQuery.addFilter(MethodologyInstanceConstants.STATUS, Operator.EQ, MethodologyInstanceConstants.StatusType.ENABLE.getStatusType());
        return serviceFacade.findBySearchQueryIgnoreAll(user,
                FeatureConstants.METHODOLOGY_INSTANCE, methodologySearchQuery).getData();
    }

    @Nullable
    private IFilter getIFilter(String objectApiName, List<String> objectId) {
        IFilter filter;
        Operator operator = Operator.EQ;
        if (objectId.size() > 1) {
            operator = Operator.IN;
        }
        if (objectApiName.equals(FeatureConstants.ACCOUNT_OBJ)) {
            filter = SearchTemplateQueryPlus.getFilter(MethodologyInstanceConstants.ACCOUNT_ID, operator,
                    objectId);
        } else if (objectApiName.equals(FeatureConstants.LEADS_OBJ)) {
            filter = SearchTemplateQueryPlus.getFilter(MethodologyInstanceConstants.LEAD_ID, operator,
                    objectId);
        } else if (objectApiName.equals(FeatureConstants.NEW_OPPORTUNITY_OBJ)) {
            filter = SearchTemplateQueryPlus.getFilter(MethodologyInstanceConstants.OPPORTUNITY_ID, operator,
                    objectId);
        } else {
            log.error("objectApiName is not supported:{}", objectApiName);
            return null;
        }
        return filter;
    }
}
