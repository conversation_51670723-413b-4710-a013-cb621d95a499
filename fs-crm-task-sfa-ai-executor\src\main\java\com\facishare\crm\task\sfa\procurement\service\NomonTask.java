package com.facishare.crm.task.sfa.procurement.service;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.task.sfa.activitysummary.model.ActivityTaskMessage;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/2 12:01
 */

@Component
public class NomonTask {


    @Autowired
    private NomonProducer nomonProducer;

    public void createOrUpdateTask(String biz, String tenantId, String dataId, Date executeTime, String callArg) {
        send(biz, tenantId, dataId, executeTime, callArg, null);
    }


    private void send(String biz, String tenantId, String dataId, Date executeTime, String callArg, Integer callQueueMod) {
        NomonMessage message = NomonMessage
                .builder()
                .biz(biz)
                .tenantId(tenantId)
                .dataId(dataId)
                .executeTime(executeTime)
                .callArg(String.format(callArg, tenantId, dataId))
                .callQueueMod(callQueueMod)
                .build();
        nomonProducer.send(message);
    }

    /**
     * 发送 activity-text-task 任务
     * 
     * 
     */

    public void sendActivityTextTask(ActivityTaskMessage.Rec2TextTask rec2TextTask) {
        Date executeTime = new Date(System.currentTimeMillis() + 2 * 60 * 1000); // 2分钟后执行
        NomonMessage message = NomonMessage.builder()
                .biz("activity-text-task")
                .tenantId(rec2TextTask.getTenantId())
                .dataId(rec2TextTask.getObjectId())
                .executeTime(executeTime)
                .callArg(JSON.toJSONString(rec2TextTask))
                .build();
        nomonProducer.send(message);
    }
}
